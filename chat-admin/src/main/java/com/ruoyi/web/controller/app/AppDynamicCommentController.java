package com.ruoyi.web.controller.app;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ruoyi.app.domain.bo.AppDynamicCommentBo;
import com.ruoyi.app.domain.vo.AppDynamicCommentVo;
import com.ruoyi.app.service.IAppDynamicCommentService;
import com.ruoyi.app.service.IAppUserService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import lombok.RequiredArgsConstructor;
import org.redisson.api.RList;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * APP用户动态评论
 *
 * <AUTHOR>
 * @date 2022-12-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/dynamicComment")
public class AppDynamicCommentController extends BaseController {

    private final IAppDynamicCommentService iAppDynamicCommentService;
    private final IAppUserService iAppUserService;

    /**
     * 查询APP用户动态评论列表
     */
    @GetMapping("/page")
    public TableDataInfo<AppDynamicCommentVo> page(AppDynamicCommentBo bo, PageQuery pageQuery) {
        TableDataInfo<AppDynamicCommentVo> page = iAppDynamicCommentService.queryPageList(bo, pageQuery);
        page.getData().forEach(e -> e.setUserVo(iAppUserService.queryById(e.getUserId())));
        return page;
    }


    /**
     * 查询APP用户动态评论列表
     */
    @GetMapping("/list")
    public R<List<AppDynamicCommentVo>> list(AppDynamicCommentBo bo) {
        return R.ok(iAppDynamicCommentService.queryList(bo));
    }

    /**
     * 导出APP用户动态评论列表
     */
    @Log(title = "APP用户动态评论", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AppDynamicCommentBo bo, HttpServletResponse response) {
        List<AppDynamicCommentVo> list = iAppDynamicCommentService.queryList(bo);
        ExcelUtil.exportExcel(list, "APP用户动态评论", AppDynamicCommentVo.class, response);
    }

    /**
     * 获取APP用户动态评论详细信息
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    public R<AppDynamicCommentVo> getInfo(@NotNull(message = "主键不能为空")
                                          @PathVariable Long id) {
        return R.ok(iAppDynamicCommentService.queryById(id));
    }

    /**
     * 新增APP用户动态评论
     */
    @Log(title = "APP用户动态评论", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AppDynamicCommentBo bo) {
        return toAjax(iAppDynamicCommentService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改APP用户动态评论
     */
    @Log(title = "APP用户动态评论", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@RequestBody AppDynamicCommentBo bo) {
        return toAjax(iAppDynamicCommentService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除APP用户动态评论
     *
     * @param ids 主键串
     */
    @Log(title = "APP用户动态评论", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iAppDynamicCommentService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
