package com.ruoyi.web.controller.app;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ruoyi.app.domain.bo.AppVipOrderBo;
import com.ruoyi.app.domain.vo.AppVipOrderVo;
import com.ruoyi.app.service.IAppUserService;
import com.ruoyi.app.service.IAppVipOrderService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * APP会员VIP订单
 *
 * <AUTHOR>
 * @date 2023-03-17
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/vipOrder")
public class AppVipOrderController extends BaseController {

    private final IAppUserService appUserService;
    private final IAppVipOrderService iAppVipOrderService;


    /**
     * 查询APPv会员VIP订单列表
     */
    @SaCheckPermission("system:vipOrder:list")
    @GetMapping("/list")
    public TableDataInfo<AppVipOrderVo> list(AppVipOrderBo bo, PageQuery pageQuery) {
        TableDataInfo<AppVipOrderVo> page = iAppVipOrderService.queryPageList(bo, pageQuery);
        page.getData().forEach(e-> e.setNickName(appUserService.queryById(e.getUserId()).getNickName()));
        return page;
    }

    /**
     * 导出APPv会员VIP订单列表
     */
    @SaCheckPermission("system:vipOrder:export")
    @Log(title = "APPv会员VIP订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AppVipOrderBo bo, HttpServletResponse response) {
        List<AppVipOrderVo> list = iAppVipOrderService.queryList(bo);
        ExcelUtil.exportExcel(list, "APP会员VIP订单", AppVipOrderVo.class, response);
    }

    /**
     * 获取APPv会员VIP订单详细信息
     *
     *
     *
     * @param id 主键
     */
    @SaCheckPermission("system:vipOrder:query")
    @GetMapping("/{id}")
    public R<AppVipOrderVo> getInfo(@NotNull(message = "主键不能为空")
                                    @PathVariable Long id) {
        return R.ok(iAppVipOrderService.queryById(id));
    }

    /**
     * 新增APPv会员VIP订单
     */
    @SaCheckPermission("system:vipOrder:add")
    @Log(title = "APPv会员VIP订单", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AppVipOrderBo bo) {
        return toAjax(iAppVipOrderService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改APPv会员VIP订单
     */
    @SaCheckPermission("app:vipOrder:edit")
    @Log(title = "APPv会员VIP订单", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AppVipOrderBo bo) {
        return toAjax(iAppVipOrderService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除APPv会员VIP订单
     *
     * @param ids 主键串
     */
    @SaCheckPermission("app:vipOrder:remove")
    @Log(title = "APPv会员VIP订单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iAppVipOrderService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
