package com.ruoyi.web.controller.app;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ruoyi.app.domain.bo.AppGiftOrderBo;
import com.ruoyi.app.domain.vo.AppGiftOrderVo;
import com.ruoyi.app.domain.vo.AppGiftVo;
import com.ruoyi.app.domain.vo.UserFilterVo;
import com.ruoyi.app.service.IAppGiftOrderService;
import com.ruoyi.app.service.IAppGiftService;
import com.ruoyi.app.service.IAppUserService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * APP礼物记录
 *
 * <AUTHOR>
 * @date 2025-12-21
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/giftOrder")
public class AppGiftOrderController extends BaseController {


    private final IAppUserService appUserService;
    private final IAppGiftService appGiftService;
    private final IAppGiftOrderService iAppGiftOrderService;

    /**
     * 查询APP礼物记录列表
     */
    @SaCheckPermission("system:giftOrder:list")
    @GetMapping("/list")
    public TableDataInfo<AppGiftOrderVo> list(AppGiftOrderBo bo, PageQuery pageQuery) {
        TableDataInfo<AppGiftOrderVo> page = iAppGiftOrderService.queryPageList(bo, pageQuery);
        page.getData().forEach(e -> {
            UserFilterVo userFilterVo = appUserService.userFilterVo(e.getUserId());
            UserFilterVo toUserFilterVo = appUserService.userFilterVo(e.getToUserId());
            e.setUserName(userFilterVo.getNickName());
            e.setToUserName(toUserFilterVo.getNickName());
            AppGiftVo appGiftVo = appGiftService.queryById(e.getGiftId());
            e.setImage(appGiftVo.getImage());
        });
        return page;
    }

    /**
     * 导出APP礼物记录列表
     */
    @SaCheckPermission("system:giftOrder:export")
    @Log(title = "APP礼物记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AppGiftOrderBo bo, HttpServletResponse response) {
        List<AppGiftOrderVo> list = iAppGiftOrderService.queryList(bo);
        ExcelUtil.exportExcel(list, "APP礼物记录", AppGiftOrderVo.class, response);
    }

    /**
     * 获取APP礼物记录详细信息
     *
     * @param id 主键
     **/
    @SaCheckPermission("system:giftOrder:query")
    @GetMapping("/{id}")
    public R<AppGiftOrderVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iAppGiftOrderService.queryById(id));
    }

    /**
     * 新增APP礼物记录
     */
    @SaCheckPermission("system:giftOrder:add")
    @Log(title = "APP礼物记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AppGiftOrderBo bo) {
        return toAjax(iAppGiftOrderService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改APP礼物记录
     */
    @SaCheckPermission("system:giftOrder:edit")
    @Log(title = "APP礼物记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AppGiftOrderBo bo) {
        return toAjax(iAppGiftOrderService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除APP礼物记录
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:giftOrder:remove")
    @Log(title = "APP礼物记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iAppGiftOrderService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
