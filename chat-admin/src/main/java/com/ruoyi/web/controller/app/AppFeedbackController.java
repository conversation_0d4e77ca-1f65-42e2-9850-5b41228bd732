package com.ruoyi.web.controller.app;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ruoyi.app.domain.bo.AppFeedbackBo;
import com.ruoyi.app.domain.vo.AppFeedbackVo;
import com.ruoyi.app.service.IAppFeedbackService;
import com.ruoyi.app.service.IAppUserService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.validate.EditGroup;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * APP用户意见反馈
 *
 * <AUTHOR>
 * @date 2023-02-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/feedback")
public class AppFeedbackController extends BaseController {

    private final IAppFeedbackService iAppFeedbackService;

    /**
     * 查询APP用户意见反馈列表
     */
    @SaCheckPermission("system:feedback:list")
    @GetMapping("/list")
    public TableDataInfo<AppFeedbackVo> list(AppFeedbackBo bo, PageQuery pageQuery) {
        return iAppFeedbackService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出APP用户意见反馈列表
     */
    @SaCheckPermission("system:feedback:export")
    @Log(title = "APP用户意见反馈", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AppFeedbackBo bo, HttpServletResponse response) {
        List<AppFeedbackVo> list = iAppFeedbackService.queryList(bo);
        ExcelUtil.exportExcel(list, "APP用户意见反馈", AppFeedbackVo.class, response);
    }

    /**
     * 获取APP用户意见反馈详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:feedback:query")
    @GetMapping("/{id}")
    public R<AppFeedbackVo> getInfo(@NotNull(message = "主键不能为空")
                                    @PathVariable Long id) {
        return R.ok(iAppFeedbackService.queryById(id));
    }

    /**
     * 修改APP用户意见反馈
     */
    @SaCheckPermission("system:feedback:edit")
    @Log(title = "APP用户意见反馈", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AppFeedbackBo bo) {
        return toAjax(iAppFeedbackService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除APP用户意见反馈
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:feedback:remove")
    @Log(title = "APP用户意见反馈", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iAppFeedbackService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
