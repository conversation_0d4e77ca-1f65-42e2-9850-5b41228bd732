package com.ruoyi.web.controller.app;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ruoyi.app.domain.bo.AppGiftBo;
import com.ruoyi.app.domain.vo.AppGiftVo;
import com.ruoyi.app.service.IAppGiftService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * APP礼物
 *
 * <AUTHOR>
 * @date 2025-12-14
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/gift")
public class AppGiftController extends BaseController {

    private final IAppGiftService iAppGiftService;

    /**
     * 查询APP礼物列表
     */
    @SaCheckPermission("system:gift:list")
    @GetMapping("/list")
    public TableDataInfo<AppGiftVo> list(AppGiftBo bo, PageQuery pageQuery) {
        return iAppGiftService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出APP礼物列表
     */
    @SaCheckPermission("system:gift:export")
    @Log(title = "APP礼物", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AppGiftBo bo, HttpServletResponse response) {
        List<AppGiftVo> list = iAppGiftService.queryList(bo);
        ExcelUtil.exportExcel(list, "APP礼物", AppGiftVo.class, response);
    }

    /**
     * 获取APP礼物详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:gift:query")
    @GetMapping("/{id}")
    public R<AppGiftVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iAppGiftService.queryById(id));
    }

    /**
     * 新增APP礼物
     */
    @SaCheckPermission("system:gift:add")
    @Log(title = "APP礼物", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AppGiftBo bo) {
        return toAjax(iAppGiftService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改APP礼物
     */
    @SaCheckPermission("system:gift:edit")
    @Log(title = "APP礼物", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AppGiftBo bo) {
        return toAjax(iAppGiftService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除APP礼物
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:gift:remove")
    @Log(title = "APP礼物", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iAppGiftService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
