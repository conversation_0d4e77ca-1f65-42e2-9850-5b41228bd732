package com.ruoyi.web.controller.app;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ruoyi.app.domain.bo.AppLabelBo;
import com.ruoyi.app.domain.vo.AppLabelVo;
import com.ruoyi.app.service.IAppLabelService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * APP标签
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/label")
public class AppLabelController extends BaseController {

    private final IAppLabelService iAppLabelService;

    /**
     * 查询APP标签列表
     */
    @SaCheckPermission("system:label:list")
    @GetMapping("/list")
    public TableDataInfo<AppLabelVo> list(AppLabelBo bo, PageQuery pageQuery) {
        return iAppLabelService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出APP标签列表
     */
    @SaCheckPermission("system:label:export")
    @Log(title = "APP标签", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AppLabelBo bo, HttpServletResponse response) {
        List<AppLabelVo> list = iAppLabelService.queryList(bo);
        ExcelUtil.exportExcel(list, "APP标签", AppLabelVo.class, response);
    }

    /**
     * 获取APP标签详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:label:query")
    @GetMapping("/{id}")
    public R<AppLabelVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iAppLabelService.queryById(id));
    }

    /**
     * 新增APP标签
     */
    @SaCheckPermission("system:label:add")
    @Log(title = "APP标签", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AppLabelBo bo) {
        return toAjax(iAppLabelService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改APP标签
     */
    @SaCheckPermission("system:label:edit")
    @Log(title = "APP标签", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AppLabelBo bo) {
        return toAjax(iAppLabelService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除APP标签
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:label:remove")
    @Log(title = "APP标签", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iAppLabelService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
