package com.ruoyi.web.controller.app;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ruoyi.app.domain.bo.AppBannerBo;
import com.ruoyi.app.domain.vo.AppBannerVo;
import com.ruoyi.app.service.IAppBannerService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * APP商城的轮播图
 *
 * <AUTHOR>
 * @date 2025-04-20
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/banner")
public class AppBannerController extends BaseController {

    private final IAppBannerService iAppBannerService;

    /**
     * 查询APP商城的轮播图列表
     */
    @SaCheckPermission("system:banner:list")
    @GetMapping("/list")
    public TableDataInfo<AppBannerVo> list(AppBannerBo bo, PageQuery pageQuery) {
        return iAppBannerService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出APP商城的轮播图列表
     */
    @SaCheckPermission("system:banner:export")
    @Log(title = "APP商城的轮播图", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AppBannerBo bo, HttpServletResponse response) {
        List<AppBannerVo> list = iAppBannerService.queryList(bo);
        ExcelUtil.exportExcel(list, "APP商城的轮播图", AppBannerVo.class, response);
    }

    /**
     * 获取APP商城的轮播图详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:banner:query")
    @GetMapping("/{id}")
    public R<AppBannerVo> getInfo(@NotNull(message = "主键不能为空")
                                  @PathVariable Long id) {
        return R.ok(iAppBannerService.queryById(id));
    }

    /**
     * 新增APP商城的轮播图
     */
    @SaCheckPermission("system:banner:add")
    @Log(title = "APP商城的轮播图", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AppBannerBo bo) {
        return toAjax(iAppBannerService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改APP商城的轮播图
     */
    @SaCheckPermission("system:banner:edit")
    @Log(title = "APP商城的轮播图", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AppBannerBo bo) {
        return toAjax(iAppBannerService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除APP商城的轮播图
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:banner:remove")
    @Log(title = "APP商城的轮播图", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iAppBannerService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
