package com.ruoyi.web.controller.app;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ruoyi.app.domain.bo.AppAddressBo;
import com.ruoyi.app.domain.vo.AppAddressVo;
import com.ruoyi.app.service.IAppAddressService;
import com.ruoyi.app.service.IAppUserService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * APP用户地址信息
 *
 * <AUTHOR>
 * @date 2022-12-21
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/address")
public class AppAddressController extends BaseController {

    private final IAppAddressService iAppAddressService;
    private final IAppUserService iAppUserService;

    /**
     * 查询APP用户地址信息列表
     */
    @SaCheckPermission("system:address:list")
    @GetMapping("/list")
    public TableDataInfo<AppAddressVo> list(AppAddressBo bo, PageQuery pageQuery) {
        TableDataInfo<AppAddressVo> address = iAppAddressService.queryPageList(bo, pageQuery);
        address.getData().forEach(e -> e.setUserVo(iAppUserService.queryById(e.getUserId())));
        return address;
    }

    /**
     * 修改APP用户地址信息
     */
    @SaCheckPermission("system:address:edit")
    @Log(title = "APP用户地址信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AppAddressBo bo) {
        return toAjax(iAppAddressService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除APP用户地址信息
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:address:remove")
    @Log(title = "APP用户地址信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iAppAddressService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
