package com.ruoyi.web.controller.app;

import java.util.List;
import java.util.Arrays;
import java.util.concurrent.TimeUnit;

import lombok.RequiredArgsConstructor;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;

import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import com.ruoyi.common.core.validate.QueryGroup;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.app.domain.vo.AppImagesVo;
import com.ruoyi.app.domain.bo.AppImagesBo;
import com.ruoyi.app.service.IAppImagesService;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * APP用户静态资源
 *
 * <AUTHOR>
 * @date 2023-04-04
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/images")
public class AppImagesController extends BaseController {

    private final IAppImagesService iAppImagesService;

    /**
     * 查询APP用户静态资源列表
     */
    @SaCheckPermission("system:images:list")
    @GetMapping("/list")
    public TableDataInfo<AppImagesVo> list(AppImagesBo bo, PageQuery pageQuery) {
        return iAppImagesService.queryPageList(bo, pageQuery);
    }


    /**
     * 获取APP用户静态资源详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:images:query")
    @GetMapping("/{id}")
    public R<AppImagesVo> getInfo(@NotNull(message = "主键不能为空")
                                  @PathVariable Long id) {
        return R.ok(iAppImagesService.queryById(id));
    }

    /**
     * 新增APP用户静态资源
     */
    @SaCheckPermission("system:images:add")
    @Log(title = "APP用户静态资源", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AppImagesBo bo) {
        return toAjax(iAppImagesService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改APP用户静态资源
     */
    @SaCheckPermission("system:images:edit")
    @Log(title = "APP用户静态资源", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AppImagesBo bo) {
        return toAjax(iAppImagesService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除APP用户静态资源
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:images:remove")
    @Log(title = "APP用户静态资源", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iAppImagesService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
