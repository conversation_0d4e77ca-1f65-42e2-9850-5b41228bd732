package com.ruoyi.web.controller.app;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ruoyi.app.domain.bo.AppUserReportBo;
import com.ruoyi.app.domain.vo.AppUserReportVo;
import com.ruoyi.app.domain.vo.AppUserVo;
import com.ruoyi.app.service.IAppUserReportService;
import com.ruoyi.app.service.IAppUserService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.validate.EditGroup;
import com.ruoyi.common.enums.BusinessType;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;

/**
 * APP举报信息
 *
 * <AUTHOR>
 * @date 2025-01-03
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/userReport")
public class AppUserReportController extends BaseController {

    private final IAppUserService appUserService;
    private final IAppUserReportService iAppUserReportService;

    /**
     * 查询APP举报信息列表
     */
    @SaCheckPermission("system:userReport:list")
    @GetMapping("/list")
    public TableDataInfo<AppUserReportVo> list(AppUserReportBo bo, PageQuery pageQuery) {
        TableDataInfo<AppUserReportVo> page = iAppUserReportService.queryPageList(bo, pageQuery);
        page.getData().forEach(e->{
            AppUserVo userVo = appUserService.queryById(e.getReportId());
            e.setReportName(ObjectUtils.isNotEmpty(userVo) ? userVo.getNickName() : null);
        });
        return page;
    }

    /**
     * 获取APP举报信息详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:userReport:query")
    @GetMapping("/{id}")
    public R<AppUserReportVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iAppUserReportService.queryById(id));
    }

    /**
     * 修改APP举报信息
     */
    @SaCheckPermission("system:userReport:edit")
    @Log(title = "APP举报信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AppUserReportBo bo) {
        return toAjax(iAppUserReportService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除APP举报信息
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:userReport:remove")
    @Log(title = "APP举报信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iAppUserReportService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
