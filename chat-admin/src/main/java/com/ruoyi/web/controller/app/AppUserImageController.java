package com.ruoyi.web.controller.app;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ruoyi.app.domain.bo.AppUserImageBo;
import com.ruoyi.app.domain.vo.AppUserImageVo;
import com.ruoyi.app.service.IAppUserImageService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * APP用户照片
 *
 * <AUTHOR>
 * @date 2023-01-15
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/userImage")
public class AppUserImageController extends BaseController {

    private final IAppUserImageService iAppUserImageService;

    /**
     * 查询APP用户照片列表
     */
    @SaCheckPermission("system:userImage:list")
    @GetMapping("/list")
    public TableDataInfo<AppUserImageVo> list(AppUserImageBo bo, PageQuery pageQuery) {
        return iAppUserImageService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出APP用户照片列表
     */
    @SaCheckPermission("system:userImage:export")
    @Log(title = "APP用户照片", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AppUserImageBo bo, HttpServletResponse response) {
        List<AppUserImageVo> list = iAppUserImageService.queryList(bo);
        ExcelUtil.exportExcel(list, "APP用户照片", AppUserImageVo.class, response);
    }

    /**
     * 获取APP用户照片详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:userImage:query")
    @GetMapping("/{id}")
    public R<AppUserImageVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iAppUserImageService.queryById(id));
    }

    /**
     * 新增APP用户照片
     */
    @SaCheckPermission("system:userImage:add")
    @Log(title = "APP用户照片", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AppUserImageBo bo) {
        return toAjax(iAppUserImageService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改APP用户照片
     */
    @SaCheckPermission("system:userImage:edit")
    @Log(title = "APP用户照片", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AppUserImageBo bo) {
        return toAjax(iAppUserImageService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除APP用户照片
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:userImage:remove")
    @Log(title = "APP用户照片", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iAppUserImageService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
