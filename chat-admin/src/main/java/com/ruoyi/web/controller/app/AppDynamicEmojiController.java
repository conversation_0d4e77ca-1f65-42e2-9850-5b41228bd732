package com.ruoyi.web.controller.app;

import java.util.List;
import java.util.Arrays;
import java.util.concurrent.TimeUnit;

import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import com.ruoyi.common.core.validate.QueryGroup;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.app.domain.vo.AppDynamicEmojiVo;
import com.ruoyi.app.domain.bo.AppDynamicEmojiBo;
import com.ruoyi.app.service.IAppDynamicEmojiService;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 动态表情
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/dynamicEmoji")
public class AppDynamicEmojiController extends BaseController {

    private final IAppDynamicEmojiService iAppDynamicEmojiService;

    /**
     * 查询动态表情列表
     */
    @SaCheckPermission("system:dynamicEmoji:list")
    @GetMapping("/list")
    public TableDataInfo<AppDynamicEmojiVo> list(AppDynamicEmojiBo bo, PageQuery pageQuery) {
        return iAppDynamicEmojiService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出动态表情列表
     */
    @SaCheckPermission("system:dynamicEmoji:export")
    @Log(title = "动态表情", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AppDynamicEmojiBo bo, HttpServletResponse response) {
        List<AppDynamicEmojiVo> list = iAppDynamicEmojiService.queryList(bo);
        ExcelUtil.exportExcel(list, "动态表情", AppDynamicEmojiVo.class, response);
    }

    /**
     * 获取动态表情详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:dynamicEmoji:query")
    @GetMapping("/{id}")
    public R<AppDynamicEmojiVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iAppDynamicEmojiService.queryById(id));
    }

    /**
     * 新增动态表情
     */
    @SaCheckPermission("system:dynamicEmoji:add")
    @Log(title = "动态表情", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AppDynamicEmojiBo bo) {
        return toAjax(iAppDynamicEmojiService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改动态表情
     */
    @SaCheckPermission("system:dynamicEmoji:edit")
    @Log(title = "动态表情", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AppDynamicEmojiBo bo) {
        return toAjax(iAppDynamicEmojiService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除动态表情
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:dynamicEmoji:remove")
    @Log(title = "动态表情", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iAppDynamicEmojiService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
