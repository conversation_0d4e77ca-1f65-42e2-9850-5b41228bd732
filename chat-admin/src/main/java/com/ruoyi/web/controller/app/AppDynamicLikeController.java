package com.ruoyi.web.controller.app;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ruoyi.app.domain.bo.AppDynamicLikeBo;
import com.ruoyi.app.domain.vo.AppDynamicLikeVo;
import com.ruoyi.app.service.IAppDynamicLikeService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * APP动态点赞
 *
 * <AUTHOR>
 * @date 2025-12-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/dynamicLike")
public class AppDynamicLikeController extends BaseController {

    private final IAppDynamicLikeService iAppDynamicLikeService;

    /**
     * 查询APP动态点赞列表
     */
    @SaCheckPermission("system:dynamicLike:list")
    @GetMapping("/list")
    public TableDataInfo<AppDynamicLikeVo> list(AppDynamicLikeBo bo, PageQuery pageQuery) {
        return iAppDynamicLikeService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出APP动态点赞列表
     */
    @SaCheckPermission("system:dynamicLike:export")
    @Log(title = "APP动态点赞", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AppDynamicLikeBo bo, HttpServletResponse response) {
        List<AppDynamicLikeVo> list = iAppDynamicLikeService.queryList(bo);
        ExcelUtil.exportExcel(list, "APP动态点赞", AppDynamicLikeVo.class, response);
    }

    /**
     * 获取APP动态点赞详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:dynamicLike:query")
    @GetMapping("/{id}")
    public R<AppDynamicLikeVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iAppDynamicLikeService.queryById(id));
    }

    /**
     * 新增APP动态点赞
     */
    @SaCheckPermission("system:dynamicLike:add")
    @Log(title = "APP动态点赞", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AppDynamicLikeBo bo) {
        return toAjax(iAppDynamicLikeService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改APP动态点赞
     */
    @SaCheckPermission("system:dynamicLike:edit")
    @Log(title = "APP动态点赞", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AppDynamicLikeBo bo) {
        return toAjax(iAppDynamicLikeService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除APP动态点赞
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:dynamicLike:remove")
    @Log(title = "APP动态点赞", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iAppDynamicLikeService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
