package com.ruoyi.web.controller.app;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ruoyi.app.domain.bo.AppDynamicBo;
import com.ruoyi.app.domain.bo.AppDynamicCommentBo;
import com.ruoyi.app.domain.vo.AppDynamicCommentVo;
import com.ruoyi.app.domain.vo.AppDynamicLikeVo;
import com.ruoyi.app.domain.vo.AppDynamicVo;
import com.ruoyi.app.service.IAppDynamicCommentService;
import com.ruoyi.app.service.IAppDynamicLikeService;
import com.ruoyi.app.service.IAppDynamicService;
import com.ruoyi.app.service.IAppUserService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.CommentType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * APP用户动态
 *
 * <AUTHOR>
 * @date 2025-12-22
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/dynamic")
public class AppDynamicController extends BaseController {

    private final IAppUserService appUserService;
    private final IAppDynamicService iAppDynamicService;
    private final IAppDynamicLikeService appDynamicLikeService;
    private final IAppDynamicCommentService appDynamicCommentService;

    /**
     * 查询APP用户动态列表
     */
    @SaCheckPermission("system:dynamic:list")
    @GetMapping("/list")
    public TableDataInfo<AppDynamicVo> list(AppDynamicBo bo, PageQuery pageQuery) {
        TableDataInfo<AppDynamicVo> page = iAppDynamicService.queryPageList(bo, pageQuery);
        page.getData().forEach(e -> {
            e.setUserVo(appUserService.filterateUserVo(e.getUserId()));
            List<AppDynamicLikeVo> likeVos = appDynamicLikeService.queryByLike(e.getId(), CommentType.DYNAMIC.getCode());
            e.setLikeVosNumber(likeVos.size());
            AppDynamicCommentBo commentBo = new AppDynamicCommentBo();
            commentBo.setDynamicId(e.getId());
            List<AppDynamicCommentVo> commentVos = appDynamicCommentService.queryList(commentBo);
            e.setCommentVosNumber(commentVos.size());
        });
        return page;
    }

    /**
     * 导出APP用户动态列表
     */
    @SaCheckPermission("system:dynamic:export")
    @Log(title = "APP用户动态", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AppDynamicBo bo, HttpServletResponse response) {
        List<AppDynamicVo> list = iAppDynamicService.queryList(bo);
        ExcelUtil.exportExcel(list, "APP用户动态", AppDynamicVo.class, response);
    }

    /**
     * 获取APP用户动态详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:dynamic:query")
    @GetMapping("/{id}")
    public R<AppDynamicVo> getInfo(@NotNull(message = "主键不能为空")
                                   @PathVariable Long id) {
        AppDynamicVo e = iAppDynamicService.queryById(id);
        e.setUserVo(appUserService.filterateUserVo(e.getUserId()));
        List<AppDynamicLikeVo> likeVos = appDynamicLikeService.queryByLike(e.getId(), CommentType.DYNAMIC.getCode());
        e.setLikeVosNumber(likeVos.size());
        AppDynamicCommentBo commentBo = new AppDynamicCommentBo();
        commentBo.setDynamicId(e.getId());
        List<AppDynamicCommentVo> commentVos = appDynamicCommentService.queryList(commentBo);
        e.setCommentVosNumber(commentVos.size());
        e.setCommentVos(commentVos);
        return R.ok(e);
    }

    /**
     * 修改APP用户动态
     */
    @SaCheckPermission("system:dynamic:edit")
    @Log(title = "APP用户动态", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@RequestBody AppDynamicBo bo) {
        return toAjax(iAppDynamicService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除APP用户动态
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:dynamic:remove")
    @Log(title = "APP用户动态", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iAppDynamicService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
