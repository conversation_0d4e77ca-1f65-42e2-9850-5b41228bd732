package com.ruoyi.web.controller.app;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ruoyi.app.domain.bo.AppLevelBo;
import com.ruoyi.app.domain.vo.AppLevelVo;
import com.ruoyi.app.service.IAppLevelService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * AP用户等级
 *
 * <AUTHOR>
 * @date 2024-09-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/level")
public class AppLevelController extends BaseController {

    private final IAppLevelService iAppLevelService;

    /**
     * 查询AP用户等级列表
     */
    @SaCheckPermission("system:level:list")
    @GetMapping("/list")
    public TableDataInfo<AppLevelVo> list(AppLevelBo bo, PageQuery pageQuery) {
        return iAppLevelService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出AP用户等级列表
     */
    @SaCheckPermission("system:level:export")
    @Log(title = "AP用户等级", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AppLevelBo bo, HttpServletResponse response) {
        List<AppLevelVo> list = iAppLevelService.queryList(bo);
        ExcelUtil.exportExcel(list, "AP用户等级", AppLevelVo.class, response);
    }

    /**
     * 获取AP用户等级详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:level:query")
    @GetMapping("/{id}")
    public R<AppLevelVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iAppLevelService.queryById(id));
    }

    /**
     * 新增AP用户等级
     */
    @SaCheckPermission("system:level:add")
    @Log(title = "AP用户等级", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AppLevelBo bo) {
        return toAjax(iAppLevelService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改AP用户等级
     */
    @SaCheckPermission("system:level:edit")
    @Log(title = "AP用户等级", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AppLevelBo bo) {
        return toAjax(iAppLevelService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除AP用户等级
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:level:remove")
    @Log(title = "AP用户等级", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iAppLevelService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
