package com.ruoyi.web.controller.app;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ruoyi.app.domain.bo.AppLikeNotesBo;
import com.ruoyi.app.domain.vo.AppLikeNotesVo;
import com.ruoyi.app.service.IAppLikeNotesService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * APP用户喜欢记录
 *
 * <AUTHOR>
 * @date 2025-02-14
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/likeNotes")
public class AppLikeNotesController extends BaseController {

    private final IAppLikeNotesService iAppLikeNotesService;

    /**
     * 查询APP用户喜欢记录列表
     */
    @SaCheckPermission("system:likeNotes:list")
    @GetMapping("/list")
    public TableDataInfo<AppLikeNotesVo> list(AppLikeNotesBo bo, PageQuery pageQuery) {
        return iAppLikeNotesService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出APP用户喜欢记录列表
     */
    @SaCheckPermission("system:likeNotes:export")
    @Log(title = "APP用户喜欢记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AppLikeNotesBo bo, HttpServletResponse response) {
        List<AppLikeNotesVo> list = iAppLikeNotesService.queryList(bo);
        ExcelUtil.exportExcel(list, "APP用户喜欢记录", AppLikeNotesVo.class, response);
    }

    /**
     * 获取APP用户喜欢记录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:likeNotes:query")
    @GetMapping("/{id}")
    public R<AppLikeNotesVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iAppLikeNotesService.queryById(id));
    }

    /**
     * 新增APP用户喜欢记录
     */
    @SaCheckPermission("system:likeNotes:add")
    @Log(title = "APP用户喜欢记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AppLikeNotesBo bo) {
        return toAjax(iAppLikeNotesService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改APP用户喜欢记录
     */
    @SaCheckPermission("system:likeNotes:edit")
    @Log(title = "APP用户喜欢记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AppLikeNotesBo bo) {
        return toAjax(iAppLikeNotesService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除APP用户喜欢记录
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:likeNotes:remove")
    @Log(title = "APP用户喜欢记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iAppLikeNotesService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
