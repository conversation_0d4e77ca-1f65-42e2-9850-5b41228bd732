package com.ruoyi.web.controller.app;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ruoyi.app.domain.bo.AppOccupationBo;
import com.ruoyi.app.domain.vo.AppOccupationVo;
import com.ruoyi.app.service.IAppOccupationService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * APP职业信息
 *
 * <AUTHOR>
 * @date 2025-12-12
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/occupation")
public class AppOccupationController extends BaseController {

    private final IAppOccupationService iAppOccupationService;

    /**
     * 查询APP职业信息列表
     */
    @SaCheckPermission("system:occupation:list")
    @GetMapping("/list")
    public TableDataInfo<AppOccupationVo> list(AppOccupationBo bo, PageQuery pageQuery) {
        return iAppOccupationService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出APP职业信息列表
     */
    @SaCheckPermission("system:occupation:export")
    @Log(title = "APP职业信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AppOccupationBo bo, HttpServletResponse response) {
        List<AppOccupationVo> list = iAppOccupationService.queryList(bo);
        ExcelUtil.exportExcel(list, "APP职业信息", AppOccupationVo.class, response);
    }

    /**
     * 获取APP职业信息详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:occupation:query")
    @GetMapping("/{id}")
    public R<AppOccupationVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iAppOccupationService.queryById(id));
    }

    /**
     * 新增APP职业信息
     */
    @SaCheckPermission("system:occupation:add")
    @Log(title = "APP职业信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AppOccupationBo bo) {
        return toAjax(iAppOccupationService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改APP职业信息
     */
    @SaCheckPermission("system:occupation:edit")
    @Log(title = "APP职业信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AppOccupationBo bo) {
        return toAjax(iAppOccupationService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除APP职业信息
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:occupation:remove")
    @Log(title = "APP职业信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iAppOccupationService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
