package com.ruoyi.web.controller.app;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ruoyi.app.domain.bo.AppReportBo;
import com.ruoyi.app.domain.vo.AppReportVo;
import com.ruoyi.app.service.IAppReportService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * APP举报类型
 *
 * <AUTHOR>
 * @date 2025-01-03
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/report")
public class AppReportController extends BaseController {

    private final IAppReportService iAppReportService;

    /**
     * 查询APP举报类型列表
     */
    @SaCheckPermission("system:report:list")
    @GetMapping("/list")
    public TableDataInfo<AppReportVo> list(AppReportBo bo, PageQuery pageQuery) {
        return iAppReportService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出APP举报类型列表
     */
    @SaCheckPermission("system:report:export")
    @Log(title = "APP举报类型", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AppReportBo bo, HttpServletResponse response) {
        List<AppReportVo> list = iAppReportService.queryList(bo);
        ExcelUtil.exportExcel(list, "APP举报类型", AppReportVo.class, response);
    }

    /**
     * 获取APP举报类型详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:report:query")
    @GetMapping("/{id}")
    public R<AppReportVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iAppReportService.queryById(id));
    }

    /**
     * 新增APP举报类型
     */
    @SaCheckPermission("system:report:add")
    @Log(title = "APP举报类型", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AppReportBo bo) {
        return toAjax(iAppReportService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改APP举报类型
     */
    @SaCheckPermission("system:report:edit")
    @Log(title = "APP举报类型", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AppReportBo bo) {
        return toAjax(iAppReportService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除APP举报类型
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:report:remove")
    @Log(title = "APP举报类型", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iAppReportService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
