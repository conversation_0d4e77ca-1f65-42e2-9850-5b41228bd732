package com.ruoyi.web.controller.system;

import cn.dev33.satoken.annotation.SaIgnore;
import com.ruoyi.app.domain.bo.AppAssetBo;
import com.ruoyi.app.domain.bo.AppDynamicBo;
import com.ruoyi.app.domain.bo.AppGroupBo;
import com.ruoyi.app.domain.bo.AppUserBo;
import com.ruoyi.app.domain.vo.AppAssetVo;
import com.ruoyi.app.domain.vo.AppDynamicVo;
import com.ruoyi.app.domain.vo.AppGroupVo;
import com.ruoyi.app.domain.vo.AppUserVo;
import com.ruoyi.app.service.IAppAssetService;
import com.ruoyi.app.service.IAppDynamicService;
import com.ruoyi.app.service.IAppGroupService;
import com.ruoyi.app.service.IAppUserService;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.redis.RedisUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;

/**
 * 首页
 *
 * <AUTHOR> Li
 */
@RequiredArgsConstructor
@RestController
public class SysIndexController {

    /**
     * 系统基础配置
     */
    private final RuoYiConfig ruoyiConfig;
    private final IAppUserService iAppUserService;
    private final IAppAssetService appAssetService;
    private final IAppDynamicService iAppDynamicService;

    /**
     * 访问首页，提示语
     */
    @SaIgnore
    @GetMapping("/")
    public String index() {
        return StringUtils.format("欢迎使用{}后台管理框架，当前版本：v{}，请通过前端地址访问。", ruoyiConfig.getName(), ruoyiConfig.getVersion());
    }

    /**
     * 首页的统计数据
     *
     * @return
     */
    @GetMapping("indexData")
    public R<Object> indexData() {
        //当前用户总数
        HashMap<String, Object> map = new HashMap<>(4);
        List<AppUserVo> userVos = iAppUserService.queryList(new AppUserBo());
        int sum = userVos.size();
        map.put("userSum", sum);
        //今日新增用户总数
        Integer num = RedisUtils.getCacheObject(CacheConstants.USER_SUM);
        if (num == null) {
            map.put("newSum", 0);
        } else {
            map.put("newSum", sum - num);
        }
        //金币数量
        List<AppAssetVo> assetVos = appAssetService.queryList(new AppAssetBo());
        map.put("gourpSum", assetVos.stream().map(AppAssetVo::getBalance).reduce(BigDecimal.ZERO, BigDecimal::add));
        //动态数量
        List<AppDynamicVo> list = iAppDynamicService.queryList(new AppDynamicBo());
        map.put("dynamicSum", list.size());
        return R.ok(map);
    }
}
