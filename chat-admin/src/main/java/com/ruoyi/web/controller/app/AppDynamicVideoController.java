package com.ruoyi.web.controller.app;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ruoyi.app.domain.bo.AppDynamicVideoBo;
import com.ruoyi.app.domain.vo.AppDynamicVideoVo;
import com.ruoyi.app.service.IAppDynamicVideoService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import lombok.RequiredArgsConstructor;
import org.redisson.api.RList;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * APP动态视频信息
 *
 * <AUTHOR>
 * @date 2023-02-21
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/dynamicVideo")
public class AppDynamicVideoController extends BaseController {

    private final IAppDynamicVideoService iAppDynamicVideoService;

    /**
     * 查询APP动态视频信息列表
     */
    @GetMapping("/page")
    public TableDataInfo<AppDynamicVideoVo> page(AppDynamicVideoBo bo, PageQuery pageQuery) {
        return iAppDynamicVideoService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询APP动态视频信息列表
     */

    @GetMapping("/list")
    public R<List<AppDynamicVideoVo>> list(AppDynamicVideoBo bo) {
        return R.ok(iAppDynamicVideoService.queryList(bo));
    }

    /**
     * 获取APP动态视频信息详细信息
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    public R<AppDynamicVideoVo> getInfo(@NotNull(message = "主键不能为空")
                                        @PathVariable Long id) {
        return R.ok(iAppDynamicVideoService.queryById(id));
    }

    /**
     * 新增APP动态视频信息
     */
    @Log(title = "APP动态视频信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AppDynamicVideoBo bo) {
        return toAjax(iAppDynamicVideoService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改APP动态视频信息
     */
    @Log(title = "APP动态视频信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@RequestBody AppDynamicVideoBo bo) {
        return toAjax(iAppDynamicVideoService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除APP动态视频信息
     *
     * @param ids 主键串
     */
    @Log(title = "APP动态视频信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iAppDynamicVideoService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
