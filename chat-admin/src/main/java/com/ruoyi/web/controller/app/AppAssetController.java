package com.ruoyi.web.controller.app;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ruoyi.app.domain.bo.AppAssetBo;
import com.ruoyi.app.domain.vo.AppAssetVo;
import com.ruoyi.app.service.IAppAssetService;
import com.ruoyi.app.service.IAppUserService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * APP用户资产信息
 *
 * <AUTHOR>
 * @date 2025-12-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/asset")
public class AppAssetController extends BaseController {

    private final IAppAssetService iAppAssetService;
    private final IAppUserService iAppUserService;

    /**
     * 查询APP用户资产信息列表
     */
    @SaCheckPermission("system:asset:list")
    @GetMapping("/list")
    public TableDataInfo<AppAssetVo> list(AppAssetBo bo, PageQuery pageQuery) {
        TableDataInfo<AppAssetVo> list = iAppAssetService.queryPageList(bo, pageQuery);
        list.getData().forEach(e -> e.setUserVo(iAppUserService.queryByIdUserVo(e.getUserId())));
        return list;
    }

    /**
     * 导出APP用户资产信息列表
     */
    @SaCheckPermission("system:asset:export")
    @Log(title = "APP用户资产信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AppAssetBo bo, HttpServletResponse response) {
        List<AppAssetVo> list = iAppAssetService.queryList(bo);
        ExcelUtil.exportExcel(list, "APP用户资产信息", AppAssetVo.class, response);
    }

    /**
     * 获取APP用户资产信息详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:asset:query")
    @GetMapping("/{id}")
    public R<AppAssetVo> getInfo(@NotNull(message = "主键不能为空")
                                 @PathVariable Long id) {
        return R.ok(iAppAssetService.queryById(id));
    }

    /**
     * 新增APP用户资产信息
     */
    @SaCheckPermission("system:asset:add")
    @Log(title = "APP用户资产信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AppAssetBo bo) {
        return toAjax(iAppAssetService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改APP用户资产信息
     */
    @SaCheckPermission("system:asset:edit")
    @Log(title = "APP用户资产信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AppAssetBo bo) {
        return toAjax(iAppAssetService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除APP用户资产信息
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:asset:remove")
    @Log(title = "APP用户资产信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iAppAssetService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
