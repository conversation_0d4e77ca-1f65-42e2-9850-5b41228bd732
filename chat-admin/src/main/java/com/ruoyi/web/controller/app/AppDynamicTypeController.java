package com.ruoyi.web.controller.app;

import java.util.List;
import java.util.Arrays;
import java.util.concurrent.TimeUnit;

import com.ruoyi.common.constant.Constants;
import lombok.RequiredArgsConstructor;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;

import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import com.ruoyi.common.core.validate.QueryGroup;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.app.domain.vo.AppDynamicTypeVo;
import com.ruoyi.app.domain.bo.AppDynamicTypeBo;
import com.ruoyi.app.service.IAppDynamicTypeService;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * APP用户动态话题类型
 *
 * <AUTHOR>
 * @date 2025-05-18
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/dynamicType")
public class AppDynamicTypeController extends BaseController {

    private final IAppDynamicTypeService iAppDynamicTypeService;

    /**
     * 查询APP用户动态话题类型列表
     */
    @SaCheckPermission("system:dynamicType:list")
    @GetMapping("/list")
    public TableDataInfo<AppDynamicTypeVo> list(AppDynamicTypeBo bo, PageQuery pageQuery) {
        return iAppDynamicTypeService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出APP用户动态话题类型列表
     */
    @SaCheckPermission("system:dynamicType:export")
    @Log(title = "APP用户动态话题类型", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AppDynamicTypeBo bo, HttpServletResponse response) {
        List<AppDynamicTypeVo> list = iAppDynamicTypeService.queryList(bo);
        ExcelUtil.exportExcel(list, "APP用户动态话题类型", AppDynamicTypeVo.class, response);
    }

    /**
     * 获取APP用户动态话题类型详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:dynamicType:query")
    @GetMapping("/{id}")
    public R<AppDynamicTypeVo> getInfo(@NotNull(message = "主键不能为空")
                                       @PathVariable Long id) {
        return R.ok(iAppDynamicTypeService.queryById(id));
    }

    /**
     * 新增APP用户动态话题类型
     */
    @SaCheckPermission("system:dynamicType:add")
    @Log(title = "APP用户动态话题类型", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AppDynamicTypeBo bo) {
        bo.setType(Constants.SUCCESS);
        bo.setHeat(0);
        return toAjax(iAppDynamicTypeService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改APP用户动态话题类型
     */
    @SaCheckPermission("system:dynamicType:edit")
    @Log(title = "APP用户动态话题类型", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AppDynamicTypeBo bo) {
        return toAjax(iAppDynamicTypeService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除APP用户动态话题类型
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:dynamicType:remove")
    @Log(title = "APP用户动态话题类型", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iAppDynamicTypeService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
