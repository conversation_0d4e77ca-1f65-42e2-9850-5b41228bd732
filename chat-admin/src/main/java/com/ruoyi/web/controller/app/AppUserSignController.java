package com.ruoyi.web.controller.app;

import java.util.List;
import java.util.Arrays;
import java.util.concurrent.TimeUnit;

import lombok.RequiredArgsConstructor;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;

import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import com.ruoyi.common.core.validate.QueryGroup;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.app.domain.vo.AppUserSignVo;
import com.ruoyi.app.domain.bo.AppUserSignBo;
import com.ruoyi.app.service.IAppUserSignService;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * App用户签到记录
 *
 * <AUTHOR>
 * @date 2023-03-28
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/userSign")
public class AppUserSignController extends BaseController {

    private final IAppUserSignService iAppUserSignService;

    /**
     * 查询App用户签到记录列表
     */
    @SaCheckPermission("system:userSign:list")
    @GetMapping("/list")
    public TableDataInfo<AppUserSignVo> list(AppUserSignBo bo, PageQuery pageQuery) {
        return iAppUserSignService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出App用户签到记录列表
     */
    @SaCheckPermission("system:userSign:export")
    @Log(title = "App用户签到记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AppUserSignBo bo, HttpServletResponse response) {
        List<AppUserSignVo> list = iAppUserSignService.queryList(bo);
        ExcelUtil.exportExcel(list, "App用户签到记录", AppUserSignVo.class, response);
    }

    /**
     * 获取App用户签到记录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:userSign:query")
    @GetMapping("/{id}")
    public R<AppUserSignVo> getInfo(@NotNull(message = "主键不能为空")
                                    @PathVariable Long id) {
        return R.ok(iAppUserSignService.queryById(id));
    }

    /**
     * 新增App用户签到记录
     */
    @SaCheckPermission("system:userSign:add")
    @Log(title = "App用户签到记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AppUserSignBo bo) {
        return toAjax(iAppUserSignService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改App用户签到记录
     */
    @SaCheckPermission("system:userSign:edit")
    @Log(title = "App用户签到记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AppUserSignBo bo) {
        return toAjax(iAppUserSignService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除App用户签到记录
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:userSign:remove")
    @Log(title = "App用户签到记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iAppUserSignService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
