package com.ruoyi.web.controller.app;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ruoyi.app.domain.bo.AppSchoolBo;
import com.ruoyi.app.domain.vo.AppSchoolVo;
import com.ruoyi.app.service.IAppSchoolService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * APP学校信息
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/school")
public class AppSchoolController extends BaseController {

    private final IAppSchoolService iAppSchoolService;

    /**
     * 查询APP学校信息列表
     */
    @SaCheckPermission("system:school:list")
    @GetMapping("/list")
    public TableDataInfo<AppSchoolVo> list(AppSchoolBo bo, PageQuery pageQuery) {
        return iAppSchoolService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出APP学校信息列表
     */
    @SaCheckPermission("system:school:export")
    @Log(title = "APP学校信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AppSchoolBo bo, HttpServletResponse response) {
        List<AppSchoolVo> list = iAppSchoolService.queryList(bo);
        ExcelUtil.exportExcel(list, "APP学校信息", AppSchoolVo.class, response);
    }

    /**
     * 获取APP学校信息详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:school:query")
    @GetMapping("/{id}")
    public R<AppSchoolVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iAppSchoolService.queryById(id));
    }

    /**
     * 新增APP学校信息
     */
    @SaCheckPermission("system:school:add")
    @Log(title = "APP学校信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AppSchoolBo bo) {
        return toAjax(iAppSchoolService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改APP学校信息
     */
    @SaCheckPermission("system:school:edit")
    @Log(title = "APP学校信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AppSchoolBo bo) {
        return toAjax(iAppSchoolService.updateByBo(bo) ? 1 : 0);
    }
}
