package com.ruoyi.web.controller.app;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ruoyi.app.domain.bo.AppUserSettingBo;
import com.ruoyi.app.domain.vo.AppUserSettingVo;
import com.ruoyi.app.service.IAppUserSettingService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * APP用户信息设置
 *
 * <AUTHOR>
 * @date 2023-02-02
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/userSetting")
public class AppUserSettingController extends BaseController {

    private final IAppUserSettingService iAppUserSettingService;

    /**
     * 查询APP用户信息设置列表
     */
    @SaCheckPermission("system:userSetting:list")
    @GetMapping("/list")
    public TableDataInfo<AppUserSettingVo> list(AppUserSettingBo bo, PageQuery pageQuery) {
        return iAppUserSettingService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出APP用户信息设置列表
     */
    @SaCheckPermission("system:userSetting:export")
    @Log(title = "APP用户信息设置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AppUserSettingBo bo, HttpServletResponse response) {
        List<AppUserSettingVo> list = iAppUserSettingService.queryList(bo);
        ExcelUtil.exportExcel(list, "APP用户信息设置", AppUserSettingVo.class, response);
    }

    /**
     * 获取APP用户信息设置详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:userSetting:query")
    @GetMapping("/{id}")
    public R<AppUserSettingVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iAppUserSettingService.queryById(id));
    }

    /**
     * 新增APP用户信息设置
     */
    @SaCheckPermission("system:userSetting:add")
    @Log(title = "APP用户信息设置", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AppUserSettingBo bo) {
        return toAjax(iAppUserSettingService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改APP用户信息设置
     */
    @SaCheckPermission("system:userSetting:edit")
    @Log(title = "APP用户信息设置", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AppUserSettingBo bo) {
        return toAjax(iAppUserSettingService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除APP用户信息设置
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:userSetting:remove")
    @Log(title = "APP用户信息设置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iAppUserSettingService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
