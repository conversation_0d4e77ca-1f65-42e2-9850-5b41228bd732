package com.ruoyi.web.controller.app;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ruoyi.app.domain.bo.AppLabelTypeBo;
import com.ruoyi.app.domain.vo.AppLabelTypeVo;
import com.ruoyi.app.service.IAppLabelTypeService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * APP标签类型
 *
 * <AUTHOR>
 * @date 2023-01-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/labelType")
public class AppLabelTypeController extends BaseController {

    private final IAppLabelTypeService iAppLabelTypeService;

    /**
     * 查询APP标签类型列表
     */
    @SaCheckPermission("system:labelType:list")
    @GetMapping("/list")
    public TableDataInfo<AppLabelTypeVo> list(AppLabelTypeBo bo, PageQuery pageQuery) {
        return iAppLabelTypeService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询APP标签类型列表
     */
    @GetMapping("/select")
    public R<List<AppLabelTypeVo>> list() {
        AppLabelTypeBo appLabelTypeBo = new AppLabelTypeBo();
        appLabelTypeBo.setStatus(Constants.SUCCESS);
        return R.ok(iAppLabelTypeService.queryList(appLabelTypeBo));
    }

    /**
     * 导出APP标签类型列表
     */
    @SaCheckPermission("system:labelType:export")
    @Log(title = "APP标签类型", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AppLabelTypeBo bo, HttpServletResponse response) {
        List<AppLabelTypeVo> list = iAppLabelTypeService.queryList(bo);
        ExcelUtil.exportExcel(list, "APP标签类型", AppLabelTypeVo.class, response);
    }

    /**
     * 获取APP标签类型详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:labelType:query")
    @GetMapping("/{id}")
    public R<AppLabelTypeVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iAppLabelTypeService.queryById(id));
    }

    /**
     * 新增APP标签类型
     */
    @SaCheckPermission("system:labelType:add")
    @Log(title = "APP标签类型", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AppLabelTypeBo bo) {
        return toAjax(iAppLabelTypeService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改APP标签类型
     */
    @SaCheckPermission("system:labelType:edit")
    @Log(title = "APP标签类型", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AppLabelTypeBo bo) {
        return toAjax(iAppLabelTypeService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除APP标签类型
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:labelType:remove")
    @Log(title = "APP标签类型", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iAppLabelTypeService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
