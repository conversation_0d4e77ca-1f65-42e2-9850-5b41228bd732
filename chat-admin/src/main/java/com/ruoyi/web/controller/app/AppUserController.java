package com.ruoyi.web.controller.app;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.crypto.digest.BCrypt;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.app.domain.AppUser;
import com.ruoyi.app.domain.bo.AppUserBo;
import com.ruoyi.app.domain.bo.AppUserVipBo;
import com.ruoyi.app.domain.vo.AppUserVipVo;
import com.ruoyi.app.domain.vo.AppUserVo;
import com.ruoyi.app.service.IAppUserImageService;
import com.ruoyi.app.service.IAppUserService;
import com.ruoyi.app.service.IAppUserVipService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * APP用户信息
 *
 * <AUTHOR>
 * @date 2022-12-08
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/appUser")
public class AppUserController extends BaseController {

    private final IAppUserService iAppUserService;
    private final IAppUserVipService iAppUserVipService;
    private final IAppUserImageService iAppUserImageService;

    /**
     * 查询APP用户信息列表
     */
    @SaCheckPermission("system:appUser:list")
    @GetMapping("/list")
    public TableDataInfo<AppUserVo> list(AppUserBo bo, PageQuery pageQuery) {
        TableDataInfo<AppUserVo> page = iAppUserService.queryPageList(bo, pageQuery);
        page.getData().forEach(e -> {
            e.setVip(false);
            e.setVipTime(null);
            String vip = iAppUserVipService.userVip(e.getId());
            if (StringUtils.isNotBlank(vip)) {
                e.setVip(true);
                e.setVipTime(vip);
            }
        });
        return page;
    }

    /**
     * 导出APP用户信息列表
     */
    @SaCheckPermission("system:appUser:export")
    @Log(title = "APP用户信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AppUserBo bo, HttpServletResponse response) {
        List<AppUserVo> list = iAppUserService.queryList(bo);
        ExcelUtil.exportExcel(list, "APP用户信息", AppUserVo.class, response);
    }

    /**
     * 获取APP用户信息详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:appUser:query")
    @GetMapping("/{id}")
    public R<AppUserVo> getInfo(@NotNull(message = "主键不能为空")
                                @PathVariable Long id) {
        AppUserVo appUserVo = iAppUserService.queryById(id);
        appUserVo.setVipId(Constants.SUCCESS);
        AppUserVipVo vipVo = iAppUserVipService.userVipId(appUserVo.getId());
        if (Objects.nonNull(vipVo)) {
            appUserVo.setVipId(String.valueOf(vipVo.getLevel()));
            appUserVo.setVipExpireTime(vipVo.getEndTime());
        }
        appUserVo.setPhotos(iAppUserImageService.queryList(appUserVo.getId()));
        return R.ok(appUserVo);
    }


    /**
     * 新增APP用户信息
     */
    @SaCheckPermission("system:appUser:add")
    @Log(title = "APP用户信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AppUserBo bo) {
        return toAjax(iAppUserService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改APP用户信息
     */
    @SaCheckPermission("system:appUser:edit")
    @Log(title = "APP用户信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@RequestBody AppUserBo bo) {
        if (StringUtils.isNotBlank(bo.getPassword())) {
            bo.setPassword(BCrypt.hashpw(bo.getPassword()));
        }
        if (bo.getId() != null) {
            AppUserVipVo vipVo = iAppUserVipService.queryByUserId(bo.getId());
            if (bo.getVipId() == 0 && Objects.nonNull(vipVo)) {
                iAppUserVipService.deleteWithValidById(vipVo.getId());
            }
            if (bo.getVipId() != 0) {
                if (bo.getVipExpireTime() < System.currentTimeMillis()) {
                    return R.fail("VIP时间不能小于当前时间");
                }
                if (Objects.isNull(vipVo)) {
                    AppUserVipBo appUserVipBo = new AppUserVipBo();
                    appUserVipBo.setUserId(bo.getId());
                    appUserVipBo.setLevel(bo.getVipId());
                    appUserVipBo.setEndTime(bo.getVipExpireTime());
                    appUserVipBo.setExperience(0);
                    iAppUserVipService.insertByBo(appUserVipBo);
                } else {
                    vipVo.setLevel(bo.getVipId());
                    vipVo.setEndTime(bo.getVipExpireTime());
                    iAppUserVipService.updateByVo(vipVo);
                }
            }
        }
        return toAjax(iAppUserService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除APP用户信息
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:appUser:remove")
    @Log(title = "APP用户信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iAppUserService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
