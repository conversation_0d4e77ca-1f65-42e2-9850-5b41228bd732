package com.ruoyi.web.controller.app;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ruoyi.app.domain.bo.AppNoticeBo;
import com.ruoyi.app.domain.vo.AppNoticeVo;
import com.ruoyi.app.service.IAppNoticeService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * APP用户通知信息
 *
 * <AUTHOR>
 * @date 2022-12-27
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/appNotice")
public class AppNoticeController extends BaseController {

    private final IAppNoticeService iAppNoticeService;

    /**
     * 查询APP用户通知信息列表
     */
    @SaCheckPermission("system:appNotice:list")
    @GetMapping("/list")
    public TableDataInfo<AppNoticeVo> list(AppNoticeBo bo, PageQuery pageQuery) {
        return iAppNoticeService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出APP用户通知信息列表
     */
    @SaCheckPermission("system:appNotice:export")
    @Log(title = "APP用户通知信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AppNoticeBo bo, HttpServletResponse response) {
        List<AppNoticeVo> list = iAppNoticeService.queryList(bo);
        ExcelUtil.exportExcel(list, "APP用户通知信息", AppNoticeVo.class, response);
    }

    /**
     * 获取APP用户通知信息详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:appNotice:query")
    @GetMapping("/{id}")
    public R<AppNoticeVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iAppNoticeService.queryById(id));
    }

    /**
     * 新增APP用户通知信息
     */
    @SaCheckPermission("system:appNotice:add")
    @Log(title = "APP用户通知信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AppNoticeBo bo) {
        return toAjax(iAppNoticeService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改APP用户通知信息
     */
    @SaCheckPermission("system:appNotice:edit")
    @Log(title = "APP用户通知信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AppNoticeBo bo) {
        return toAjax(iAppNoticeService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除APP用户通知信息
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:appNotice:remove")
    @Log(title = "APP用户通知信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iAppNoticeService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
