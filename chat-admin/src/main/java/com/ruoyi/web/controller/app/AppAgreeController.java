package com.ruoyi.web.controller.app;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ruoyi.app.domain.bo.AppAgreeBo;
import com.ruoyi.app.domain.vo.AppAgreeVo;
import com.ruoyi.app.service.IAppAgreeService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * APP时光约会
 *
 * <AUTHOR>
 * @date 2024-03-25
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/agree")
public class AppAgreeController extends BaseController {

    private final IAppAgreeService iAppAgreeService;

    /**
     * 查询APP时光约会列表
     */
    @SaCheckPermission("system:agree:list")
    @GetMapping("/list")
    public TableDataInfo<AppAgreeVo> list(AppAgreeBo bo, PageQuery pageQuery) {
        return iAppAgreeService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出APP时光约会列表
     */
    @SaCheckPermission("system:agree:export")
    @Log(title = "APP时光约会", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AppAgreeBo bo, HttpServletResponse response) {
        List<AppAgreeVo> list = iAppAgreeService.queryList(bo);
        ExcelUtil.exportExcel(list, "APP时光约会", AppAgreeVo.class, response);
    }

    /**
     * 获取APP时光约会详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:agree:query")
    @GetMapping("/{id}")
    public R<AppAgreeVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iAppAgreeService.queryById(id));
    }

    /**
     * 新增APP时光约会
     */
    @SaCheckPermission("system:agree:add")
    @Log(title = "APP时光约会", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AppAgreeBo bo) {
        return toAjax(iAppAgreeService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改APP时光约会
     */
    @SaCheckPermission("app:agree:edit")
    @Log(title = "APP时光约会", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AppAgreeBo bo) {
        return toAjax(iAppAgreeService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除APP时光约会
     *
     * @param ids 主键串
     */
    @SaCheckPermission("app:agree:remove")
    @Log(title = "APP时光约会", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iAppAgreeService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
