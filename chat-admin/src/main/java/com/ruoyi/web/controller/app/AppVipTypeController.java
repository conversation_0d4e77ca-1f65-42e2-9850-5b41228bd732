package com.ruoyi.web.controller.app;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ruoyi.app.domain.bo.AppVipTypeBo;
import com.ruoyi.app.domain.vo.AppVipTypeVo;
import com.ruoyi.app.service.IAppVipTypeService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * APPVIP类型
 *
 * <AUTHOR>
 * @date 2023-03-16
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/vipType")
public class AppVipTypeController extends BaseController {

    private final IAppVipTypeService iAppVipTypeService;

    /**
     * 查询APPVIP类型列表
     */
    @SaCheckPermission("system:vipType:list")
    @GetMapping("/list")
    public TableDataInfo<AppVipTypeVo> list(AppVipTypeBo bo, PageQuery pageQuery) {
        return iAppVipTypeService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出APPVIP类型列表
     */
    @SaCheckPermission("system:vipType:export")
    @Log(title = "APPVIP类型", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AppVipTypeBo bo, HttpServletResponse response) {
        List<AppVipTypeVo> list = iAppVipTypeService.queryList(bo);
        ExcelUtil.exportExcel(list, "APPVIP类型", AppVipTypeVo.class, response);
    }

    /**
     * 获取APPVIP类型详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:vipType:query")
    @GetMapping("/{id}")
    public R<AppVipTypeVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iAppVipTypeService.queryById(id));
    }

    /**
     * 新增APPVIP类型
     */
    @SaCheckPermission("system:vipType:add")
    @Log(title = "APPVIP类型", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AppVipTypeBo bo) {
        return toAjax(iAppVipTypeService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改APPVIP类型
     */
    @SaCheckPermission("system:vipType:edit")
    @Log(title = "APPVIP类型", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AppVipTypeBo bo) {
        return toAjax(iAppVipTypeService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除APPVIP类型
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:vipType:remove")
    @Log(title = "APPVIP类型", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iAppVipTypeService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
