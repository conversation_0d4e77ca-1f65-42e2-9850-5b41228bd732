package com.ruoyi.web.controller.app;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ruoyi.app.domain.bo.AppAddressBookBo;
import com.ruoyi.app.domain.bo.AppFriendBo;
import com.ruoyi.app.domain.vo.AppAddressBookVo;
import com.ruoyi.app.domain.vo.AppFriendVo;
import com.ruoyi.app.domain.vo.AppUserVo;
import com.ruoyi.app.domain.vo.UserVo;
import com.ruoyi.app.service.IAppAddressBookService;
import com.ruoyi.app.service.IAppFriendService;
import com.ruoyi.app.service.IAppUserService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * APP用户关注
 *
 * <AUTHOR>
 * @date 2025-01-04
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/friend")
public class AppFriendController extends BaseController {

    private final IAppFriendService iAppFriendService;
    private final IAppUserService appUserService;
    private final IAppAddressBookService appAddressBookService;

    /**
     * 查询APP用户关注列表
     */
    @SaCheckPermission("system:friend:page")
    @GetMapping("/page")
    public TableDataInfo<AppFriendVo> page(AppFriendBo bo, PageQuery pageQuery) {
        return iAppFriendService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询APP用户好友列表
     */
    @SaCheckPermission("system:friend:list")
    @GetMapping("/list")
    public R<List<AppUserVo>> list(AppFriendBo bo) {
        AppAddressBookBo appAddressBookBo = new AppAddressBookBo();
        appAddressBookBo.setUserId(bo.getUserId());
        List<AppAddressBookVo> bookVos = appAddressBookService.queryList(appAddressBookBo);
        ArrayList<AppUserVo> userVos = new ArrayList<>();
        bookVos.forEach(e -> {
            AppUserVo userVo = appUserService.queryById(e.getBookId());
            userVos.add(userVo);
        });
        return R.ok(userVos);
    }

    /**
     * 导出APP用户关注列表
     */
    @SaCheckPermission("system:friend:export")
    @Log(title = "APP用户关注", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AppFriendBo bo, HttpServletResponse response) {
        List<AppFriendVo> list = iAppFriendService.queryList(bo);
        ExcelUtil.exportExcel(list, "APP用户关注", AppFriendVo.class, response);
    }

    /**
     * 获取APP用户关注详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:friend:query")
    @GetMapping("/{id}")
    public R<AppFriendVo> getInfo(@NotNull(message = "主键不能为空")
                                  @PathVariable Long id) {
        return R.ok(iAppFriendService.queryById(id));
    }

    /**
     * 新增APP用户关注
     */
    @SaCheckPermission("system:friend:add")
    @Log(title = "APP用户关注", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AppFriendBo bo) {
        return toAjax(iAppFriendService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改APP用户关注
     */
    @SaCheckPermission("system:friend:edit")
    @Log(title = "APP用户关注", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AppFriendBo bo) {
        return toAjax(iAppFriendService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除APP用户关注
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:friend:remove")
    @Log(title = "APP用户关注", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iAppFriendService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
