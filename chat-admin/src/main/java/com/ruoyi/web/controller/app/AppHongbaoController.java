package com.ruoyi.web.controller.app;

import java.util.List;
import java.util.Arrays;
import java.util.concurrent.TimeUnit;

import com.ruoyi.app.domain.vo.UserFilterVo;
import com.ruoyi.app.service.IAppGiftService;
import com.ruoyi.app.service.IAppUserService;
import lombok.RequiredArgsConstructor;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;

import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import com.ruoyi.common.core.validate.QueryGroup;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.app.domain.vo.AppHongbaoVo;
import com.ruoyi.app.domain.bo.AppHongbaoBo;
import com.ruoyi.app.service.IAppHongbaoService;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * APP红包
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/hongbao")
public class AppHongbaoController extends BaseController {

    private final IAppUserService appUserService;
    private final IAppHongbaoService iAppHongbaoService;

    /**
     * 查询APP红包列表
     */
    @SaCheckPermission("system:hongbao:list")
    @GetMapping("/list")
    public TableDataInfo<AppHongbaoVo> list(AppHongbaoBo bo, PageQuery pageQuery) {
        TableDataInfo<AppHongbaoVo> page = iAppHongbaoService.queryPageList(bo, pageQuery);
        page.getData().forEach(e -> {
            UserFilterVo userFilterVo = appUserService.userFilterVo(e.getUserId());
            UserFilterVo toUserFilterVo = appUserService.userFilterVo(e.getSendUser());
            e.setUserName(userFilterVo.getNickName());
            e.setSendUserName(toUserFilterVo.getNickName());
        });
        return page;
    }

    /**
     * 导出APP红包列表
     */
    @SaCheckPermission("system:hongbao:export")
    @Log(title = "APP红包", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AppHongbaoBo bo, HttpServletResponse response) {
        List<AppHongbaoVo> list = iAppHongbaoService.queryList(bo);
        ExcelUtil.exportExcel(list, "APP红包", AppHongbaoVo.class, response);
    }

    /**
     * 获取APP红包详细信息
     *
     * @param hongbaoId 主键
     */
    @SaCheckPermission("system:hongbao:query")
    @GetMapping("/{hongbaoId}")
    public R<AppHongbaoVo> getInfo(@NotNull(message = "主键不能为空")
                                   @PathVariable Long hongbaoId) {
        return R.ok(iAppHongbaoService.queryById(hongbaoId));
    }

    /**
     * 新增APP红包
     */
    @SaCheckPermission("system:hongbao:add")
    @Log(title = "APP红包", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AppHongbaoBo bo) {
        return toAjax(iAppHongbaoService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改APP红包
     */
    @SaCheckPermission("system:hongbao:edit")
    @Log(title = "APP红包", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AppHongbaoBo bo) {
        return toAjax(iAppHongbaoService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除APP红包
     *
     * @param hongbaoIds 主键串
     */
    @SaCheckPermission("system:hongbao:remove")
    @Log(title = "APP红包", businessType = BusinessType.DELETE)
    @DeleteMapping("/{hongbaoIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] hongbaoIds) {
        return toAjax(iAppHongbaoService.deleteWithValidByIds(Arrays.asList(hongbaoIds), true) ? 1 : 0);
    }
}
