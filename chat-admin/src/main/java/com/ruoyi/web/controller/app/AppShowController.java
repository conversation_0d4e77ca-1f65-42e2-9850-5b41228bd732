package com.ruoyi.web.controller.app;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ruoyi.app.domain.bo.AppShowBo;
import com.ruoyi.app.domain.vo.AppShowVo;
import com.ruoyi.app.service.IAppShowService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 活动展馆管理
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/show")
public class AppShowController extends BaseController {

    private final IAppShowService iAppShowService;

    /**
     * 查询活动展馆管理列表
     */
    @SaCheckPermission("system:show:list")
    @GetMapping("/list")
    public TableDataInfo<AppShowVo> list(AppShowBo bo, PageQuery pageQuery) {
        return iAppShowService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出活动展馆管理列表
     */
    @SaCheckPermission("system:show:export")
    @Log(title = "活动展馆管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AppShowBo bo, HttpServletResponse response) {
        List<AppShowVo> list = iAppShowService.queryList(bo);
        ExcelUtil.exportExcel(list, "活动展馆管理", AppShowVo.class, response);
    }

    /**
     * 获取活动展馆管理详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:show:query")
    @GetMapping("/{id}")
    public R<AppShowVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iAppShowService.queryById(id));
    }

    /**
     * 新增活动展馆管理
     */
    @SaCheckPermission("system:show:add")
    @Log(title = "活动展馆管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AppShowBo bo) {
        return toAjax(iAppShowService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改活动展馆管理
     */
    @SaCheckPermission("system:show:edit")
    @Log(title = "活动展馆管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AppShowBo bo) {
        return toAjax(iAppShowService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除活动展馆管理
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:show:remove")
    @Log(title = "活动展馆管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iAppShowService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
