package com.ruoyi.web.controller.app;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaIgnore;
import com.ruoyi.app.domain.bo.AppVersionBo;
import com.ruoyi.app.domain.vo.AppVersionVo;
import com.ruoyi.app.service.IAppVersionService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import com.ruoyi.common.enums.BusinessType;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;

/**
 * APP版本更新记录
 *
 * <AUTHOR>
 * @date 2023-04-11
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/version")
public class AppVersionController extends BaseController {

    private final IAppVersionService iAppVersionService;

    /**
     * 查询APP版本更新记录列表
     */
    @SaCheckPermission("system:version:list")
    @GetMapping("/list")
    public TableDataInfo<AppVersionVo> list(AppVersionBo bo, PageQuery pageQuery) {
        return iAppVersionService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取APP版本更新记录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:version:query")
    @GetMapping("/{id}")
    public R<AppVersionVo> getInfo(@NotNull(message = "主键不能为空")
                                   @PathVariable Long id) {
        return R.ok(iAppVersionService.queryById(id));
    }

    /**
     * 新增APP版本更新记录
     */
    @SaCheckPermission("system:version:add")
    @Log(title = "APP版本更新记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@RequestBody AppVersionBo bo) {
        return toAjax(iAppVersionService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改APP版本更新记录
     */
    @SaCheckPermission("system:version:edit")
    @Log(title = "APP版本更新记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AppVersionBo bo) {
        return toAjax(iAppVersionService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除APP版本更新记录
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:version:remove")
    @Log(title = "APP版本更新记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iAppVersionService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
