package com.ruoyi.web.controller.app;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ruoyi.app.domain.bo.AppLabelLikeBo;
import com.ruoyi.app.domain.vo.AppLabelLikeVo;
import com.ruoyi.app.service.IAppLabelLikeService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * APP用户标签喜欢
 *
 * <AUTHOR>
 * @date 2023-01-13
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/labelLike")
public class AppLabelLikeController extends BaseController {

    private final IAppLabelLikeService iAppLabelLikeService;

    /**
     * 查询APP用户标签喜欢列表
     */
    @SaCheckPermission("system:labelLike:list")
    @GetMapping("/list")
    public TableDataInfo<AppLabelLikeVo> list(AppLabelLikeBo bo, PageQuery pageQuery) {
        return iAppLabelLikeService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出APP用户标签喜欢列表
     */
    @SaCheckPermission("system:labelLike:export")
    @Log(title = "APP用户标签喜欢", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AppLabelLikeBo bo, HttpServletResponse response) {
        List<AppLabelLikeVo> list = iAppLabelLikeService.queryList(bo);
        ExcelUtil.exportExcel(list, "APP用户标签喜欢", AppLabelLikeVo.class, response);
    }

    /**
     * 获取APP用户标签喜欢详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:labelLike:query")
    @GetMapping("/{id}")
    public R<AppLabelLikeVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iAppLabelLikeService.queryById(id));
    }

    /**
     * 新增APP用户标签喜欢
     */
    @SaCheckPermission("system:labelLike:add")
    @Log(title = "APP用户标签喜欢", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AppLabelLikeBo bo) {
        return toAjax(iAppLabelLikeService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改APP用户标签喜欢
     */
    @SaCheckPermission("system:labelLike:edit")
    @Log(title = "APP用户标签喜欢", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AppLabelLikeBo bo) {
        return toAjax(iAppLabelLikeService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除APP用户标签喜欢
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:labelLike:remove")
    @Log(title = "APP用户标签喜欢", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iAppLabelLikeService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
