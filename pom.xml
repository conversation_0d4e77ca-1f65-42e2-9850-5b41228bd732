<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.ruoyi</groupId>
    <artifactId>chat-Plus-server</artifactId>
    <version>4.3.1</version>

    <name>chat-Plus-server</name>
    <url>https://gitee.com/JavaLionLi/RuoYi-Vue-Plus</url>
    <description>chat-Plus-server聊天交友系统</description>

    <properties>
        <ruoyi-vue-plus.version>4.3.1</ruoyi-vue-plus.version>
        <spring-boot.version>2.7.5</spring-boot.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <maven-jar-plugin.version>3.2.2</maven-jar-plugin.version>
        <spring-boot.mybatis>2.2.2</spring-boot.mybatis>
        <druid.version>1.2.12</druid.version>
        <springdoc.version>1.6.12</springdoc.version>
        <poi.version>5.2.3</poi.version>
        <easyexcel.version>3.1.1</easyexcel.version>
        <velocity.version>2.3</velocity.version>
        <satoken.version>1.31.0</satoken.version>
        <mybatis-plus.version>3.5.2</mybatis-plus.version>
        <p6spy.version>3.9.1</p6spy.version>
        <hutool.version>5.8.8</hutool.version>
        <okhttp.version>4.10.0</okhttp.version>
        <spring-boot-admin.version>2.7.6</spring-boot-admin.version>
        <redisson.version>3.17.7</redisson.version>
        <lock4j.version>2.2.2</lock4j.version>
        <dynamic-ds.version>3.5.2</dynamic-ds.version>
        <tlog.version>1.5.0</tlog.version>
        <xxl-job.version>2.3.1</xxl-job.version>
        <lombok.version>1.18.24</lombok.version>

        <!-- 统一 guava 版本 解决隐式漏洞问题 -->
        <guava.version>31.1-jre</guava.version>
        <!-- 临时修复 snakeyaml 漏洞 -->
        <snakeyaml.version>1.32</snakeyaml.version>

        <!-- OSS 配置 -->
        <aws-java-sdk-s3.version>1.12.324</aws-java-sdk-s3.version>
        <!-- SMS 配置 -->
        <aliyun.sms.version>2.0.22</aliyun.sms.version>
        <tencent.sms.version>3.1.611</tencent.sms.version>
        <!-- 阿里云一键登录 配置 -->
        <aliyun.pns.version>1.0.7</aliyun.pns.version>
        <!-- 环信IM 配置 -->
        <easemob.version>0.6.0</easemob.version>
    </properties>

    <!-- 依赖声明 -->
    <dependencyManagement>
        <dependencies>

            <!-- SpringBoot的依赖配置-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- hutool 的依赖配置-->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-bom</artifactId>
                <version>${hutool.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- 阿里数据库连接池 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-webmvc-core</artifactId>
                <version>${springdoc.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-javadoc</artifactId>
                <version>${springdoc.version}</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.poi</groupId>
                        <artifactId>poi-ooxml-schemas</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- velocity代码生成使用模板 -->
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
            </dependency>

            <!-- Sa-Token 权限认证, 在线文档：http://sa-token.dev33.cn/ -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-spring-boot-starter</artifactId>
                <version>${satoken.version}</version>
            </dependency>
            <!-- Sa-Token 整合 jwt -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-jwt</artifactId>
                <version>${satoken.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>cn.hutool</groupId>
                        <artifactId>hutool-all</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- dynamic-datasource 多数据源-->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                <version>${dynamic-ds.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-extension</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <!-- sql性能分析插件 -->
            <dependency>
                <groupId>p6spy</groupId>
                <artifactId>p6spy</artifactId>
                <version>${p6spy.version}</version>
            </dependency>

            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp.version}</version>
            </dependency>

            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-s3</artifactId>
                <version>${aws-java-sdk-s3.version}</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>dysmsapi20170525</artifactId>
                <version>${aliyun.sms.version}</version>
            </dependency>

            <dependency>
                <groupId>com.tencentcloudapi</groupId>
                <artifactId>tencentcloud-sdk-java</artifactId>
                <version>${tencent.sms.version}</version>
            </dependency>

            <dependency>
                <groupId>com.easemob.im</groupId>
                <artifactId>im-sdk-core</artifactId>
                <version>${easemob.version}</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>dypnsapi20170525</artifactId>
                <version>${aliyun.pns.version}</version>
            </dependency>

            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-starter-server</artifactId>
                <version>${spring-boot-admin.version}</version>
            </dependency>
            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-starter-client</artifactId>
                <version>${spring-boot-admin.version}</version>
            </dependency>

            <!--redisson-->
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>lock4j-redisson-spring-boot-starter</artifactId>
                <version>${lock4j.version}</version>
            </dependency>

            <!-- xxl-job-core -->
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yomahub</groupId>
                <artifactId>tlog-web-spring-boot-starter</artifactId>
                <version>${tlog.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>log4j</artifactId>
                        <groupId>log4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>dom4j</artifactId>
                        <groupId>dom4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-beanutils</artifactId>
                        <groupId>commons-beanutils</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.yomahub</groupId>
                <artifactId>tlog-xxljob-spring-boot-starter</artifactId>
                <version>${tlog.version}</version>
            </dependency>

            <!-- 统一 guava 版本 解决隐式漏洞问题 -->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>

            <!-- 临时修复 snakeyaml 漏洞 -->
            <dependency>
                <groupId>org.yaml</groupId>
                <artifactId>snakeyaml</artifactId>
                <version>${snakeyaml.version}</version>
            </dependency>

            <!-- 定时任务 -->
            <dependency>
                <groupId>com.ruoyi</groupId>
                <artifactId>chat-job</artifactId>
                <version>${ruoyi-vue-plus.version}</version>
            </dependency>

            <!-- 代码生成-->
            <dependency>
                <groupId>com.ruoyi</groupId>
                <artifactId>chat-generator</artifactId>
                <version>${ruoyi-vue-plus.version}</version>
            </dependency>

            <!-- 核心模块-->
            <dependency>
                <groupId>com.ruoyi</groupId>
                <artifactId>chat-framework</artifactId>
                <version>${ruoyi-vue-plus.version}</version>
            </dependency>

            <!-- 系统模块-->
            <dependency>
                <groupId>com.ruoyi</groupId>
                <artifactId>chat-system</artifactId>
                <version>${ruoyi-vue-plus.version}</version>
            </dependency>

            <!-- 通用工具-->
            <dependency>
                <groupId>com.ruoyi</groupId>
                <artifactId>chat-common</artifactId>
                <version>${ruoyi-vue-plus.version}</version>
            </dependency>

            <!-- OSS对象存储模块 -->
            <dependency>
                <groupId>com.ruoyi</groupId>
                <artifactId>chat-oss</artifactId>
                <version>${ruoyi-vue-plus.version}</version>
            </dependency>

            <!-- SMS短信模块 -->
            <dependency>
                <groupId>com.ruoyi</groupId>
                <artifactId>chat-sms</artifactId>
                <version>${ruoyi-vue-plus.version}</version>
            </dependency>

            <!-- IM环信模块 -->
            <dependency>
                <groupId>com.ruoyi</groupId>
                <artifactId>chat-im</artifactId>
                <version>${ruoyi-vue-plus.version}</version>
            </dependency>

            <!-- 腾讯IM签名模块 -->
            <dependency>
                <groupId>com.ruoyi</groupId>
                <artifactId>chat-txim</artifactId>
                <version>${ruoyi-vue-plus.version}</version>
            </dependency>

            <!-- demo模块 -->
            <dependency>
                <groupId>com.ruoyi</groupId>
                <artifactId>chat-demo</artifactId>
                <version>${ruoyi-vue-plus.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <modules>
        <module>chat-admin</module>
        <module>chat-framework</module>
        <module>chat-system</module>
        <module>chat-job</module>
        <module>chat-generator</module>
        <module>chat-common</module>
        <module>chat-app</module>
        <module>chat-im</module>
        <module>chat-oss</module>
        <module>chat-sms</module>
    </modules>
    <packaging>pom</packaging>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.9.0</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>com.github.therapi</groupId>
                            <artifactId>therapi-runtime-javadoc-scribe</artifactId>
                            <version>0.15.0</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.springframework.boot</groupId>
                            <artifactId>spring-boot-configuration-processor</artifactId>
                            <version>${spring-boot.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <!-- 关闭过滤 -->
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <!-- 引入所有 匹配文件进行过滤 -->
                <includes>
                    <include>application*</include>
                    <include>bootstrap*</include>
                    <include>banner*</include>
                </includes>
                <!-- 启用过滤 即该资源中的变量将会被过滤器中的值替换 -->
                <filtering>true</filtering>
            </resource>
        </resources>
    </build>

    <repositories>
        <repository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/repository/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/repository/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

    <profiles>
        <profile>
            <id>local</id>
            <properties>
                <!-- 环境标识，需要与配置文件的名称相对应 -->
                <profiles.active>local</profiles.active>
                <logging.level>debug</logging.level>
            </properties>
        </profile>
        <profile>
            <id>dev</id>
            <properties>
                <!-- 环境标识，需要与配置文件的名称相对应 -->
                <profiles.active>dev</profiles.active>
                <logging.level>debug</logging.level>
            </properties>
            <activation>
                <!-- 默认环境 -->
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <profiles.active>prod</profiles.active>
                <logging.level>warn</logging.level>
            </properties>
        </profile>
    </profiles>

</project>


