package com.ruoyi.sms.config;

import com.ruoyi.sms.config.properties.SmsProperties;
import com.ruoyi.sms.core.*;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 短信配置类
 *
 * <AUTHOR> Li
 * @version 4.2.0
 */
@Configuration
public class SmsConfig {

    @Configuration
    @ConditionalOnClass(com.aliyun.dysmsapi20170525.Client.class)
    static class AliyunSmsConfig {

        @Bean
        public SmsTemplate aliyunSmsTemplate(SmsProperties smsProperties) {
            return new AliyunSmsTemplate(smsProperties);
        }

    }

    @Configuration
    @ConditionalOnProperty(value = "sms.enabled", havingValue = "false")
    @ConditionalOnClass(com.tencentcloudapi.sms.v20190711.SmsClient.class)
    static class TencentSmsConfig {

        @Bean
        public SmsTemplate tencentSmsTemplate(SmsProperties smsProperties) {
            return new TencentSmsTemplate(smsProperties);
        }

    }

    @Configuration
    @ConditionalOnClass(com.aliyun.dypnsapi20170525.Client.class)
    static class AliyunPnsConfig {
        @Bean
        public PnsTemplate aliyunPnsTemplate(SmsProperties smsProperties) {
            return new AliyunPnsTemplate(smsProperties);
        }

    }
}
