package com.ruoyi.sms.core;

import com.aliyun.dypnsapi20170525.Client;
import com.aliyun.dypnsapi20170525.models.GetMobileRequest;
import com.aliyun.dypnsapi20170525.models.GetMobileResponse;
import com.aliyun.teaopenapi.models.Config;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.sms.config.properties.SmsProperties;
import com.ruoyi.sms.entity.SmsResult;
import lombok.SneakyThrows;

public class AliyunPnsTemplate implements PnsTemplate {

    private SmsProperties properties;
    private Client client;

    @SneakyThrows(Exception.class)
    public AliyunPnsTemplate(SmsProperties smsProperties) {
        this.properties = smsProperties;
        Config config = new Config()
            // 必填，您的 AccessKey ID
            .setAccessKeyId(properties.getAccessKeyId())
            // 必填，您的 AccessKey Secret
            .setAccessKeySecret(properties.getAccessKeySecret())
            // 访问的域名
            .setEndpoint("dypnsapi.aliyuncs.com");
        this.client = new Client(config);
    }


    @Override
    public SmsResult getMobile(String token) {
        GetMobileRequest getMobileRequest = (new GetMobileRequest()).setAccessToken(token);
        try {
            GetMobileResponse response = client.getMobile(getMobileRequest);
            return SmsResult.builder()
                .isSuccess("OK".equals(response.getBody().getCode()))
                .message(response.getBody().getMessage())
                .response(response.body.getMobileResultDTO.getMobile())
                .build();
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }
}
