package com.ruoyi.common.enums;

public enum DetailType {

    /**
     * 账单类型
     * 1 = 文字聊天
     * 2 = 发送图片
     */
    TEXT("1", "发送文字"),
    IMAGE("2", "发送图片"),
    HONGBAO("3", "发送红包"),
    RECEIVE_HONGBAO("4", "收红包");
    private final String code;
    private final String info;

    DetailType(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }

}
