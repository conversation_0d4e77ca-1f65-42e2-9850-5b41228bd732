package com.ruoyi.common.enums;

/**
 * 查找状态
 *
 * <AUTHOR>
 */
public enum LikeStatus {

    /**
     * 0 = 超级喜欢/关注
     * 1 = 喜欢
     * 2 = 不喜欢
     */
    SUPER_LIKE("0", "超级喜欢"), LIKE("1", "喜欢"), DISLIKE("2", "不喜欢"), BLOCK("3", "拉黑"),TOPIC("4", "话题");

    private final String code;
    private final String info;

    LikeStatus(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }
}
