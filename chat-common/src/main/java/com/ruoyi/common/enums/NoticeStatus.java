package com.ruoyi.common.enums;

/**
 * 通知操作类型
 *
 * <AUTHOR>
 */
public enum NoticeStatus {
    /**
     * 0 =  系统通知
     * 1 =  社交嘀嘀
     */
    SYSTEM("0", "系统通知"),
    TEAM("1", "社交嘀嘀");

    private final String code;
    private final String info;

    NoticeStatus(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }
}
