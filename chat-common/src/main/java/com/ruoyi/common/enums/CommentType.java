package com.ruoyi.common.enums;

/**
 * 动态类型
 *
 * <AUTHOR>
 */
public enum CommentType {
    /**
     * 0 =  动态点赞
     * 1 =  评论点赞
     */
    DYNAMIC("0", "动态点赞"), COMMENT("1", "评论点赞");

    private final String code;
    private final String info;

    CommentType(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }
}
