package com.ruoyi.common.enums;

/**
 * 用户状态
 *
 * <AUTHOR>
 */
public enum DynamicStatus {
    /**
     * 0 =  展示
     * 1 = 不展示
     * 2 = 待审核
     */
    OK("0", "展示"), DISABLE("1", "不展示"), CHECK("2", "待审核");

    private final String code;
    private final String info;

    DynamicStatus(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }
}
