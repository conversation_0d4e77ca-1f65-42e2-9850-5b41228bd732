package com.ruoyi.common.enums;

/**
 * 动态类型
 *
 * <AUTHOR>
 */
public enum DynamicType {
    /**
     * 1 =  图文动态
     * 2 =  视频动态
     */
    IMAGE("1", "图文动态"), VIDEO("2", "视频动态");

    private final String code;
    private final String info;

    DynamicType(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }
}
