package com.ruoyi.common.enums;

public enum TargetTypeStatus {

    /**
     * 0 = 所有人可见
     * 1 = 相同目的可见
     * 2 = 不可见
     */
    ONE("0", "所有人可见"), TWO("1", "相同目的可见"), THREE("2", "不可见");

    private final String code;
    private final String info;

    TargetTypeStatus(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }

    public static String getInfo(String code) {
        String msg = null;
        if (ONE.code.equals(code)) {
            msg = ONE.getInfo();
        }
        if (TWO.code.equals(code)) {
            msg = TWO.getInfo();
        }
        if (THREE.code.equals(code)) {
            msg = THREE.getInfo();
        }
        return msg;
    }

}
