package com.ruoyi.common.enums;

/**
 * 通知操作类型
 *
 * <AUTHOR>
 */
public enum NoticType {
    /**
     * 1 =  点赞
     * 2 =  评论
     */
    LIKE("1", "点赞"),
    COMMENT("2", "评论"),
    VIP("3", "vip");

    private final String code;
    private final String info;

    NoticType(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }
}
