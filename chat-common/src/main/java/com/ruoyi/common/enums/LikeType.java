package com.ruoyi.common.enums;

/**
 * 查找状态
 *
 * <AUTHOR>
 */
public enum LikeType {

    /**
     * 0 = 我喜欢的用户列表
     * 1 = 喜欢我的用户列表
     * 2 = 不喜欢的用户列表
     */
    LIKE("0", "我喜欢的用户列表"), FANS("1", "喜欢我的用户列表"), DISLIKE("2", "不喜欢用户列表");

    private final String code;
    private final String info;

    LikeType(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }
}
