package com.ruoyi.common.enums;

/**
 * 群身份类型
 *
 * <AUTHOR>
 */
public enum GroupUser {
    /**
     * 1 =  群主
     * 2 =  群管理员
     * 3 =  群成员
     */
    MASTER("1", "群主"), ADMIN("2", "群管理员"), MEMBER("3", "群成员");

    private final String code;
    private final String info;

    GroupUser(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }
}
