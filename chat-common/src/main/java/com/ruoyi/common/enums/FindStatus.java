package com.ruoyi.common.enums;

/**
 * 查找状态
 *
 * <AUTHOR>
 */
public enum FindStatus {

    /**
     * 1 = 查找用户
     * 2 = 查找群组
     */
    USER(1, "查找用户"), GROUP(2, "查找群组");

    private final Integer code;
    private final String info;

    FindStatus(Integer code, String info) {
        this.code = code;
        this.info = info;
    }

    public Integer getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }
}
