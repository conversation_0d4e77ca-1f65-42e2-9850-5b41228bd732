package com.ruoyi.common.constant;

/**
 * 通用常量信息
 *
 * <AUTHOR>
 */
public interface Constants {

    /**
     * UTF-8 字符集
     */
    String UTF8 = "UTF-8";

    /**
     * GBK 字符集
     */
    String GBK = "GBK";

    /**
     * www主域
     */
    String WWW = "www.";

    /**
     * http请求
     */
    String HTTP = "http://";

    /**
     * https请求
     */
    String HTTPS = "https://";

    /**
     * 动态发布时间定义
     */
    String JUST = "just";

    /**
     * 通用成功标识
     * 通用正常标识
     * 通用已处理标记
     * 通用推荐标记
     * 通用生效标记
     * 只看同城
     * 状态标记字段，根据不同的字段展示同的含义
     */
    String SUCCESS = "0";

    /**
     * 通用失败标识
     * 通用停止标识
     * 通用禁用标识
     * 通用未处理标记
     * 通用不推荐标记
     * 通用不生效标记
     * 通用禁言标记
     * 同城不限制
     * 状态标记字段，根据不同的字段展示同的含义
     */
    String FAIL = "1";

    /**
     * 通用标记
     * 账号注销
     * 发送验证码，其他场景
     * 好友关系
     */
    String TWO = "2";


    /**
     * 常量通配符
     */
    String THREE = "3";

    /**
     * 发送验证码，忘记密码场景
     */
    Integer FORGET = 1;

    /**
     * 未关注
     */
    String STRANG = "0";
    /**
     * 已关注
     */
    String LIKE = "1";

    /**
     * 信息阅读标记[未读]
     */
    String MSG_UNREAD = "0";

    /**
     * 信息阅读标记[已读]
     */
    String MSG_READ = "1";

    /**
     * 令牌
     */
    String TOKEN = "token";

    /**
     * 环信TOKEN
     */
    String EASEMOB_TOKEN = "easemob_token";

    /**
     * 用户
     */
    String USER_INFO = "user";

    /**
     * 登录成功
     */
    String LOGIN_SUCCESS = "Success";

    /**
     * 注销
     */
    String LOGOUT = "Logout";

    /**
     * 注册
     */
    String REGISTER = "Register";

    /**
     * 登录失败
     */
    String LOGIN_FAIL = "Error";

    /**
     * 环信用户名前缀
     */
    String USER = "user-";

    /**
     * 创建账号的默认头像
     */
    String AVATAR = "http://heike-chat.oss-cn-shanghai.aliyuncs.com/2023/02/28/f948a04cb16e47c0952ead97d9f478a0.png";

    /**
     * 环信用户统一密码
     */
    String PASSWORD = "hxchat524";

    /**
     * 环信系统通知账号
     */
    String SYSTEM_NAME = "emsystemnotificationid";

    /**
     * 系统内通知账号定义
     */
    String APP_SYSTEM_NAME = "hkSystemNoticeid";


    /**
     * 发送短信使用的阿里云模板编号
     */
    String TEMPLATE_CODE = "SMS_150475386";

    /**
     * 验证码有效期（分钟）
     */
    Integer CAPTCHA_EXPIRATION = 1;

    /**
     * 短信验证码有效时间(分钟)
     */
    Integer APP_CAPTCHA_EXPIRATION = 5;

    /**
     * 环信APPTOKEN缓存时间
     */
    Integer APP_TOKEN_KEY = 10080;

    /**
     * 用户地址的缓存时间[3天]
     */
    Integer USER_ADDRESS_TIME = 3 * 24 * 60;

    /**
     * 用户环信Token缓存时间[1小时]
     */
    Integer APP_USER_EASEMOB_TIME = 59;

    /**
     * EMAIL正则表达式
     */
    String EMAIL_REGEX = "^\\S+@\\S+\\.\\S+$";
}

