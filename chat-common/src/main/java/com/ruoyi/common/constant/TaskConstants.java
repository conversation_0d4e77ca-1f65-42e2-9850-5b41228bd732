package com.ruoyi.common.constant;

public interface TaskConstants {


    String TASK = "task:";

    //-------------------------用户取消发布任务------------------------
    //用户和发布者取消参与任务
    String cancelJoin = "cancel_join";
    //每天用户和发布者取消加入任务免费次数
    int cancelJoinNumber = 1;
    //限制用户次数KEY
    String cancelJoinKey = TASK + cancelJoin + ":";


    //-------------------------发布者取消用户加入任务-------------------
    //用户和发布者取消用户参与任务
    String adminCancelJoin = "admin_cancel_join";
    //每天用户和发布者取消加入任务免费次数
    int adminCancelJoinNumber = 1;
    //限制用户次数KEY
    String adminCancelJoinKey = TASK + adminCancelJoin + ":";


    //------------------------发布者取消发布任务-----------------------
    //发布者取消发布的任务
    String adminCancelTeam = "cancel_team";
    //发布者取消发布任务免费次数
    int adminCancelTeamNumber = 1;
    //限制用户次数KEY
    String adminCancelTeamKey = TASK + adminCancelTeam + ":";


    //------------------------用户签到任务---------------------------
    //用户签到用户
    String userSign = "user_sign";

    //限制用户次数KEY
    String userSignKey = TASK + userSign + ":";

    //------------------------实名认证---------------------------
    String userCert = "cert";

    //------------------------未完成任务---------------------------
    String incomplete = "incomplete";

    //------------------------完成任务---------------------------
    String complete = "complete";

    //------------------------邀请好友---------------------------
    String invite = "invite";

    //------------------------评论---------------------------
    String teamComment = "team_comment";
    //任务每日生效次数
    Integer teamCommentNumber = 3;
    //任务缓存KEY
    String teamCommentKey = TASK + teamComment + ":";


    //------------------------点赞---------------------------
    String teamLike = "team_like";
    //任务每日生效次数
    Integer teamLikeNumber = 3;
    //任务缓存KEY
    String teamLikeKey = TASK + teamLike + ":";

    //------------------------发布动态---------------------------
    String createDynamic = "create_dynamic";
    //任务每日生效次数
    Integer createDynamicNumber = 3;
    //任务缓存KEY
    String createDynamicKey = TASK + createDynamic + ":";

    //------------------------扫码速配---------------------------
    String userMate = "user_mate";
    //任务每日生效次数
    Integer userMateNumber = 3;
    //任务缓存KEY
    String userMateKey = TASK + userMate + ":";


    //------------------------打招呼---------------------------
    String userPhrases = "phrases";
    //任务每日生效次数
    Integer userPhrasesNumber = 3;
    //任务缓存KEY
    String userPhrasesKey = TASK + userPhrases + ":";

    //------------------------右滑喜欢---------------------------
    String userLike = "user_like";
    Integer userLikeNumber = 5;
    //任务缓存KEY
    String userLikeKey = TASK + userLike + ":";

}
