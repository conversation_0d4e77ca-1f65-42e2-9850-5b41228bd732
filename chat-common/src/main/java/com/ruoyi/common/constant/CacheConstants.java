package com.ruoyi.common.constant;

/**
 * 缓存的key 常量
 *
 * <AUTHOR>
 */
public interface CacheConstants {

    /**
     * 登录用户 redis key
     */
    String LOGIN_TOKEN_KEY = "Authorization:login:token:";

    /**
     * 在线用户 redis key
     */
    String ONLINE_TOKEN_KEY = "online_tokens:";

    /**
     * 短信验证码 redis key
     */
    String CAPTCHA_CODE_KEY = "captcha_codes:";


    /**
     * 本地划块验证码 redis key
     */
    String LOCAL_CAPTCHA_CODE_KEY = "local_captcha_codes:";

    /**
     * 参数管理 cache key
     */
    String SYS_CONFIG_KEY = "sys_config:";

    /**
     * 字典管理 cache key
     */
    String SYS_DICT_KEY = "sys_dict:";

    /**
     * 防重提交 redis key
     */
    String REPEAT_SUBMIT_KEY = "repeat_submit:";

    /**
     * 限流 redis key
     */
    String RATE_LIMIT_KEY = "rate_limit:";

    /**
     * 登录账户密码错误次数 redis key
     */
    String PWD_ERR_CNT_KEY = "pwd_err_cnt:";

    /**
     * 昨天用户总数
     */
    String USER_SUM = "app:sum";


    //---------------------APP缓存KEY信息-------------------------------

    /**
     * 短信验证码 redis key
     */
    String APP_CAPTCHA_CODE_KEY = "app:captcha_codes:";

    /**
     * 短信验证码 redis key
     */
    String APP_NOTICE = "app:notice:";


    /**
     * 用户环信TOKEN redis key
     */
    String APP_USER_EASEMOB = "app:user_easemob:";

    /**
     * 用户好友申请列表缓存
     */
    String APP_USER_FRIEND = "app:user_friend:";

    /**
     * 用户
     */
    String TOKEN_KEY = "app:token";

    /**
     * 用户每日超级喜欢次数限制
     */
    String SUPER_LIKE = "app:superLike:";

    /**
     * 普通用户每日右滑次数KEY
     */
    String DAY_LIKE_KEY = "app:like:number:";

    /**
     * 普通用户被喜欢每日通知次数key
     */
    String DAY_LIKE_PUSH = "app:likePush:";

    /**
     * 普通用户被喜欢每日推送通知最大次数
     */
    int DAY_LIKE_PUSH_MAX = 2;

    /**
     * 普通用户每日右滑次数限制
     */
    Integer DAY_LIKE_NUMBER = 99;


    /**
     * 缓存注销用户的键
     */
    String LOGIN_OUT = "app:loginOut:";

    /**
     * 缓存注销用户时间[天]
     */
    Integer LOGIN_OUT_DAY = 15;

    /**
     * 首页用户缓存
     */
    String APP_INDEX_USERS = "app:users:";

}
