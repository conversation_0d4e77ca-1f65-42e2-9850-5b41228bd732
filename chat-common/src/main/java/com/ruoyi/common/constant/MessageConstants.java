package com.ruoyi.common.constant;

/**
 * 用户和发送消息常量信息
 *
 * <AUTHOR>
 */
public interface MessageConstants {


    /**
     * 创建群组自定义消息类型
     */
    String ADD_GROUP_TYPE = "add_group_message";

    String UPDATE_GROUP_NAME = "update_group_name_message";

    String UPDATE_GROUP_DESC = "update_group_description_message";


    /**
     * 动态点赞类型
     */
    String DYNAMIC_PUSH_TYPE = "dynamic_push_message";

    /**
     * 消息推送类型
     */
    String AGREE_PUSH_TYPE = "agree_push_message";

    /**
     * 动态评论类型
     */
    String DYNAMIC_COMMENT_PUSH_TYPE = "comment_push_message";

    /**
     * 喜欢/超级喜欢推送类型
     */
    String LIKE_PUSH_TYPE = "like_push_message";

    /**
     * 发送打招呼推送类型
     */
    String SUPER_LIKE = "super_like_push_message";

    /**
     * 用户长时间不登录发送的推广离线消息
     */
    String OFF_LINE_PUSH = "off_line_user_message";

    /**
     * 添加好友成功提示
     */
    String ADD_USER = "添加好友成功，现在我们可以开始聊天了";

}

