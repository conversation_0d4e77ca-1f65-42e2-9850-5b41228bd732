package com.ruoyi.common.constant;

public interface NoticeConstants {

    //完成任务
    String DOWN = "您发布的\"%\"活动已完成";

    //通过了任务
    String PASS = "您发布的\"%\"活动已通过";

    //拒绝了任务
    String REFUSE = "您发布的\"%\"活动已驳回";

    //同意请求
    String USER_PASS = "您报名的\"%\"活动已同意约会";

    //拒绝请求
    String USER_REFUSE = "您报名的\"%\"活动拒绝了您的申请";

    //解除约会
    String USER_SECURE = "您报名的\"%\"活动解除了与您的约会";


    //用户申请活动
    String USER_APPLY = "报名申请您发布的\"%\"活动";

    //用户取消参加活动
    String USER_CANCEL_APPLY = "取消参加了您发布的\"%\"活动";

    //取消发布的任务
    String CANCEL = "您报名的\"%\"活动已被取消";


    //新用户第一次登录系统推送消息
    interface NEW_USER_MESSAGE {
        String URL = "https://share.ziyoushidai.net/#/pages/rule/guide";
        String CONTEXT = "快速玩转黑客";
        String IMAGE = "http://heike-chat.oss-cn-shanghai.aliyuncs.com/2023/09/11/5608787f69124339b1a9909624ee4fcc.png";
    }

}
