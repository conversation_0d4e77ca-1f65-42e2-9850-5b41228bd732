//package com.ruoyi.common.utils.http;
//
//
//import org.springframework.http.HttpEntity;
//import sun.net.www.http.HttpClient;
//
//import java.io.IOException;
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Map;
//
//public class HttpUtil {
//    private static final CloseableHttpClient httpclient = HttpClients.createDefault();
//
//    /**
//     * 发送HttpGet请求
//     *
//     * @param url
//     * @return
//     */
//    public static String sendGet(String url) {
//        HttpGet httpget = new HttpGet(url);
//        CloseableHttpResponse response = null;
//        try {
//            response = httpclient.execute(httpget);
//        } catch (IOException e1) {
//            e1.printStackTrace();
//        }
//        String result = null;
//        try {
//            HttpEntity entity = response.getEntity();
//            if (entity != null) {
//                result = EntityUtils.toString(entity);
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        } finally {
//            try {
//                response.close();
//            } catch (IOException e) {
//                e.printStackTrace();
//            }
//        }
//        return result;
//    }
//
//    /**
//     * 发送HttpPost请求，参数为map
//     *
//     * @param url
//     * @param map
//     * @return
//     */
//    public static String sendPost(String url, Map<String, String> map) {
//        List<NameValuePair> formparams = new ArrayList<NameValuePair>();
//        for (Map.Entry<String, String> entry : map.entrySet()) {
//            formparams.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
//        }
//        UrlEncodedFormEntity entity = new UrlEncodedFormEntity(formparams, Consts.UTF_8);
//        HttpPost httppost = new HttpPost(url);
//        httppost.setEntity(entity);
//        CloseableHttpResponse response = null;
//        try {
//            response = httpclient.execute(httppost);
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//        HttpEntity entity1 = response.getEntity();
//        String result = null;
//        try {
//            result = EntityUtils.toString(entity1);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return result;
//    }
//
//    /**
//     * 发送不带参数的HttpPost请求
//     *
//     * @param url
//     * @return
//     */
//    public static String sendPost(String url) {
//        HttpPost httppost = new HttpPost(url);
//        CloseableHttpResponse response = null;
//        try {
//            response = httpclient.execute(httppost);
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//        HttpEntity entity = response.getEntity();
//        String result = null;
//        try {
//            result = EntityUtils.toString(entity);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return result;
//    }
//
//    public static String doPost2(String url, JSONObject param) {
//        HttpPost httpPost = null;
//        String result = null;
//        try {
//            HttpClient client = new DefaultHttpClient();
//            httpPost = new HttpPost(url);
//            if (param != null) {
//                StringEntity se = new StringEntity(param.toString(), "utf-8");
//                httpPost.setEntity(se); // post方法中，加入json数据
//                httpPost.setHeader("Content-Type", "application/json");
//                httpPost.setHeader("Authorization", param.getString("authorization"));
//            }
//
//            HttpResponse response = client.execute(httpPost);
//            if (response != null) {
//                HttpEntity resEntity = response.getEntity();
//                if (resEntity != null) {
//                    result = EntityUtils.toString(resEntity, "utf-8");
//                }
//            }
//
//        } catch (Exception ex) {
//            ex.printStackTrace();
//        }
//        return result;
//    }
//}
//
