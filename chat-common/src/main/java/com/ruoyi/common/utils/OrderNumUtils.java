package com.ruoyi.common.utils;

import java.text.SimpleDateFormat;
import java.util.Date;

public class OrderNumUtils {
    private static int count = 0;

    private static final int total = 9999;

    private static String now = null;

    private static final SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");

    private static OrderNumUtils generateOrderNum = null;

    public static synchronized OrderNumUtils getInstance() {
        if (generateOrderNum == null) {
            generateOrderNum = new OrderNumUtils();
        }
        return generateOrderNum;
    }

    private static String getNowDateStr() {
        /* 43 */
        return sdf.format(new Date());
        /*    */
    }

    public synchronized String generateOrderNo() {
        String dataStr = getNowDateStr();
        if (dataStr.equals(now)) {
            count++;
        } else {
            count = 1;
            now = dataStr;
        }
        int countInteger = String.valueOf(9999).length() - String.valueOf(count).length();
        String bu = "";
        for (int i = 0; i < countInteger; i++)
            bu = bu + "0";
        bu = bu + String.valueOf(count);
        if (count >= 9999)
            count = 0;
        return dataStr + bu;
    }
}

