package com.ruoyi.common.utils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

public class InfoUtils {


    /**
     * 获取年龄工具类
     *
     * @param birthDay
     * @return
     * @throws ParseException
     */
    public static int getAgeByBirth(Date birthDay) {
        int age = 0;
        Calendar cal = Calendar.getInstance();
        if (cal.before(birthDay)) {
            throw new IllegalArgumentException(
                "The birthDay is before Now.It's unbelievable!");
        }
        int yearNow = cal.get(Calendar.YEAR);
        int monthNow = cal.get(Calendar.MONTH);
        int dayOfMonthNow = cal.get(Calendar.DAY_OF_MONTH);
        cal.setTime(birthDay);
        int yearBirth = cal.get(Calendar.YEAR);
        int monthBirth = cal.get(Calendar.MONTH);
        int dayOfMonthBirth = cal.get(Calendar.DAY_OF_MONTH);
        age = yearNow - yearBirth;
        if (monthNow <= monthBirth) {
            if (monthNow == monthBirth) {
                if (dayOfMonthNow < dayOfMonthBirth) {
                    age--;
                }
            } else {
                age--;
            }
        }
        return age;
    }

    public static int getAge(long birthDay) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy");
        String formatYear = simpleDateFormat.format(birthDay);
        String nowYear = simpleDateFormat.format(System.currentTimeMillis());
        return Integer.parseInt(nowYear) - Integer.parseInt(formatYear);
    }

    /**
     * 获取星座
     *
     * @param birthday 生日时间戳(毫秒)
     * @return
     */
    public static String getConstellation(long birthday) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("Mdd");
        Date birthdayDate = new Date(birthday);
        String format = simpleDateFormat.format(birthdayDate);
        int date = Integer.parseInt(format);
        if (date >= 121 && date <= 219) {
            return "水瓶座";
        }
        if (date >= 220 && date <= 320) {
            return "双鱼座";
        }
        if (date >= 321 && date <= 420) {
            return "白羊座";
        }
        if (date >= 421 && date <= 521) {
            return "金牛座";
        }
        if (date >= 522 && date <= 621) {
            return "双子座";
        }
        if (date >= 622 && date <= 722) {
            return "巨蟹座";
        }
        if (date >= 723 && date <= 823) {
            return "狮子座";
        }
        if (date >= 824 && date <= 923) {
            return "处女座";
        }
        if (date >= 924 && date <= 1023) {
            return "天秤座";
        }
        if (date >= 1024 && date <= 1122) {
            return "天蝎座";
        }
        if (date >= 1123 && date <= 1221) {
            return "射手座";
        }
        return "摩羯座";
    }
}
