package com.ruoyi.common.utils;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.security.SecureRandom;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class RandomNameUtils {

    // 仅包含数字和小写字母
    private static final String CHAR_POOL = "0123456789abcdefghijklmnopqrstuvwxyz";
    private static final String CHAR_ACTERS = "123456789abcdefghijklmnpqrstuvwxyz";
    private static final SecureRandom random = new SecureRandom();

    /**
     * 生成随机用户名
     *
     * @return
     */
    public static String generate() {
        int length = 6; // 依然保持1-8位长度
        StringBuilder sb = new StringBuilder();

        for (int i = 0; i < length; i++) {
            int index = random.nextInt(CHAR_POOL.length());
            sb.append(CHAR_POOL.charAt(index));
        }
        return sb.toString();
    }

    /**
     * 生成随机8位用户ID
     *
     * @return
     */
    public static long generateId() {
        long id = random.nextInt(9) + 1; // 第一位1-9
        for (int i = 0; i < 7; i++) {
            id = id * 10 + random.nextInt(10);
        }
        return id;
    }

    public static String generateRandom() {
        // 随机生成长度（6到20位）
        int length = 6 + random.nextInt(15); // 6 + (0到14) = 6到20
        StringBuilder sb = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            // 随机选择字符集中的字符
            int index = random.nextInt(CHAR_ACTERS.length());
            sb.append(CHAR_ACTERS.charAt(index));
        }
        return sb.toString();
    }
}
