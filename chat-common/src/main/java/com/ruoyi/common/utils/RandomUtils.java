package com.ruoyi.common.utils;

import java.util.Random;
import java.util.concurrent.ThreadLocalRandom;

public class RandomUtils {


    /**
     * 获取一个随机数
     *
     * @param min 最小数(包含在值内)
     * @param max 最大数(不包含在值内)
     * @return
     */
    public static int number(int min, int max) {
        return ThreadLocalRandom.current().nextInt(min, max);
    }

    /**
     * 获取一个随机数
     * 获取的随机数不包含本身
     *
     * @return
     */
    public static int number(int max) {
        Random r = new Random();
        return r.nextInt(max);
    }

}
