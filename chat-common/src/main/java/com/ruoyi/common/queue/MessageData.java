package com.ruoyi.common.queue;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;

/**
 * 消息实体
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class MessageData implements Comparable<MessageData> {


    /**
     * 消息类型ID
     */
    private Integer id;

    /**
     * 消息头
     */
    private String title;

    /**
     * 消息数据
     */
    private String data;

    /**
     * 描述备注
     */
    private String remark;


    @Override
    public int compareTo(@NotNull MessageData data) {
        return getId().compareTo(data.getId());
    }

}
