package com.ruoyi.common.core.domain.model;

import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 用户第三方登录对象
 *
 * <AUTHOR>
 */

@Data
public class ThirdLoginBody {

    /**
     * 手机号码
     */
    @NotBlank(message = "手机号码不能为空", groups = EditGroup.class)
    @Pattern(regexp = "^1\\d{10}$", message = "手机号码格式错误!", groups = {AddGroup.class, EditGroup.class})
    private String phonenumber;

    /**
     * 短信验证码
     */
    private String code;

    /**
     * 第三方TOKEN
     */
    @NotBlank(message = "Token不能为空", groups = AddGroup.class)
    private String token;

    /**
     * 第三方登录方式
     */
    @NotBlank(message = "第三方类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private String type;

    /**
     * 是否第一次登录
     */
    private Boolean firstBoolean = false;

}
