package com.ruoyi.common.core.service;

import javax.servlet.http.HttpServletRequest;

/**
 * 通用 系统访问日志
 *
 * <AUTHOR> <PERSON>
 */
public interface LogininforService {

    /**
     * 后台系统系统访问日志
     * @param username
     * @param status
     * @param message
     * @param request
     * @param args
     */
    void recordLogininfor(String username, String status, String message,
                          HttpServletRequest request, Object... args);

    /**
     * APP系统访问日志
     * @param username
     * @param status
     * @param message
     * @param request
     * @param args
     */
    void appRecordLogininfor(String username, String status, String message,
                          HttpServletRequest request, Object... args);
}
