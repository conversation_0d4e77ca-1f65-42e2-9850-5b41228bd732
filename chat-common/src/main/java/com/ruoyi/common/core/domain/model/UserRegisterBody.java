package com.ruoyi.common.core.domain.model;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 用户注册对象
 *
 * <AUTHOR> Li
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserRegisterBody extends LoginBody {


    /**
     * 性别
     */
    @NotBlank(message = "{user.sex.not.blank}")
    private String sex;

    /**
     * 生日
     */
    @NotNull(message = "{user.sex.not.birthday}")
    private Long birthday;

    /**
     * 邮箱||手机号
     */
    private String otherInfo;

    /**
     * 用户目标语言
     */
    private String userLanguage;
}
