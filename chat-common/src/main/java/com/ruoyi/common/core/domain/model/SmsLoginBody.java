package com.ruoyi.common.core.domain.model;

import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 短信登录对象
 *
 * <AUTHOR> Li
 */

@Data
public class SmsLoginBody {

    /**
     * 用户名
     */
    @NotBlank(message = "{user.phonenumber.not.blank}")
    @Length(min = 11, max = 11, message = "{user.mobile.phone.number.not.valid}")
    @Pattern(regexp = "^1\\d{10}$", message = "手机号码格式错误!")
    private String phonenumber;

    /**
     * 验证码
     */
    @NotBlank(message = "{sms.code.not.blank}")
    @Length(min = 4, max = 4, message = "{sms.code.check.not.blank}")
    private String smsCode;

}
