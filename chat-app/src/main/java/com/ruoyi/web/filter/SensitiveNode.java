package com.ruoyi.web.filter;

import java.io.Serializable;
import java.util.TreeSet;

public class SensitiveNode implements Serializable {
  private static final long serialVersionUID = 1L;

  protected final int headTwoCharMix;

  protected final TreeSet<StringPointer> words = new TreeSet<>();

  protected SensitiveNode next;

  public SensitiveNode(int headTwoCharMix) {
    this.headTwoCharMix = headTwoCharMix;
  }

  public SensitiveNode(int headTwoCharMix, SensitiveNode parent) {
    this.headTwoCharMix = headTwoCharMix;
    parent.next = this;
  }
}
