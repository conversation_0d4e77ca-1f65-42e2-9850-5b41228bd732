package com.ruoyi.web.controller.app;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ruoyi.app.domain.bo.AppVipTypeBo;
import com.ruoyi.app.domain.vo.AppUserVipVo;
import com.ruoyi.app.domain.vo.AppVipTypeVo;
import com.ruoyi.app.service.IAppLevelService;
import com.ruoyi.app.service.IAppUserVipService;
import com.ruoyi.app.service.IAppVipTypeService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.service.ISysConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.Objects;

/**
 * APPVIP类型
 *
 * <AUTHOR>
 * @date 2023-03-16
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/vipType")
public class AppVipTypeController extends BaseController {

    private final IAppLevelService appLevelService;
    private final ISysConfigService sysConfigService;
    private final ISysConfigService iSysConfigService;
    private final IAppVipTypeService iAppVipTypeService;
    private final IAppUserVipService iAppUserVipService;


    /**
     * 查询后台VIP配置
     */
    @GetMapping("/config")
    public R<HashMap<String, Boolean>> config() {
        HashMap<String, Boolean> hashMap = new HashMap<>(1);
        boolean vip = Boolean.parseBoolean(iSysConfigService.selectConfigByKey("app.vip"));
        hashMap.put("vipSetting", vip);
        return R.ok(hashMap);
    }

    /**
     * 查询APPVIP类型列表
     */
    @GetMapping("/list")
    public R<HashMap<String, Object>> list() {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("vipList", iAppVipTypeService.queryList(new AppVipTypeBo()));
        AppUserVipVo appUserVipVo = iAppUserVipService.queryByUserId(getUserId());
        if (Objects.nonNull(appUserVipVo)) {
            hashMap.put("isVip", appUserVipVo.getEndTime() > System.currentTimeMillis());
            hashMap.put("vipLevel", appUserVipVo.getLevel());
            hashMap.put("vipExperience", appUserVipVo.getExperience());
            hashMap.put("vipEndTime", appUserVipVo.getEndTime());
            if (appUserVipVo.getLevel() < 10) {
                hashMap.put("nextLevel", appLevelService.queryById(appUserVipVo.getLevel() + 1L));
            }
        } else {
            hashMap.put("isVip", false);
            hashMap.put("vipLevel", 0);
            hashMap.put("vipExperience", 0);
            hashMap.put("vipEndTime", 0);
            hashMap.put("nextLevel", appLevelService.queryById(1L));
        }
        hashMap.put("image", sysConfigService.selectConfigByKey("app.chat.group"));
        return R.ok(hashMap);
    }

    /**
     * 获取APPVIP类型详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("app:vipType:query")
    @GetMapping("/{id}")
    public R<AppVipTypeVo> getInfo(@NotNull(message = "主键不能为空")
                                   @PathVariable Long id) {
        return R.ok(iAppVipTypeService.queryById(id));
    }
}
