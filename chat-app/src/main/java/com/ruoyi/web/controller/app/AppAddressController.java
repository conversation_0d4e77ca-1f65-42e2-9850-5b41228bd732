package com.ruoyi.web.controller.app;

import com.ruoyi.app.domain.bo.AppAddressBo;
import com.ruoyi.app.domain.vo.AppAddressVo;
import com.ruoyi.app.domain.vo.AppCityDistVo;
import com.ruoyi.app.service.IAppAddressService;
import com.ruoyi.app.service.IAppCityDistService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * APP用户地址信息
 *
 * <AUTHOR>
 * @date 2022-12-21
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/address")
public class AppAddressController extends BaseController {

    private final IAppAddressService appAddressService;
    private final IAppCityDistService iAppCityDistService;

    /**
     * 查询APP用户地址信息
     */
    @GetMapping()
    public R<Object> info() {
        AppAddressBo appAddressBo = new AppAddressBo();
        appAddressBo.setUserId(getUserId());
        return R.ok(appAddressService.queryList(appAddressBo));
    }

    /**
     * 新增/修改APP用户地址信息
     */
    @Log(title = "APP用户地址信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Object> saveOrUpdate(@Validated(AddGroup.class) AppAddressBo bo) {
        bo.setUserId(getUserId());
        if (StringUtils.isNotBlank(bo.getCityCode())) {
            String city = resolveCityCode(bo.getCityCode());
            if (StringUtils.isNotBlank(city)) {
                bo.setCity(city);
            }
        }
        return R.ok(appAddressService.saveOrUpdate(bo));
    }

    /**
     * 获取默认地址
     *
     * @return
     */
    @GetMapping("select")
    public R<Object> address() {
        AppAddressVo addressVo;
        addressVo = appAddressService.queryUserId(getUserId(), Constants.SUCCESS);
        if (Objects.isNull(addressVo)) {
            AppAddressBo address = new AppAddressBo();
            address.setUserId(getUserId());
            List<AppAddressVo> appAddressVos = appAddressService.queryList(address);
            if (appAddressVos.size() > 0) {
                addressVo = appAddressVos.get(0);
            }
        }
        return R.ok(addressVo);
    }

    /**
     * 修改用户默认收获地址状态
     */
    @Log(title = "修改用户默认收获地址状态", businessType = BusinessType.UPDATE)
    @PostMapping("selectAddress")
    public R<AppAddressVo> selectAddress(
        @RequestParam(name = "id") Long id
    ) {
        AppAddressVo addressVo = appAddressService.queryById(id);
        if (Objects.isNull(addressVo)) {
            return R.fail("地址不存在!");
        }
        AppAddressVo nowAddress = appAddressService.queryUserId(getUserId(), Constants.SUCCESS);
        if (Objects.nonNull(nowAddress)) {
            nowAddress.setStatus(Constants.FAIL);
            appAddressService.updateByVo(nowAddress);
        }
        addressVo.setStatus(Constants.SUCCESS);
        appAddressService.updateByVo(addressVo);
        return R.ok();
    }


    /**
     * 删除用户地址
     */
    @Log(title = "删除用户地址", businessType = BusinessType.DELETE)
    @PostMapping("delect")
    public R<AppAddressVo> delect(
        @RequestParam(name = "id") Long id
    ) {
        AppAddressVo addressVo = appAddressService.queryById(id);
        if (Objects.isNull(addressVo)) {
            return R.fail("地址不存在!");
        }
        ArrayList<Long> ids = new ArrayList<>();
        ids.add(id);
        return R.result(appAddressService.deleteWithValidByIds(ids, true));
    }


    /**
     * 解析城市代码
     *
     * @param cityCode 城市代码
     * @return
     */
    public String resolveCityCode(String cityCode) {
        String city = "";
        String[] idsString = cityCode.split(",");
        for (String e : idsString) {
            if (StringUtils.isNotBlank(e)) {
                Long id = Long.valueOf(e);
                AppCityDistVo appCityDistVo = iAppCityDistService.queryById(id);
                if (Objects.nonNull(appCityDistVo)) {
                    String next = "";
                    if (appCityDistVo.getType().equals(Constants.SUCCESS)) {
                        next = appCityDistVo.getProvince();
                    } else if (appCityDistVo.getType().equals(Constants.FAIL)) {
                        next = appCityDistVo.getCity();
                    } else {
                        next = appCityDistVo.getDistrict();
                    }
                    city += next;
                }
            }
        }
        return city;
    }

}
