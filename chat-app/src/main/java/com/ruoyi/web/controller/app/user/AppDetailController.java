package com.ruoyi.web.controller.app.user;

import java.util.List;
import java.util.Arrays;
import java.util.concurrent.TimeUnit;

import lombok.RequiredArgsConstructor;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;

import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import com.ruoyi.common.core.validate.QueryGroup;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.app.domain.vo.AppDetailVo;
import com.ruoyi.app.domain.bo.AppDetailBo;
import com.ruoyi.app.service.IAppDetailService;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * APP用户账单明细
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/detail")
public class AppDetailController extends BaseController {

    private final IAppDetailService iAppDetailService;

    /**
     * 查询APP用户账单明细列表
     */
    @SaCheckPermission("app:detail:list")
    @GetMapping("/list")
    public TableDataInfo<AppDetailVo> list(AppDetailBo bo, PageQuery pageQuery) {
        return iAppDetailService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出APP用户账单明细列表
     */
    @SaCheckPermission("app:detail:export")
    @Log(title = "APP用户账单明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AppDetailBo bo, HttpServletResponse response) {
        List<AppDetailVo> list = iAppDetailService.queryList(bo);
        ExcelUtil.exportExcel(list, "APP用户账单明细", AppDetailVo.class, response);
    }

    /**
     * 获取APP用户账单明细详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("app:detail:query")
    @GetMapping("/{id}")
    public R<AppDetailVo> getInfo(@NotNull(message = "主键不能为空")
                                  @PathVariable Long id) {
        return R.ok(iAppDetailService.queryById(id));
    }

    /**
     * 新增APP用户账单明细
     */
    @SaCheckPermission("app:detail:add")
    @Log(title = "APP用户账单明细", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AppDetailBo bo) {
        return toAjax(iAppDetailService.insertByBo(bo) ? 1 : 0);
    }
}
