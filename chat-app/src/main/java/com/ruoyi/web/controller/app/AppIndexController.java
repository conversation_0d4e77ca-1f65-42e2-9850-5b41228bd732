package com.ruoyi.web.controller.app;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.app.domain.AppUser;
import com.ruoyi.app.domain.vo.AppFriendRelationVo;
import com.ruoyi.app.domain.vo.AppUserVo;
import com.ruoyi.app.service.IAppFriendRelationService;
import com.ruoyi.app.service.IAppUserService;
import com.ruoyi.app.service.IAppUserVipService;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.MessageUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.redis.RedisUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.Duration;
import java.util.List;

@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app")
public class AppIndexController extends BaseController {


    private final IAppUserService appUserService;
    private final IAppUserVipService appUserVipService;
    private final IAppFriendRelationService appFriendRelationService;


    /**
     * 首页数据
     *
     * @param pageQuery 分页信息
     * @return
     */
    @GetMapping("/index")
    public Object index(PageQuery pageQuery) {
        Long id = getUserId();
        String key = CacheConstants.APP_INDEX_USERS + id;
        AppUserVo userVo = appUserService.queryById(id);
        String sex = userVo.getSex();
        if (StringUtils.equals(sex, Constants.TWO)) {
            return R.fail(MessageUtils.message("user.sex.not.blank"));
        }
        List<AppUserVo> appUserVos = RedisUtils.getCacheObject(key);
        if (ObjectUtils.isEmpty(appUserVos)) {
            if (StringUtils.equals(sex, Constants.SUCCESS)) {
                //查看女生的信息
                LambdaQueryWrapper<AppUser> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(AppUser::getSex, Constants.FAIL);
                wrapper.eq(AppUser::getStatus, Constants.SUCCESS);
                if (StringUtils.isNotBlank(pageQuery.getOrderByColumn())) {
                    wrapper.orderByDesc(AppUser::getCreateTime);
                    wrapper.last("LIMIT 20");
                } else {
                    wrapper.last("ORDER BY RAND() LIMIT 20");
                }
                appUserVos = appUserService.selectList(wrapper);
            } else {
                //查看男生的信息
                LambdaQueryWrapper<AppUser> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(AppUser::getSex, Constants.SUCCESS);
                wrapper.eq(AppUser::getStatus, Constants.SUCCESS);
                wrapper.last("ORDER BY RAND() LIMIT 20");
                appUserVos = appUserService.selectList(wrapper);
            }
        }
        if (StringUtils.equals(sex, Constants.FAIL) && !appUserVos.isEmpty()) {
            RedisUtils.setCacheObject(key, appUserVos, Duration.ofSeconds(DateUtils.seconds()));
        }
        appUserVos.forEach(e -> {
            e.setEmail(null);
            e.setPhone(null);
            e.setPassword(null);
            if (StringUtils.equals(sex, Constants.SUCCESS)) {
                e.setOnlineStatus(null);
            }
            List<AppFriendRelationVo> userCollect = appFriendRelationService.queryList(id, e.getId(), Constants.SUCCESS);
            e.setCollectStatus(!userCollect.isEmpty());
            e.setVipTime(appUserVipService.userVip(e.getId()));
        });
        return TableDataInfo.build(appUserVos, appUserVos.size());
    }


    /**
     * 搜索用户
     *
     * @param nickNam 搜索名称
     * @return
     */
    @GetMapping("/searchUsers")
    public R<?> searchUsers(@RequestParam(name = "nickName") String nickNam) {
        // 获取当前用户ID
        Long userId = getUserId();
        // 获取当前用户信息
        AppUserVo currentUser = appUserService.queryById(userId);
        // 获取当前用户的性别
        String sex = currentUser.getSex();
        // 构建查询条件
        LambdaQueryWrapper<AppUser> wrapper = new LambdaQueryWrapper<>();
        // 根据性别决定查询逻辑
        if (Constants.SUCCESS.equals(sex)) { // sex=0 (男性)
            // like查询所有nickName包含name的用户列表
            wrapper.like(StringUtils.isNotBlank(nickNam), AppUser::getNickName, nickNam);
        } else if (Constants.FAIL.equals(sex)) { // sex=1 (女性)
            // eq查询所有nickName等于name的用户列表
            wrapper.eq(StringUtils.isNotBlank(nickNam), AppUser::getNickName, nickNam);
        }
        // 只查询正常状态的用户
        wrapper.eq(AppUser::getStatus, Constants.SUCCESS);
        // 按create_time排序
        wrapper.orderByDesc(AppUser::getCreateTime);
        // 限制查询结果最多10条
        wrapper.last("LIMIT 20");
        // 执行查询
        List<AppUserVo> appUserVos = appUserService.selectList(wrapper);
        appUserVos.forEach(e -> {
            e.setEmail(null);
            e.setPhone(null);
            e.setPassword(null);
            e.setVipTime(appUserVipService.userVip(e.getId()));
        });
        return R.ok(appUserVos);
    }
}
