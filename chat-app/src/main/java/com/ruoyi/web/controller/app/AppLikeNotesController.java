package com.ruoyi.web.controller.app;

import com.ruoyi.app.domain.bo.AppDynamicLikeBo;
import com.ruoyi.app.domain.bo.AppLikeNotesBo;
import com.ruoyi.app.domain.bo.AppNoticeBo;
import com.ruoyi.app.domain.vo.*;
import com.ruoyi.app.service.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.validate.EditGroup;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.LikeStatus;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.im.core.EasemobTemplate;
import com.ruoyi.system.service.ISysConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * APP用户喜欢，不喜欢，拉黑控制器
 *
 * <AUTHOR>
 * @date 2023-02-14
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/likeNotes")
public class AppLikeNotesController extends BaseController {

    private final IAppUserService appUserService;
    private final IAppFriendService appFriendService;
    private final IAppNoticeService appNoticeService;
    private final IAppDynamicService appDynamicService;
    private final ISysConfigService sysConfigService;
    private final IAppLikeNotesService iAppLikeNotesService;
    private final IAppDynamicLikeService iAppDynamicLikeService;


    /**
     * 拉黑用户
     *
     * @param userId 用户ID
     */
    @PostMapping("block")
    public R<AppLikeNotesVo> block(
        @RequestParam(name = "userId") Long userId
    ) {
        if (userId.equals(getUserId())) {
            return R.fail("不能拉黑自己!");
        }
        if (iAppLikeNotesService.queryBlock(getUserId(), userId)) {
            return R.fail("你已经拉黑了用户!");
        }
        //删除用户关系
        AppFriendVo appFriendVo = appFriendService.queryUser(getUserId(), userId);
        if (Objects.nonNull(appFriendVo)) {
            appFriendService.canCelLike(appFriendVo);
        }
        AppLikeNotesBo likeNotesBo = new AppLikeNotesBo();
        likeNotesBo.setUserId(getUserId());
        likeNotesBo.setSideId(userId);
        likeNotesBo.setStatus(LikeStatus.BLOCK.getCode());
        return R.result(iAppLikeNotesService.insertByBo(likeNotesBo));
    }

    /**
     * 拉黑用户列表
     */
    @GetMapping("blockList")
    public R<List<AppUserVo>> blockList() {
        AppLikeNotesBo appLikeNotes = new AppLikeNotesBo();
        appLikeNotes.setStatus(LikeStatus.BLOCK.getCode());
        appLikeNotes.setUserId(getUserId());
        List<AppLikeNotesVo> list = iAppLikeNotesService.queryList(appLikeNotes);
        ArrayList<AppUserVo> userVos = new ArrayList<>();
        list.forEach(e -> {
            AppUserVo userVo = appUserService.queryById(e.getSideId());
            userVos.add(userVo);
        });
        return R.ok(userVos);
    }


    /**
     * 解除用户拉黑
     */
    @PostMapping("secureBlock")
    public R<List<AppUserVo>> secureBlock(
        @RequestParam(name = "userId") Long userId
    ) {
        AppLikeNotesVo appLikeNotesVo = iAppLikeNotesService.queryUser(getUserId(), userId, LikeStatus.BLOCK.getCode());
        if (Objects.isNull(appLikeNotesVo)) {
            return R.fail("未找到拉黑用户信息!");
        }
        return R.result(iAppLikeNotesService.deleteById(appLikeNotesVo.getId()));
    }

    /**
     * 收藏动态
     *
     * @param dynamicId 动态ID
     */
    @RepeatSubmit()
    @PostMapping("collectionDynamic")
    public R<AppLikeNotesVo> collectionDynamic(
        @RequestParam(name = "dynamicId") Long dynamicId
    ) {
        AppDynamicVo appDynamicVo = appDynamicService.queryById(dynamicId);
        if (Objects.isNull(appDynamicVo)) {
            return R.fail("动态不存在!");
        }
        if (Objects.nonNull(iAppLikeNotesService.queryDynamic(getUserId(), dynamicId))) {
            return R.fail("你已经收藏了动态!");
        }
        AppLikeNotesBo likeNotesBo = new AppLikeNotesBo();
        likeNotesBo.setUserId(getUserId());
        likeNotesBo.setSideId(dynamicId);
        likeNotesBo.setStatus(Constants.SUCCESS);
        if (iAppLikeNotesService.insertByBo(likeNotesBo)) {
            if (!getUserId().equals(appDynamicVo.getUserId())) {
                AppNoticeBo appNoticeBo = new AppNoticeBo();
                appNoticeBo.setTitle("收藏了你的动态");
                appNoticeBo.setContent("收藏了你的动态");
                appNoticeBo.setFromId(getUserId());
                appNoticeBo.setUserId(appDynamicVo.getUserId());
                appNoticeBo.setStatusRead(Constants.MSG_UNREAD);
                appNoticeBo.setType("5");
                appNoticeBo.setRecordId(appDynamicVo.getId());
                appNoticeService.insertByBo(appNoticeBo);
            }
            return R.ok();
        }
        return R.fail();
    }

    /**
     * 取消收藏动态
     *
     * @param dynamicId 动态ID
     */
    @RepeatSubmit()
    @PostMapping("cancelDynamic")
    public R<AppLikeNotesVo> cancelDynamic(
        @RequestParam(name = "dynamicId") Long dynamicId
    ) {
        AppLikeNotesVo like = iAppLikeNotesService.queryDynamic(getUserId(), dynamicId);
        if (Objects.isNull(like)) {
            return R.fail("未获取到收藏动态信息！");
        }
        return R.result(iAppLikeNotesService.deleteById(like.getId()));
    }

    /**
     * 获取收藏动态分页列表
     *
     * @param
     */
    @GetMapping("getDynamic")
    public TableDataInfo<AppDynamicVo> getDynamic(AppLikeNotesBo appLikeNotesBo, PageQuery pageQuery) {
        appLikeNotesBo.setUserId(getUserId());
        appLikeNotesBo.setStatus(Constants.SUCCESS);
        TableDataInfo<AppLikeNotesVo> page = iAppLikeNotesService.queryPageList(appLikeNotesBo, pageQuery);
        ArrayList<AppDynamicVo> data = new ArrayList<>();
        page.getData().forEach(e -> {
            AppDynamicVo appDynamicVo = appDynamicService.queryById(e.getSideId());
            if (Objects.nonNull(appDynamicVo)) {
                AppDynamicVo info = appDynamicService.info(appDynamicVo, getUserId());
                info.setShareUrl(sysConfigService.selectConfigByKey("app:dynamic:url") + "?id=" + info.getId());
                info.setLikeDate(e.getCreateTime());
                data.add(info);
            }
        });
        return TableDataInfo.build(data);
    }

    /**
     * 获取收藏动态分页列表
     *
     * @param
     */
    @GetMapping("getLikeDynamic")
    public TableDataInfo<AppDynamicVo> getLikeDynamic(PageQuery pageQuery) {
        AppDynamicLikeBo appDynamicLikeBo = new AppDynamicLikeBo();
        appDynamicLikeBo.setUserId(getUserId());
        appDynamicLikeBo.setType(Constants.SUCCESS);
        TableDataInfo<AppDynamicLikeVo> page = iAppDynamicLikeService.queryPageList(appDynamicLikeBo, pageQuery);
        ArrayList<AppDynamicVo> data = new ArrayList<>();
        page.getData().forEach(e -> {
            AppDynamicVo appDynamicVo = appDynamicService.queryById(e.getLikeId());
            if (Objects.nonNull(appDynamicVo)) {
                AppDynamicVo info = appDynamicService.info(appDynamicVo, getUserId());
                info.setShareUrl(sysConfigService.selectConfigByKey("app:dynamic:url") + "?id=" + info.getId());
                info.setLikeDate(e.getCreateTime());
                data.add(info);
            }
        });
        return TableDataInfo.build(data);
    }
}
