package com.ruoyi.web.controller.app;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ruoyi.app.domain.bo.AppThirdUserBo;
import com.ruoyi.app.domain.vo.AppThirdUserVo;
import com.ruoyi.app.service.IAppThirdUserService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * APP用户第三方登录
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/thirdUser")
public class AppThirdUserController extends BaseController {

    private final IAppThirdUserService iAppThirdUserService;

    /**
     * 查询APP用户第三方登录列表
     */
    @SaCheckPermission("app:thirdUser:list")
    @GetMapping("/list")
    public TableDataInfo<AppThirdUserVo> list(AppThirdUserBo bo, PageQuery pageQuery) {
        return iAppThirdUserService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出APP用户第三方登录列表
     */
    @SaCheckPermission("app:thirdUser:export")
    @Log(title = "APP用户第三方登录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AppThirdUserBo bo, HttpServletResponse response) {
        List<AppThirdUserVo> list = iAppThirdUserService.queryList(bo);
        ExcelUtil.exportExcel(list, "APP用户第三方登录", AppThirdUserVo.class, response);
    }

    /**
     * 获取APP用户第三方登录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("app:thirdUser:query")
    @GetMapping("/{id}")
    public R<AppThirdUserVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iAppThirdUserService.queryById(id));
    }

    /**
     * 新增APP用户第三方登录
     */
    @SaCheckPermission("app:thirdUser:add")
    @Log(title = "APP用户第三方登录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AppThirdUserBo bo) {
        return toAjax(iAppThirdUserService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改APP用户第三方登录
     */
    @SaCheckPermission("app:thirdUser:edit")
    @Log(title = "APP用户第三方登录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AppThirdUserBo bo) {
        return toAjax(iAppThirdUserService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除APP用户第三方登录
     *
     * @param ids 主键串
     */
    @SaCheckPermission("app:thirdUser:remove")
    @Log(title = "APP用户第三方登录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iAppThirdUserService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
