package com.ruoyi.web.controller.app;

import java.math.BigDecimal;
import java.util.List;
import java.util.Arrays;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import com.ruoyi.app.domain.vo.AppAssetVo;
import com.ruoyi.app.service.IAppAssetService;
import com.ruoyi.app.service.IAppUserService;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.utils.MessageUtils;
import com.ruoyi.common.utils.StringUtils;
import lombok.RequiredArgsConstructor;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;

import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import com.ruoyi.common.core.validate.QueryGroup;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.app.domain.vo.AppHongbaoVo;
import com.ruoyi.app.domain.bo.AppHongbaoBo;
import com.ruoyi.app.service.IAppHongbaoService;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * APP红包
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/hongbao")
public class AppHongbaoController extends BaseController {

    private final IAppUserService appUserService;
    private final IAppAssetService appAssetService;
    private final IAppHongbaoService iAppHongbaoService;

    /**
     * 查询APP红包列表
     */
    @GetMapping("/list")
    public TableDataInfo<AppHongbaoVo> list(AppHongbaoBo bo, PageQuery pageQuery) {
        return iAppHongbaoService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取APP红包详细信息
     *
     * @param hongbaoId 主键
     */
    @GetMapping("/details")
    public R<AppHongbaoVo> details(@RequestParam(name = "hongbaoId") Long hongbaoId) {
        AppHongbaoVo hongbaoVo = iAppHongbaoService.queryById(hongbaoId);
        if (hongbaoVo.getUserId() != null) {
            hongbaoVo.setUserVo(appUserService.userFilterVo(hongbaoVo.getUserId()));
        }
        if (hongbaoVo.getSendUser() != null) {
            hongbaoVo.setUserVo(appUserService.userFilterVo(hongbaoVo.getSendUser()));
        }
        return R.ok(hongbaoVo);
    }

    /**
     * 发送APP红包
     */
    @RepeatSubmit()
    @PostMapping("/send")
    public R<?> add(@Validated(AddGroup.class) AppHongbaoBo bo) {
        Long id = getUserId();
        bo.setUserId(id);
        bo.setStatus(Constants.SUCCESS);
        if (bo.getHongbaoNumber() == null) {
            bo.setHongbaoNumber(1L);
        }
        BigDecimal amount = new BigDecimal(bo.getHongbaoNumber()).multiply(bo.getHongbaoMoney().abs());
        AppAssetVo appAssetVo = appAssetService.queryByUid(id);
        if (amount.compareTo(appAssetVo.getBalance()) > 0) {
            return R.fail(MessageUtils.message("user.error.balance"));
        }
        AppHongbaoBo appHongbaoBo = iAppHongbaoService.sendHongbao(bo, amount);
        return Objects.nonNull(appHongbaoBo) ? R.ok(MessageUtils.message("system.info.success"), appHongbaoBo) : R.fail(MessageUtils.message("system.info.error"));
    }


    /**
     * 领取APP红包
     */
    @RepeatSubmit()
    @PostMapping("/receiveHongbao")
    public R<?> receiveHongbao(@Validated(EditGroup.class) AppHongbaoBo bo) {
        Long id = getUserId();
        AppHongbaoVo hongbaoVo = iAppHongbaoService.queryById(bo.getHongbaoId());
        if (Objects.isNull(hongbaoVo) || !id.equals(hongbaoVo.getSendUser())) {
            return R.fail(MessageUtils.message("hongbao.not.blank"));
        }
        if (StringUtils.equals(hongbaoVo.getStatus(), Constants.FAIL)) {
            return R.fail(MessageUtils.message("hongbao.receive.end"));
        }
        AppHongbaoVo appHongbaoVo = iAppHongbaoService.receiveHongbao(id, hongbaoVo);
        return Objects.nonNull(appHongbaoVo) ? R.ok(MessageUtils.message("system.info.success"), appHongbaoVo) : R.fail(MessageUtils.message("system.info.error"));
    }
}
