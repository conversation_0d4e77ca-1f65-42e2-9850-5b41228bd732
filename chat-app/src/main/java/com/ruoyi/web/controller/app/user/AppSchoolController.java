package com.ruoyi.web.controller.app.user;

import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.app.domain.bo.AppSchoolBo;
import com.ruoyi.app.domain.vo.AppSchoolVo;
import com.ruoyi.app.service.IAppSchoolService;
import com.ruoyi.common.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * APP学校信息
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/school")
public class AppSchoolController extends BaseController {

    private final IAppSchoolService iAppSchoolService;

    /**
     * 查询APP学校信息列表
     */
    @GetMapping("/list")
    public R<List<AppSchoolVo>> list(
        @RequestParam(name = "schoolName", required = false) String schoolName
    ) {
        AppSchoolBo appSchoolBo = new AppSchoolBo();
        appSchoolBo.setStatus(Constants.SUCCESS);
        if (StringUtils.isNotBlank(schoolName)) {
            appSchoolBo.setName(schoolName);
        }
        return R.ok(iAppSchoolService.queryList(appSchoolBo));
    }

}
