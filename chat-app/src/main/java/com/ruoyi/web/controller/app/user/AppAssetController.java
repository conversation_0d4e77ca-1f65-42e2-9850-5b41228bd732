package com.ruoyi.web.controller.app.user;

import com.ruoyi.app.domain.bo.AppTranslateBo;
import com.ruoyi.app.domain.vo.AppAssetVo;
import com.ruoyi.app.service.IAppAssetService;
import com.ruoyi.app.service.IAppDetailService;
import com.ruoyi.app.service.IAppUserService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.enums.BillType;
import com.ruoyi.common.enums.CoinType;
import com.ruoyi.common.enums.DetailType;
import com.ruoyi.common.helper.LoginHelper;
import com.ruoyi.common.utils.MessageUtils;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.web.service.BaiduTranslateService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.HashMap;

/**
 * APP用户资产信息
 *
 * <AUTHOR>
 * @date 2025-12-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/asset")
public class AppAssetController extends BaseController {


    private final IAppUserService appUserService;
    private final IAppAssetService appAssetService;
    private final IAppDetailService appDetailService;
    private final ISysConfigService sysConfigService;

    /**
     * 获取APP用户资产信息
     */
    @GetMapping()
    public R<AppAssetVo> info() {
        return R.ok(appAssetService.queryByUid(getUserId()));
    }

    /**
     * 语言列表
     *
     * @return
     */
    @GetMapping("/languages")
    public R<?> languages() {
        return R.ok(appUserService.languages());
    }


    /**
     * 发送图片
     *
     * @param chatName 接收方用户
     * @return
     */
    @PostMapping("/sendImage")
    public R<AppAssetVo> sendImage(
        @RequestParam(name = "chatName", required = false) String chatName,
        @RequestParam(name = "number", defaultValue = "1") String number
    ) {
        Long id = LoginHelper.getUserId();
        AppAssetVo assetVo = appAssetService.queryByUid(id);
        String imagePrice = sysConfigService.selectConfigByKey("app.chat.image");
        BigDecimal totalPrice = new BigDecimal(imagePrice).multiply(new BigDecimal(number));
        if (totalPrice.compareTo(assetVo.getBalance()) > 0) {
            return R.fail(MessageUtils.message("user.error.balance"));
        }
        appAssetService.subtractBalance(id, totalPrice);
        appDetailService.insert(id, totalPrice, BigDecimal.ZERO, BillType.TO.getCode(), CoinType.GOLD.getCode(), DetailType.IMAGE.getCode(), "发送图片扣费", null);
        return R.ok(MessageUtils.message("system.info.success"));
    }

    /**
     * 发送表情
     *
     * @return
     */
    @PostMapping("/sendEmoji")
    public R<?> sendEmoji() {
        Long id = LoginHelper.getUserId();
        String emojiPrice = sysConfigService.selectConfigByKey("app.chat.emoji");
        return appUserService.subtractionEmoji(id, new BigDecimal(emojiPrice));
    }

    /**
     * 翻译文字
     *
     * @param translateBo 翻译实体
     * @return
     */
    @PostMapping("/translate")
    public R<?> translate(@Valid AppTranslateBo translateBo) {
        Long id = LoginHelper.getUserId();
        int length = translateBo.getContext().length();
        if (length <= 0) {
            return R.fail(MessageUtils.message("user.error.context.blank"));
        }
        String imagePrice = sysConfigService.selectConfigByKey("app.chat.translate");
        int totalPrice = length * Integer.parseInt(imagePrice);
        HashMap<String, Object> translate = BaiduTranslateService.translate(translateBo.getContext(), translateBo.getFrom(), translateBo.getTo());
        if (ObjectUtils.isEmpty(translate)) {
            return R.fail(MessageUtils.message("user.error.translate"));
        } else {
            Boolean result = appUserService.translate(id, totalPrice);
            return result ? R.ok(translate) : R.fail(MessageUtils.message("user.error.integral"));
        }
    }
}
