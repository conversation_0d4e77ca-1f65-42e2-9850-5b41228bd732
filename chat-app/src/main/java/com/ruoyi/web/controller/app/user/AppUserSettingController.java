package com.ruoyi.web.controller.app.user;

import java.util.List;
import java.util.Arrays;
import java.util.concurrent.TimeUnit;

import lombok.RequiredArgsConstructor;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;

import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import com.ruoyi.common.core.validate.QueryGroup;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.app.domain.vo.AppUserSettingVo;
import com.ruoyi.app.domain.bo.AppUserSettingBo;
import com.ruoyi.app.service.IAppUserSettingService;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * APP用户信息设置
 *
 * <AUTHOR>
 * @date 2023-02-02
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/userSetting")
public class AppUserSettingController extends BaseController {

    private final IAppUserSettingService iAppUserSettingService;


    /**
     * 获取APP用户信息设置详细信息
     */
    @GetMapping()
    public R<AppUserSettingVo> getInfo() {
        return R.ok(iAppUserSettingService.queryByUserId(getUserId()));
    }


    /**
     * 修改APP用户信息设置
     */
    @Log(title = "APP用户信息设置", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> edit(@Validated(EditGroup.class) AppUserSettingBo bo) {
        AppUserSettingVo appUserSettingVo = iAppUserSettingService.queryByUserId(getUserId());
        bo.setUserId(appUserSettingVo.getUserId());
        bo.setId(appUserSettingVo.getId());
        return toAjax(iAppUserSettingService.updateByBo(bo) ? 1 : 0);
    }
}
