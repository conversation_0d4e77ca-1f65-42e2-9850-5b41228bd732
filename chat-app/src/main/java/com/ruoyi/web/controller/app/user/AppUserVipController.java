package com.ruoyi.web.controller.app.user;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ruoyi.app.domain.bo.AppUserVipBo;
import com.ruoyi.app.domain.vo.AppUserVipVo;
import com.ruoyi.app.domain.vo.AppUserVo;
import com.ruoyi.app.service.IAppUserService;
import com.ruoyi.app.service.IAppUserVipService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.constant.VipConstants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * APP用户VIP
 *
 * <AUTHOR>
 * @date 2023-03-16
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/userVip")
public class AppUserVipController extends BaseController {

    private final IAppUserVipService iAppUserVipService;
    private final IAppUserService iAppUserService;

    /**
     * 领取一日会员
     */
    @PostMapping()
    public R<Object> list() {
        AppUserVo userVo = iAppUserService.queryById(getUserId());
        long day = (System.currentTimeMillis() - userVo.getCreateTime().getTime()) / (1000 * 60 * 60 * 24);
        if (day >= VipConstants.DAY) {
            iAppUserVipService.addFreeVipTime(getUserId(), VipConstants.VIP_DAY);
        } else {
            return R.fail("距离上次登录" + day + "天");
        }
        return R.ok();
    }
}
