package com.ruoyi.web.controller.app.moments;

import java.util.*;

import com.ruoyi.app.domain.AppDynamicType;
import com.ruoyi.app.domain.bo.AppLikeNotesBo;
import com.ruoyi.app.domain.vo.AppDynamicVo;
import com.ruoyi.app.domain.vo.AppLikeNotesVo;
import com.ruoyi.app.service.IAppDynamicService;
import com.ruoyi.app.service.IAppLikeNotesService;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.LikeStatus;
import com.ruoyi.common.utils.RandomUtils;
import lombok.RequiredArgsConstructor;

import javax.validation.constraints.*;

import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.app.domain.vo.AppDynamicTypeVo;
import com.ruoyi.app.domain.bo.AppDynamicTypeBo;
import com.ruoyi.app.service.IAppDynamicTypeService;

/**
 * APP用户动态话题类型
 *
 * <AUTHOR>
 * @date 2023-05-18
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/dynamicType")
public class AppDynamicTypeController extends BaseController {

    private final IAppDynamicService iAppDynamicService;
    private final IAppLikeNotesService iAppLikeNotesService;
    private final IAppDynamicTypeService iAppDynamicTypeService;


    /**
     * 查询APP用户动态话题类型列表
     */
    @GetMapping("/list")
    public R<Object> list(AppDynamicTypeBo bo) {
        List<AppDynamicTypeVo> list = iAppDynamicTypeService.queryList(bo);
        HashMap<String, Object> map = new HashMap<>(2);
        map.put("topicExist", Objects.nonNull(iAppDynamicTypeService.queryByName(bo.getName())));
        map.put("similarList", list);
        return R.ok(map);
    }

    /**
     * 查询APP兴趣爱好分页列表
     */
    @GetMapping("/page")
    public TableDataInfo<AppDynamicTypeVo> page(AppDynamicTypeBo bo,PageQuery pageQuery) {
        return iAppDynamicTypeService.queryPageList(bo,pageQuery);
    }


    /**
     * 获取推荐的话题列表
     *
     * @param number 数量
     * @return
     */
    @GetMapping("/recommendList")
    public R<Object> recommendList(
        @RequestParam(name = "number", defaultValue = "6") Integer number
    ) {
        List<AppDynamicTypeVo> appDynamicTypeVos = dynamicTypeVosList(number);
        HashMap<String, Object> map = new HashMap<>(2);
        map.put("recommendList", appDynamicTypeVos);
        AppLikeNotesBo likeNotesBo = new AppLikeNotesBo();
        likeNotesBo.setStatus(LikeStatus.TOPIC.getCode());
        likeNotesBo.setUserId(getUserId());
        List<AppLikeNotesVo> list = iAppLikeNotesService.queryList(likeNotesBo);
        ArrayList<AppDynamicTypeVo> historyList = new ArrayList<>();
        list.forEach(e -> {
            AppDynamicTypeVo dynamicTypeVo = iAppDynamicTypeService.queryById(e.getSideId());
            if (Objects.nonNull(dynamicTypeVo)) {
                historyList.add(dynamicTypeVo);
            }
        });
        map.put("historyList", historyList);
        return R.ok(map);
    }

    /**
     * 清除用户的历史话题
     *
     * @return
     */
    @PostMapping("delete")
    public R<Void> delete() {
        return R.result(iAppLikeNotesService.deleteByType(getUserId(), LikeStatus.TOPIC.getCode()));
    }

    /**
     * 创建话题
     *
     * @param bo 信息
     */
    @PostMapping()
    public R<AppDynamicTypeVo> create(@Validated(AddGroup.class) AppDynamicTypeBo bo) {
        AppDynamicType appDynamicType = iAppDynamicTypeService.queryByName(bo.getName());
        if (Objects.nonNull(appDynamicType)) {
            return R.fail("话题已经存在！");
        }
        bo.setUserId(getUserId());
        bo.setType(Constants.FAIL);
        return R.result(iAppDynamicTypeService.insertByBo(bo));
    }

    /**
     * 获取话题详情
     *
     * @param id 话题ID
     * @return
     */
    @GetMapping("details")
    public Object details(Long id) {
        AppDynamicTypeVo dynamicTypeVo = iAppDynamicTypeService.queryById(id);
        if (Objects.isNull(dynamicTypeVo)) {
            return R.fail("未找到话题");
        }
        TableDataInfo<AppDynamicVo> appDynamicVoTableDataInfo = iAppDynamicService.queryPageList(dynamicTypeVo.getId(), Constants.SUCCESS, new PageQuery());
        dynamicTypeVo.setHeat(appDynamicVoTableDataInfo.getTotal());
        return R.ok(dynamicTypeVo);
    }

    /**
     * 删除APP用户动态话题类型
     *
     * @param ids 主键串
     */
    @Log(title = "APP用户动态话题类型", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iAppDynamicTypeService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }

    /**
     * 获取指定推荐数量的话题
     *
     * @param max 获取几条数据
     * @return
     */
    public List<AppDynamicTypeVo> dynamicTypeVosList(Integer max) {
        List<AppDynamicTypeVo> appDynamicTypeVos = iAppDynamicTypeService.queryList(new AppDynamicTypeBo());
        if (max > appDynamicTypeVos.size()) {
            return appDynamicTypeVos;
        }
        ArrayList<AppDynamicTypeVo> recoList = new ArrayList<>();
        HashSet<Long> ids = new HashSet<>();
        for (AppDynamicTypeVo e : appDynamicTypeVos) {
            AppDynamicTypeVo dynamicTypeVo = appDynamicTypeVos.get(RandomUtils.number(0, appDynamicTypeVos.size()));
            if (!ids.contains(dynamicTypeVo.getId())) {
                ids.add(dynamicTypeVo.getId());
                recoList.add(dynamicTypeVo);
            }
            if (recoList.size() >= max) {
                break;
            }
        }
        return recoList;
    }
}
