package com.ruoyi.web.controller.app.user;

import com.ruoyi.app.domain.vo.AppUserBagVo;
import com.ruoyi.app.service.IAppUserBagService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * App用户签到领取奖励
 *
 * <AUTHOR>
 * @date 2025-03-29
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/userBag")
public class AppUserBagController extends BaseController {

    private final IAppUserBagService iAppUserBagService;

    /**
     * 获取App用户签到领取奖励详细信息
     */

    @GetMapping()
    public R<AppUserBagVo> getInfo(String type) {
        return R.ok(iAppUserBagService.queryByUserId(getUserId(), type));
    }

}
