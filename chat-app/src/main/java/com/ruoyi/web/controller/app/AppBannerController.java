package com.ruoyi.web.controller.app;

import com.ruoyi.app.domain.bo.AppBannerBo;
import com.ruoyi.app.domain.vo.AppBannerVo;
import com.ruoyi.app.service.IAppBannerService;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * APP商城的轮播图
 *
 * <AUTHOR>
 * @date 2025-04-20
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/banner")
public class AppBannerController extends BaseController {

    private final IAppBannerService iAppBannerService;

    /**
     * 查询APP商城的轮播图列表
     */
    @GetMapping("/list")
    public R<List<AppBannerVo>> list(AppBannerBo bo) {
        bo.setStatus(Constants.SUCCESS);
        bo.setType(Constants.FAIL);
        return R.ok(iAppBannerService.queryList(bo));
    }

    /**
     * 获取APP商城的轮播图详细信息
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    public R<AppBannerVo> getInfo(@NotNull(message = "主键不能为空")
                                  @PathVariable Long id) {
        return R.ok(iAppBannerService.queryById(id));
    }
}
