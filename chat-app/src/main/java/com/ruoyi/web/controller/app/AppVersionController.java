package com.ruoyi.web.controller.app;

import cn.dev33.satoken.annotation.SaIgnore;
import com.ruoyi.app.domain.bo.AppVersionBo;
import com.ruoyi.app.domain.vo.AppVersionVo;
import com.ruoyi.app.service.IAppVersionService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.system.service.ISysConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * APP版本更新记录
 *
 * <AUTHOR>
 * @date 2023-04-11
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/version")
public class AppVersionController extends BaseController {

    private final IAppVersionService iAppVersionService;
    private final ISysConfigService iSysConfigService;

    /**
     * 获取APP版本更新的列表
     */
    @GetMapping("/list")
    public TableDataInfo<AppVersionVo> list(AppVersionBo bo, PageQuery pageQuery) {
        return iAppVersionService.queryPageList(bo, pageQuery);
    }


    /**
     * 获取APP的版本更新信息
     */
    @SaIgnore
    @GetMapping("/info")
    public R<AppVersionVo> info() {
        AppVersionVo appVersionVo = iAppVersionService.lastOne(new AppVersionBo());
        appVersionVo.setIosDownload(iSysConfigService.selectConfigByKey("app.down.ios"));
        appVersionVo.setAndroidDownload(iSysConfigService.selectConfigByKey("app.down.android"));
        return R.ok(appVersionVo);
    }

}
