package com.ruoyi.web.controller.app.label;

import com.ruoyi.app.domain.bo.AppLabelTypeBo;
import com.ruoyi.app.domain.vo.AppLabelTypeVo;
import com.ruoyi.app.service.IAppLabelTypeService;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * APP标签类型
 *
 * <AUTHOR>
 * @date 2023-01-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/labelType")
public class AppLabelTypeController extends BaseController {

    private final IAppLabelTypeService iAppLabelTypeService;

    /**
     * 查询APP标签类型列表
     */
    @GetMapping("/list")
    public R<List<AppLabelTypeVo>> list(AppLabelTypeBo bo) {
        bo.setStatus(Constants.SUCCESS);
        return R.ok(iAppLabelTypeService.queryList(bo));
    }
}
