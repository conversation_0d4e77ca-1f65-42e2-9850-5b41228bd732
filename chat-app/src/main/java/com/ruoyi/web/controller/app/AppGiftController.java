package com.ruoyi.web.controller.app;

import com.ruoyi.app.domain.bo.AppGiftBo;
import com.ruoyi.app.domain.bo.AppGiftOrderBo;
import com.ruoyi.app.domain.vo.*;
import com.ruoyi.app.service.*;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.helper.LoginHelper;
import com.ruoyi.common.utils.MessageUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

/**
 * APP礼物
 *
 * <AUTHOR>
 * @date 2025-12-14
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/gift")
public class AppGiftController extends BaseController {

    private final IAppGiftService appGiftService;
    private final IAppUserService appUserService;
    private final IAppVipTypeService appVipTypeService;
    private final IAppUserVipService appUserVipService;
    private final IAppGiftOrderService appGiftOrderService;

    /**
     * 查询APP礼物列表
     */
    @GetMapping("/list")
    public TableDataInfo<AppGiftVo> list(AppGiftBo bo, PageQuery pageQuery) {
        bo.setStatus(Constants.SUCCESS);
        TableDataInfo<AppGiftVo> list = appGiftService.queryPageList(bo, pageQuery);
        BigDecimal zhe;
        AppUserVipVo userVipVo = appUserVipService.userVipId(getUserId());
        if (Objects.isNull(userVipVo)) {
            zhe = BigDecimal.ZERO;
        } else {
            AppVipTypeVo vipTypeVo = appVipTypeService.queryById(userVipVo.getVipId());
            zhe = vipTypeVo.getGiveDiscount() != null ? vipTypeVo.getGiveDiscount() : BigDecimal.ZERO;
        }
        list.getData().forEach(e -> {
            if (zhe.compareTo(BigDecimal.ZERO) == 0) {
                e.setDiscountPrice(new BigDecimal(e.getAmount()));
            } else {
                BigDecimal divide = new BigDecimal(e.getAmount()).multiply(zhe).divide(new BigDecimal(10), 2, RoundingMode.HALF_UP);
                e.setDiscountPrice(divide);
            }
        });
        return list;
    }

    /**
     * 赠送用户礼物
     *
     * @param userId 赠送的用户ID
     * @param gid    礼物ID
     * @param number 礼物数量
     * @return
     */
    @PostMapping("/send")
    public R<Object> send(
        @RequestParam(name = "userId") Long userId,
        @RequestParam(name = "gid") Long gid,
        @RequestParam(name = "number", defaultValue = "1") Integer number
    ) {
        Long id = LoginHelper.getUserId();
        AppUserVo appUserVo = appUserService.queryById(userId);
        if (Objects.isNull(appUserVo)) {
            return R.fail(MessageUtils.message("user.not.blank"));
        }
        AppGiftVo appGift = appGiftService.queryById(gid);
        if (Objects.isNull(appGift)) {
            return R.fail(MessageUtils.message("gift.not.blank"));
        }
        BigDecimal price = new BigDecimal(appGift.getAmount());
        AppUserVipVo userVipVo = appUserVipService.userVipId(id);
        if (Objects.nonNull(userVipVo)) {
            AppVipTypeVo vipTypeVo = appVipTypeService.queryById(userVipVo.getVipId());
            if (vipTypeVo.getGiveDiscount() != null && vipTypeVo.getGiveDiscount().compareTo(BigDecimal.ZERO) > 0) {
                price = new BigDecimal(appGift.getAmount()).multiply(vipTypeVo.getGiveDiscount()).divide(new BigDecimal(10), 2, RoundingMode.HALF_UP);
            }
        }
        if (appGiftService.send(id, userId, appGift, number, price)) {
            return R.ok(MessageUtils.message("system.info.success"));
        } else {
            return R.fail(MessageUtils.message("system.info.error"));
        }
    }

    /**
     * 获取赠送和收到的礼物记录
     *
     * @param type      1=赠送记录，2=收到记录
     * @param pageQuery 分页信息
     * @return
     */
    @GetMapping("/page")
    public TableDataInfo<AppGiftOrderVo> page(
        @RequestParam(name = "type", defaultValue = "1") Integer type, PageQuery pageQuery) {
        AppGiftOrderBo orderBo = new AppGiftOrderBo();
        if (type == 1) {
            orderBo.setUserId(LoginHelper.getUserId());
        } else {
            orderBo.setToUserId(LoginHelper.getUserId());
        }
        return appGiftOrderService.queryPageList(orderBo, pageQuery);
    }
}
