package com.ruoyi.web.controller.app.user;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ruoyi.app.domain.bo.AppUserImageBo;
import com.ruoyi.app.domain.vo.AppUserImageVo;
import com.ruoyi.app.service.IAppUserImageService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.helper.LoginHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * APP用户照片
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/userImage")
public class AppUserImageController extends BaseController {

    private final IAppUserImageService iAppUserImageService;

    /**
     * 查询APP用户照片列表
     */
    @GetMapping("/page")
    public TableDataInfo<AppUserImageVo> page(AppUserImageBo bo, PageQuery pageQuery) {
        return iAppUserImageService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取APP用户照片列表
     */
    @GetMapping("/list")
    public R<List<AppUserImageVo>> list(AppUserImageBo bo) {
        bo.setUserId(getUserId());
        return R.ok(iAppUserImageService.queryList(bo));
    }

    /**
     * 获取APP用户照片详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("app:userImage:query")
    @GetMapping("/{id}")
    public R<AppUserImageVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iAppUserImageService.queryById(id));
    }

    /**
     * 新增APP用户生活照片
     */
    @Log(title = "新增APP用户生活照片", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Object> add(@Validated(AddGroup.class) AppUserImageBo bo) {
        bo.setUserId(getUserId());
        if (iAppUserImageService.insertByBo(bo)) {
            return R.ok("成功", bo);
        }
        return R.fail("失败");
    }


    /**
     * 删除APP用户照片
     *
     * @param id 主键
     */
    @Log(title = "APP用户照片", businessType = BusinessType.DELETE)
    @PostMapping("delete")
    public R<Void> remove(@RequestParam(name = "id") Long id) {
        return R.result(iAppUserImageService.deleteWithValidById(id, LoginHelper.getUserId()));
    }
}
