package com.ruoyi.web.controller.app;

import com.ruoyi.app.domain.bo.AppOccupationBo;
import com.ruoyi.app.service.IAppOccupationService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.enums.UserStatus;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * APP职业信息
 *
 * <AUTHOR>
 * @date 2022-12-12
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/occupation")
public class AppOccupationController extends BaseController {

    private final IAppOccupationService iAppOccupationService;

    /**
     * 查询APP职业信息列表
     */
    @GetMapping("/list")
    public R<Object> list() {
        AppOccupationBo appOccupationBo = new AppOccupationBo();
        appOccupationBo.setStatus(UserStatus.OK.getCode());
        return R.ok(iAppOccupationService.queryList(appOccupationBo));
    }
}
