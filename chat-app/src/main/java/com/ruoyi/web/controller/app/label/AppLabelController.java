package com.ruoyi.web.controller.app.label;

import com.ruoyi.app.domain.bo.AppLabelBo;
import com.ruoyi.app.domain.bo.AppLabelLikeBo;
import com.ruoyi.app.domain.vo.AppLabelLikeVo;
import com.ruoyi.app.domain.vo.AppLabelVo;
import com.ruoyi.app.service.IAppLabelLikeService;
import com.ruoyi.app.service.IAppLabelService;
import com.ruoyi.app.service.IAppUserService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.LikeStatus;
import com.ruoyi.common.helper.LoginHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * APP标签
 *
 * <AUTHOR>
 * @date 2023-01-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/label")
public class AppLabelController extends BaseController {

    private final IAppUserService appUserService;
    private final IAppLabelService appLabelService;
    private final IAppLabelLikeService appLabelLikeService;
    /**
     * 返回的数据条数
     */
    private static final int MAX = 4;


    /**
     * 标签首页数据接口
     *
     * @return
     */
    @GetMapping("indexData")
    public R<HashMap<String, Object>> indexData() {
        AppLabelBo likeBo = new AppLabelBo();
        likeBo.setHotStatus(Constants.SUCCESS);
        likeBo.setStatus(Constants.SUCCESS);
        List<AppLabelVo> likeList = appLabelService.queryList(likeBo);
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("likeList", subStr(likeList));

        AppLabelBo addBo = new AppLabelBo();
        likeBo.setStatus(Constants.SUCCESS);
        List<AppLabelVo> addList = appLabelService.queryList(addBo);
        hashMap.put("addList", subStr(addList));

        AppLabelLikeBo labelLikeBo = new AppLabelLikeBo();
        labelLikeBo.setLikeStatus(LikeStatus.SUPER_LIKE.getCode());
        List<AppLabelLikeVo> list = appLabelLikeService.list(labelLikeBo);
        ArrayList<AppLabelLikeVo> data = new ArrayList<>();
        list.stream().limit(MAX).forEach(e -> {
            e.setLabel(appLabelService.queryById(e.getLabelId()));
            e.setUserVo(appUserService.queryByIdUserVo(e.getUserId()));
            data.add(e);
        });
        hashMap.put("superLikeList", data);
        return R.ok(hashMap);
    }

    /**
     * 搜索标签列表接口全部
     */
    @GetMapping("/find")
    public R<List<AppLabelVo>> find(String name) {
        AppLabelBo appLabelBo = new AppLabelBo();
        appLabelBo.setName(name);
        return R.ok(appLabelService.queryList(appLabelBo));
    }

    /**
     * 搜索标签列表接口返回
     */
    @GetMapping("/findSize")
    public R<HashSet<AppLabelVo>> findSize(String name) {
        AppLabelBo appLabelBo = new AppLabelBo();
        appLabelBo.setName(name);
        List<AppLabelVo> list = appLabelService.queryList(appLabelBo);
        HashSet<AppLabelVo> labels = new HashSet<>();
        for (AppLabelVo label : list) {
            int random = random(0, list.size());
            AppLabelVo appLabelVo = list.get(random);
            labels.add(appLabelVo);
            if (labels.size() >= 4) {
                break;
            }
        }
        return R.ok(labels);
    }


    /**
     * 根据类型获取APP标签列表
     */
    @GetMapping("/list")
    public R<List<AppLabelVo>> list(AppLabelBo bo) {
        return R.ok(appLabelService.queryListType(bo, LoginHelper.getUserId()));
    }

    /**
     * 根据类型获取APP标签列表
     */
    @GetMapping("/page")
    public TableDataInfo<AppLabelVo> page(AppLabelBo bo, PageQuery pageQuery) {
        bo.setStatus(Constants.SUCCESS);
        return appLabelService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取APP标签详细信息
     *
     * @param id 主键
     */
    @GetMapping("detail")
    public R<AppLabelVo> detail(@RequestParam(name = "id") Long id) {
        AppLabelVo labelVo = appLabelService.queryById(id);
        if (Objects.isNull(labelVo)) {
            return R.fail("标签不存在!");
        }
        //用户是否喜欢了标签
        labelVo.setIsJoin(Objects.nonNull(appLabelLikeService.queryLikeLabel(getUserId(), labelVo.getId())));
        return R.ok(appLabelLikeService.info(labelVo, getUserId()));
    }

    /**
     * 用户新增标签
     */
    @Log(title = "用户新增兴趣爱好标签", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) AppLabelBo bo) {
        AppLabelVo labelVo = appLabelService.queryByName(bo.getName());
        if (Objects.nonNull(labelVo)) {
            return R.ok("标签名字重复!");
        }
        bo.setUserId(LoginHelper.getUserId());
        bo.setHotStatus(Constants.FAIL);
        bo.setStatus(Constants.SUCCESS);
        return R.result(appLabelService.createLabel(bo));
    }

    /**
     * 取消喜欢标签
     */
    @Log(title = "取消喜欢标签", businessType = BusinessType.DELETE)
    @RepeatSubmit()
    @PostMapping("delete")
    public R<Void> edit(@RequestParam(name = "id") Long id) {
        AppLabelLikeVo appLabelLikeVo = appLabelLikeService.queryLikeLabel(getUserId(), id);
        if (Objects.isNull(appLabelLikeVo)) {
            return R.fail("标签已取消喜欢!");
        }
        return toAjax(appLabelLikeService.delete(appLabelLikeVo.getId()));
    }

    /**
     * 截取数组元素
     *
     * @param list 数组
     * @return
     */
    public List<AppLabelVo> subStr(List<AppLabelVo> list) {
        if (MAX < list.size()) {
            ArrayList<AppLabelVo> newList = new ArrayList<>();
            for (int i = 0; i < 4; i++) {
                newList.add(list.get(i));
            }
            return newList;
        }
        return list;
    }

    /**
     * 获取一个随机数
     *
     * @param min 最小数
     * @param max 最大数
     * @return
     */
    private int random(int min, int max) {
        return min + (int) (Math.random() * (max - min));
    }
}
