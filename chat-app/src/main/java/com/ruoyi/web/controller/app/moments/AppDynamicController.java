package com.ruoyi.web.controller.app.moments;

import com.ruoyi.app.domain.bo.*;
import com.ruoyi.app.domain.vo.AppDynamicCommentVo;
import com.ruoyi.app.domain.vo.AppDynamicEmojiVo;
import com.ruoyi.app.domain.vo.AppDynamicVo;
import com.ruoyi.app.domain.vo.AppRecordVo;
import com.ruoyi.app.service.*;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.DynamicStatus;
import com.ruoyi.common.enums.DynamicType;
import com.ruoyi.common.enums.NoticType;
import com.ruoyi.common.helper.LoginHelper;
import com.ruoyi.common.utils.MessageUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.web.filter.SensitiveFilter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * APP用户动态
 *
 * <AUTHOR>
 * @date 2025-12-22
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/dynamic")
public class AppDynamicController extends BaseController {


    private final IAppUserService appUserService;
    private final IAppNoticeService appNoticeService;
    private final ISysConfigService sysConfigService;
    private final IAppRecordService appRecordService;
    private final IAppDynamicService iAppDynamicService;
    private final IAppLikeNotesService appLikeNotesService;
    private final IAppDynamicEmojiService appDynamicEmojiService;
    private final IAppDynamicLikeService iAppDynamicLikeService;
    private final IAppDynamicCommentService iAppDynamicCommentService;
    private static final Integer MAX = 9;

    /**
     * 查询APP用户动态列表
     */
    @GetMapping("/page")
    public TableDataInfo<AppDynamicVo> page(AppDynamicBo bo, PageQuery pageQuery) {
        bo.setStatus(Constants.SUCCESS);
        TableDataInfo<AppDynamicVo> page = iAppDynamicService.queryPageList(bo, pageQuery);
        return iAppDynamicService.queryDate(page, getUserId());
    }

    /**
     * 查询动态表情列表
     */
    @GetMapping("/emojiPage")
    public TableDataInfo<AppDynamicEmojiVo> emojiPage(PageQuery pageQuery) {
        AppDynamicEmojiBo emojiBo = new AppDynamicEmojiBo();
        emojiBo.setStatus(Constants.SUCCESS);
        return appDynamicEmojiService.queryPageList(emojiBo, pageQuery);
    }

    /**
     * 查询用户看到他的动态
     *
     * @param id 用户ID
     * @return
     */
    @GetMapping("/myList")
    public R<Object> myList(@RequestParam(name = "id", required = false) Long id) {
        id = id == null ? getUserId() : id;
        AppDynamicBo dynamic = new AppDynamicBo();
        dynamic.setUserId(id);
        Calendar instance = Calendar.getInstance();
        List<AppDynamicVo> data = iAppDynamicService.queryDate(iAppDynamicService.queryList(dynamic), getUserId());
        data.forEach(e -> {
            instance.setTime(e.getCreateTime());
            e.setYear(instance.get(Calendar.YEAR));
        });
        TreeMap<Integer, List<AppDynamicVo>> integerListTreeMap = new TreeMap<>(Comparator.reverseOrder());
        Map<Integer, List<AppDynamicVo>> collect = data.stream().collect(Collectors.groupingBy(AppDynamicVo::getYear));
        integerListTreeMap.putAll(collect);
        ArrayList<Map<String, Object>> arrayList = new ArrayList<>();
        integerListTreeMap.forEach((key, value) -> {
            HashMap<String, Object> hashMap = new HashMap<>(2);
            hashMap.put("year", key);
            hashMap.put("list", value);
            arrayList.add(hashMap);
        });
        return R.ok(MessageUtils.message("system.info.success"), arrayList);
    }

    /**
     * 根据ID查询动态详细信息
     *
     * @param id 主键ID
     */
    @GetMapping()
    public R<AppDynamicVo> getInfo(Long id) {
        AppDynamicVo info = iAppDynamicService.findOne(id, getUserId());
        if (ObjectUtils.isEmpty(info)) {
            return R.fail(MessageUtils.message("system.info.error"));
        }
        return R.ok(MessageUtils.message("system.info.success"), info);
    }

    /**
     * 用户发布APP动态
     */
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(AppDynamicBo bo) {
        if (bo.getContext().length() > 500) {
            return R.fail(MessageUtils.message("moments.not.context.max"));
        }
        bo.setUserId(getUserId());
        bo.setScope(Constants.FAIL);
        if (StringUtils.equals("true", sysConfigService.selectConfigByKey("app.dynamic.check").toLowerCase())) {
            bo.setStatus(DynamicStatus.CHECK.getCode());
        } else {
            bo.setStatus(DynamicStatus.OK.getCode());
        }
        if (bo.getType().equals(DynamicType.IMAGE.getCode())) {
            if (bo.getImages() != null) {
                String[] images = bo.getImages().split(",");
                long count = Arrays.stream(images).filter(StringUtils::isNotBlank).count();
                if (count > MAX) {
                    return R.fail(MessageUtils.message("moments.upload.max.image", MAX));
                }
                if (images.length > 0) {
                    bo.setImagesList(Arrays.asList(images));
                }
            }
        } else if (bo.getType().equals(DynamicType.VIDEO.getCode())) {
            if (StringUtils.isBlank(bo.getUrl()) || StringUtils.isBlank(bo.getImages())) {
                return R.fail(MessageUtils.message("moments.not.video.blank"));
            }
        } else {
            return R.fail(MessageUtils.message("moments.not.type"));
        }
        if (iAppDynamicService.addDynamic(bo)) {
            return R.ok(MessageUtils.message("system.info.success"));
        }
        return R.fail(MessageUtils.message("system.info.error"));
    }

    /**
     * 动态和评论点赞/取消点赞操作
     *
     * @param id   动态或评论ID
     * @param type 点赞类型
     * @return
     */
    @PostMapping("/like")
    public R<Object> addLike(
        @RequestParam(name = "id") Long id,
        @RequestParam(name = "type", defaultValue = "0") String type,
        @RequestParam(name = "emojiId") Long emojiId
    ) {
        AppDynamicEmojiVo emojiVo = appDynamicEmojiService.queryById(emojiId);
        if (ObjectUtils.isEmpty(emojiVo)) {
            return R.fail(MessageUtils.message("moments.not.emoji.blank"));
        }
        HashMap<String, String> like = iAppDynamicLikeService.like(id, getUserId(), type, emojiId);
        return R.ok(MessageUtils.message("system.info.success"));
    }


    /**
     * 评论动态
     *
     * @param id     动态ID
     * @param userId 评论的哪个用户
     * @return
     */
    @PostMapping("/comment")
    public R<Object> comment(@RequestParam(name = "id") Long id,
                             @RequestParam(name = "content") String content,
                             @RequestParam(name = "userId", required = false) Long userId) {
        if (StringUtils.isBlank(content)) {
            return R.fail("评论内容不能为空!");
        }
        AppDynamicVo appDynamicVo = iAppDynamicService.queryById(id);
        if (appLikeNotesService.queryBlock(appDynamicVo.getUserId(), getUserId())) {
            return R.fail("对方拉黑了你，不能评论");
        }
        if (StringUtils.isNotBlank(content)) {
            SensitiveFilter sensitiveFilter = SensitiveFilter.DEFAULT;
            content = sensitiveFilter.filter(content, '*');
        }
        AppDynamicCommentBo appDynamicCommentBo = iAppDynamicCommentService.addComment(id, getUserId(), userId, content);
        if (!getUserId().equals(appDynamicVo.getUserId())) {
            AppNoticeBo appNoticeBo = new AppNoticeBo();
            if (userId == null) {
                appNoticeBo.setTitle("评论了你的动态");
                appNoticeBo.setFromId(getUserId());
                appNoticeBo.setUserId(appDynamicVo.getUserId());
                appNoticeBo.setContent(content);
                appNoticeBo.setStatusRead(Constants.MSG_UNREAD);
                appNoticeBo.setRecordId(id);
                appNoticeBo.setType("2");
                appNoticeService.insertByBo(appNoticeBo);
            }
        }
        return R.ok(MessageUtils.message("system.info.success"), appDynamicCommentBo);
    }

    /**
     * 删除评论
     *
     * @param id 评论的记录ID
     * @return
     */
    @PostMapping("/deleteComment")
    public R<Void> deleteComment(Long id) {
        AppDynamicCommentVo commentVo = iAppDynamicCommentService.queryById(id);
        if (Objects.isNull(commentVo)) {
            return R.fail("记录不存在!");
        }
        if (!commentVo.getUserId().equals(LoginHelper.getUserId())) {
            return R.fail("只能删除自己的评论!");
        }
        iAppDynamicCommentService.delete(commentVo.getId());
        return R.ok(MessageUtils.message("system.info.success"));
    }

    /**
     * 根据ID删除动态
     *
     * @param id 主键
     */
    @PostMapping("/delete")
    public R<Void> remove(Long id) {
        AppDynamicVo appDynamicVo = iAppDynamicService.queryById(id);
        if (Objects.isNull(appDynamicVo)) {
            return R.fail(MessageUtils.message("data.not.exist"));
        }
        if (!appDynamicVo.getUserId().equals(LoginHelper.getUserId())) {
            return R.fail(MessageUtils.message("moments.not.delete.rule"));
        }
        if (iAppDynamicService.delete(id)) {
            appRecordService.delete(id, NoticType.LIKE.getCode());
        }
        return R.ok(MessageUtils.message("system.info.success"));
    }


    /**
     * 我点赞的动态列表
     */
    @GetMapping("/myLikePage")
    public TableDataInfo<AppRecordVo> myLikePage(PageQuery pageQuery) {
        Long id = getUserId();
        AppRecordBo recordBo = new AppRecordBo();
        recordBo.setUserId(id);
        recordBo.setType(Constants.FAIL);
        TableDataInfo<AppRecordVo> page = appRecordService.queryPageList(recordBo, pageQuery);
        page.getData().forEach(e -> {
            AppDynamicVo info = iAppDynamicService.findOne(e.getRecordId(), id);
            e.setAppDynamicVo(info);
        });
        return page;
    }


    /**
     * 点赞我的动态列表
     */
    @GetMapping("/likeMyPage")
    public TableDataInfo<AppRecordVo> likeMyPage(PageQuery pageQuery) {
        Long id = getUserId();
        AppRecordBo recordBo = new AppRecordBo();
        recordBo.setRecordUserId(id);
        recordBo.setType(Constants.FAIL);
        TableDataInfo<AppRecordVo> page = appRecordService.queryPageList(recordBo, pageQuery);
        page.getData().forEach(e -> {
            AppDynamicVo info = iAppDynamicService.findOne(e.getRecordId(), id);
            e.setAppDynamicVo(info);
            e.setUserVo(appUserService.userFilterVo(e.getUserId()));
        });
        return page;
    }
}
