package com.ruoyi.web.controller.app;

import com.ruoyi.app.domain.AppVipOrder;
import com.ruoyi.app.domain.bo.AppVipOrderBo;
import com.ruoyi.app.domain.vo.AppVipOrderVo;
import com.ruoyi.app.domain.vo.AppVipTypeVo;
import com.ruoyi.app.service.IAppUserVipService;
import com.ruoyi.app.service.IAppVipOrderService;
import com.ruoyi.app.service.IAppVipTypeService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.ios.IosVerify;
import com.ruoyi.framework.config.properties.PayUrlProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.*;

/**
 * APPv会员VIP订单
 *
 * <AUTHOR>
 * @date 2025-03-17
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/vipOrder")
@Slf4j
public class AppVipOrderController extends BaseController {

    private final IAppVipOrderService iAppVipOrderService;
    private final IAppVipTypeService iAppVipTypeService;
    private final IAppUserVipService iAppUserVipService;
    private final PayUrlProperties payUrlProperties;

    /**
     * 查询APP会员VIP订单列表
     */
    @GetMapping("/list")
    public TableDataInfo<AppVipOrderVo> list(AppVipOrderBo bo, PageQuery pageQuery) {
        return iAppVipOrderService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取APPv会员VIP订单详细信息
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    public R<AppVipOrderVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(iAppVipOrderService.queryById(id));
    }

    /**
     * APP会员VIP下单
     */
    @Log(title = "APP会员VIP订单", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<AppVipOrder> add(@RequestParam(name = "vipId") Long vipId) {
        AppVipTypeVo appVipTypeVo = iAppVipTypeService.queryById(vipId);
        if (Objects.isNull(appVipTypeVo)) {
            return R.fail("记录ID错误！");
        }
        return R.ok(iAppVipOrderService.createOrder(appVipTypeVo, getUserId()));
    }

    /**
     * VIP支付签名
     *
     * @param payType 支付类型
     * @param orderNo 订单号
     * @return
     */
    @PostMapping("sign")
    public R<Object> sign(@RequestParam(name = "payType") Integer payType,
                          @RequestParam(name = "orderNo") String orderNo,
                          @RequestParam(name = "code", required = false) String code,
                          HttpServletRequest request) throws Exception {
        Map<String, String> params = new HashMap<>(2);
        AppVipOrderVo vipOrder = iAppVipOrderService.queryOrderNo(orderNo);
        AppVipTypeVo vipPackage = iAppVipTypeService.queryById(vipOrder.getVipId());
        Map<String, String> map = new LinkedHashMap<>();
        params.put("body", "Honey bb");
        params.put("subject", "Honey bb");
        params.put("tradeNo", orderNo);
        params.put("amount", vipPackage.getAndroidPrice().toString());
        if (1 == payType) {
            params.put("notifyUrl", payUrlProperties.getAlipayNotifyUrl());
            map.put("sign", null);
//            log.info("支付宝签名SIGN：{}", sign);
            return R.ok(map);
        }
        if (2 == payType) {
            params.put("attach", null);
            params.put("notifyUrl", payUrlProperties.getWechatNotifyUrl());
//            log.info("微信支付签名SIGN：{}", signParam);
            return R.ok();
        }
        if (3 == payType) {
            return R.ok();
        }
        return R.fail("签名错误！");
    }

    /**
     * 苹果开通VIP支付调用
     *
     * @param orderNo 订单号
     * @param receipt 苹果数据
     * @return
     */
    @PostMapping("iosPaySuccess")
    public R<Object> iosPaySuccess(@RequestParam("orderNo") String orderNo, @RequestParam("receipt") String receipt) {
        Map<String, Object> result;
        try {
            receipt = URLDecoder.decode(receipt, "utf-8");
            log.info("----------------------开通VIP功能苹果返回数据:" + receipt);
            result = IosVerify.buyAppVerify(receipt);
            boolean success = (boolean) result.get("success");
            if (!success) {
                return R.fail("签名验证错误");
            }
            log.info("----------------------苹果支付解密数据:" + result);
            String productId = result.get("product_id").toString();
            String externalOrderNo = result.get("entity").toString();
            AppVipOrderVo vipOrder = iAppVipOrderService.queryOrderNo(orderNo);
            if (Objects.isNull(vipOrder) || vipOrder.getStatus().equals(Constants.FAIL)) {
                return R.fail("订单不存在或已处理");
            }
            AppVipTypeVo appVipTypeVo = iAppVipTypeService.queryById(vipOrder.getVipId());
            if (!appVipTypeVo.getIosName().equals(productId)) {
                return R.fail("充值金额和套餐不一致!");
            }
            iAppUserVipService.addVipTime(vipOrder, externalOrderNo, orderNo, "ios");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return R.ok("成功");
    }
}
