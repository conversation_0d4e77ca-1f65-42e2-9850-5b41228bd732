package com.ruoyi.web.controller.app;

import com.easemob.im.server.model.EMBlock;
import com.ruoyi.app.domain.bo.AppAddressBookBo;
import com.ruoyi.app.domain.vo.AppAddressBookVo;
import com.ruoyi.app.domain.vo.AppUserVo;
import com.ruoyi.app.domain.vo.UserVo;
import com.ruoyi.app.service.IAppAddressBookService;
import com.ruoyi.app.service.IAppUserService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.helper.LoginHelper;
import com.ruoyi.common.utils.EntityCopyUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.im.core.EasemobTemplate;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * APP用户的通讯录
 *
 * <AUTHOR>
 * @date 2025-12-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/book")
public class AppAddressBookController extends BaseController {

    private final IAppUserService appUserService;
    private final IAppAddressBookService appAddressBookService;


    /**
     * 查询APP用户的通讯录列表
     */
    @GetMapping("list")
    public R<List<AppUserVo>> list(AppAddressBookBo bo) {
        bo.setUserId(getUserId());
        List<AppAddressBookVo> books = appAddressBookService.queryList(bo);
        ArrayList<AppUserVo> userVos = new ArrayList<>();
        books.forEach(e -> {
            AppUserVo appUserVo = appUserService.queryById(e.getBookId());
            if (StringUtils.isNotBlank(e.getRemark())) {
                appUserVo.setRemark(e.getRemark());
            }
            appUserVo.setBookId(e.getId());
            userVos.add(appUserVo);
        });
        return R.ok(userVos);
    }

    /**
     * 查询环信APP用户的通讯录列表
     */
    @GetMapping("easemobList")
    public R<List<AppAddressBookVo>> easemobList() {
        Long id = LoginHelper.getUserId();
        EasemobTemplate imTemplate = SpringUtils.getBean(EasemobTemplate.class);
        List<String> list;
        try {
            list = imTemplate.friendList(userName(id));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
        ArrayList<AppAddressBookVo> books = new ArrayList<>();
        list.forEach(e -> {
            AppAddressBookVo book = new AppAddressBookVo();
            book.setUserId(LoginHelper.getUserId());
            Long friendId = Long.valueOf(e.substring(4));
            book.setBookId(friendId);
            AppAddressBookVo appAddressBookVo = appAddressBookService.queryBook(id, friendId);
            book.setRemark(appAddressBookVo.getRemark());
            //用户信息
            AppUserVo appUserVo = appUserService.queryById(friendId);
            UserVo userVo = new UserVo();
            EntityCopyUtils.copyPropertiesIgnoreNull(appUserVo, userVo);
            book.setUserVo(userVo);
            books.add(book);
        });
        return R.ok(books);
    }

    /**
     * 获取用户的禁言列表
     */
    @GetMapping("silencedList")
    public R<ArrayList<UserVo>> silencedList() {
        EasemobTemplate imTemplate = SpringUtils.getBean(EasemobTemplate.class);
        List<EMBlock> list;
        try {
            list = imTemplate.getUsersBlockedFromSendMsgToUser(userName(getUserId()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
        ArrayList<UserVo> data = new ArrayList<>();
        list.forEach(e -> data.add(appUserService.queryByIdUserVo(userId(e.getUsername()))));
        return R.ok(data);
    }

    /**
     * 禁言用户
     * 不让发送信息
     */
    @PostMapping("estoppel")
    public R<Void> estoppel(@RequestParam(name = "id") Long id) {
        if (id.equals(getUserId())) {
            return R.fail("不能禁言自己!");
        }
        EasemobTemplate imTemplate = SpringUtils.getBean(EasemobTemplate.class);
        try {
            imTemplate.blockUserSendMsgToUser(userName(id), userName(getUserId()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
        return R.ok();
    }

    /**
     * 解除禁言用户
     * 重新让发送信息
     */
    @PostMapping("removeEstoppel")
    public R<Void> removeEstoppel(@RequestParam(name = "id") Long id) {
        EasemobTemplate imTemplate = SpringUtils.getBean(EasemobTemplate.class);
        try {
            imTemplate.unblockUserSendMsgToUser(userName(id), userName(getUserId()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
        return R.ok();
    }

    /**
     * 修改APP用户的通讯录备注
     */
    @Log(title = "APP用户的通讯录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> edit(@RequestParam(name = "id") Long id,
                        @RequestParam(name = "remark") String remark
    ) {
        if (StringUtils.isBlank(remark)) {
            return R.fail("备注不能空!");
        }
        return toAjax(appAddressBookService.updateByUserIdAndRemark(id, getUserId(), remark));
    }
}
