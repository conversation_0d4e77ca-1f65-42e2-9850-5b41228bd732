package com.ruoyi.web.controller.app.moments;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ruoyi.app.domain.bo.AppDynamicVideoBo;
import com.ruoyi.app.domain.vo.AppDynamicVideoVo;
import com.ruoyi.app.service.IAppDynamicVideoService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.enums.BusinessType;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;

/**
 * APP动态视频信息
 *
 * <AUTHOR>
 * @date 2025-01-11
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/dynamicVideo")
public class AppDynamicVideoController extends BaseController {

    private final IAppDynamicVideoService iAppDynamicVideoService;

    /**
     * 获取APP动态视频信息详细信息
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    public R<AppDynamicVideoVo> getInfo(@NotNull(message = "主键不能为空")
                                        @PathVariable Long id) {
        return R.ok(iAppDynamicVideoService.queryById(id));
    }

    /**
     * 新增APP动态视频信息
     */
    @SaCheckPermission("app:dynamicVideo:add")
    @Log(title = "APP动态视频信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AppDynamicVideoBo bo) {
        return toAjax(iAppDynamicVideoService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 删除APP动态视频信息
     *
     * @param ids 主键串
     */
    @SaCheckPermission("app:dynamicVideo:remove")
    @Log(title = "APP动态视频信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iAppDynamicVideoService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
