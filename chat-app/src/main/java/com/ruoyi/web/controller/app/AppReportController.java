package com.ruoyi.web.controller.app;

import com.ruoyi.app.domain.bo.AppReportBo;
import com.ruoyi.app.domain.bo.AppUserReportBo;
import com.ruoyi.app.domain.vo.AppReportVo;
import com.ruoyi.app.service.IAppReportService;
import com.ruoyi.app.service.IAppUserReportService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.enums.BusinessType;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * APP举报信息
 *
 * <AUTHOR>
 * @date 2025-01-03
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/report")
public class AppReportController extends BaseController {

    private final IAppUserReportService appUserReportService;
    private final IAppReportService appReportService;


    /**
     * 查询APP举报类型列表
     */
    @GetMapping("list")
    public R<List<AppReportVo>> list(AppReportBo bo) {
        bo.setStatus(Constants.SUCCESS);
        return R.ok(appReportService.queryList(bo));
    }


    /**
     * 用户举报
     */
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) AppUserReportBo bo) {
        bo.setStatus(Constants.FAIL);
        return toAjax(appUserReportService.insertByBo(bo) ? 1 : 0);
    }
}
