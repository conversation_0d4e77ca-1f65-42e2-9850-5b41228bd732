package com.ruoyi.web.controller.app;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.web.agora.AgoraToken;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;


@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/agoraRtc")
public class AgoraRtcController extends BaseController {

    private final AgoraToken agoraToken;

    /**
     * 创建语音房间TOKEN
     *
     * @return
     */
    @PostMapping(value = "/createToken")
    public R<Object> createToken(String channelName) {
        HashMap<String, Object> hashMap = new HashMap<>(1);
        hashMap.put("token", agoraToken.buildTokenWithUid(channelName, getUserId().intValue()));
        return R.ok(hashMap);
    }

}
