package com.ruoyi.web.controller.app;

import com.ruoyi.app.domain.bo.AppRecordBo;
import com.ruoyi.app.domain.vo.*;
import com.ruoyi.app.service.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.NoticType;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

/**
 * APP用户通知记录
 *
 * <AUTHOR>
 * @date 2025-04-27
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/record")
public class AppRecordController extends BaseController {


    private final IAppRecordService iAppRecordService;


    /**
     * 查询APP用户通知记录列表
     */
    @GetMapping("/page")
    public TableDataInfo<AppRecordVo> page(AppRecordBo bo, PageQuery pageQuery) {
        bo.setRecordUserId(getUserId());
        pageQuery.setOrderByColumn("createTime");
        TableDataInfo<AppRecordVo> page = iAppRecordService.queryPageList(bo, pageQuery);
        List<AppRecordVo> recordVos = iAppRecordService.queryList(bo);
        recordVos.stream().filter(e -> e.getReadStatus().equals(Constants.SUCCESS)).forEach(e -> iAppRecordService.updateByBo(e.getId(), Constants.FAIL));
        return page;
    }

    /**
     * 查询APP用户通知角标数量
     */
    @GetMapping("/getNumber")
    public R<?> getNumber() {
        HashMap<String, Object> map = new HashMap<>();
        AppRecordBo recordBo = new AppRecordBo();
        recordBo.setRecordUserId(getUserId());
        recordBo.setReadStatus(Constants.SUCCESS);
        List<AppRecordVo> appRecordVos = iAppRecordService.queryList(recordBo);
        map.put("noticeNumber", appRecordVos.size());
        return R.ok(map);
    }
}
