package com.ruoyi.web.controller.app;

import com.ruoyi.app.domain.bo.AppRecordBo;
import com.ruoyi.app.domain.vo.*;
import com.ruoyi.app.service.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.NoticType;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * APP用户通知记录
 *
 * <AUTHOR>
 * @date 2023-04-27
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/record")
public class AppRecordController extends BaseController {

    private final IAppUserService iAppUserService;
    private final IAppRecordService iAppRecordService;
    private final IAppDynamicService iAppDynamicService;
    private final IAppDynamicVideoService iAppDynamicVideoService;
    private final IAppDynamicCommentService iAppDynamicCommentServicel;


    /**
     * 查询APP用户通知记录列表
     */
    @GetMapping("/page")
    public TableDataInfo<AppRecordVo> page(AppRecordBo bo, PageQuery pageQuery) {
        pageQuery.setOrderByColumn("createTime");
        bo.setRecordUserId(getUserId());
        TableDataInfo<AppRecordVo> page = iAppRecordService.queryPageList(bo, pageQuery);
        page.getData().forEach(e -> {
            AppUserVo userVo = iAppUserService.queryById(e.getUserId());
            e.setNickName(userVo.getNickName());
            e.setAvatar(userVo.getAvatar());
            AppDynamicVo appDynamicVo = iAppDynamicService.queryById(e.getRecordId());
            if (Objects.nonNull(appDynamicVo)) {
                e.setContext(appDynamicVo.getContext());
                AppDynamicVideoVo dynamicVideoVo = iAppDynamicVideoService.queryOne(e.getRecordId());
                if (Objects.nonNull(dynamicVideoVo)) {
                    e.setImage(dynamicVideoVo.getImage());
                }
            }
            if (bo.getType().equals(NoticType.COMMENT.getCode())) {
                AppDynamicCommentVo commentVo = iAppDynamicCommentServicel.queryById(e.getCommentId());
                if (Objects.nonNull(commentVo)) {
                    e.setComment(commentVo.getContext());
                }
            }
        });
        List<AppRecordVo> recordVos = iAppRecordService.queryList(bo);
        recordVos.stream().filter(e -> e.getReadStatus().equals(Constants.SUCCESS)).forEach(e -> iAppRecordService.updateByBo(e.getId(), Constants.FAIL));
        return page;
    }


    /**
     * 删除APP用户通知记录
     *
     * @param ids 主键串
     */
    @Log(title = "APP用户通知记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iAppRecordService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }

    
}
