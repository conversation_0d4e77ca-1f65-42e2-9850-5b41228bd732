package com.ruoyi.web.controller.app.label;

import com.ruoyi.app.domain.bo.AppLabelLikeBo;
import com.ruoyi.app.domain.vo.AppLabelLikeVo;
import com.ruoyi.app.domain.vo.AppLabelVo;
import com.ruoyi.app.domain.vo.UserVo;
import com.ruoyi.app.service.IAppLabelLikeService;
import com.ruoyi.app.service.IAppLabelService;
import com.ruoyi.app.service.IAppUserService;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.validate.QueryGroup;
import com.ruoyi.common.enums.LikeStatus;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * APP用户标签喜欢
 *
 * <AUTHOR>
 * @date 2023-01-13
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/labelLike")
public class AppLabelLikeController extends BaseController {

    private final IAppLabelLikeService iAppLabelLikeService;
    private final IAppLabelService appLabelService;
    private final IAppUserService appUserService;

    /**
     * 查询APP用户标签喜欢列表分页
     */
    @GetMapping("/page")
    public TableDataInfo<AppLabelLikeVo> page(AppLabelLikeBo bo, PageQuery pageQuery) {
        TableDataInfo<AppLabelLikeVo> page = iAppLabelLikeService.queryPageList(bo, pageQuery);
        page.getData().forEach(e -> {
            UserVo userVo = appUserService.queryByIdUserVo(e.getUserId());
            userVo.setUuid(null);
            e.setUserVo(userVo);
        });
        return page;
    }

    /**
     * 查询APP用户标签喜欢列表不分页
     */
    @GetMapping("/list")
    public R<List<AppLabelLikeVo>> list(AppLabelLikeBo bo) {
        bo.setUserId(getUserId());
        List<AppLabelLikeVo> userLabels = iAppLabelLikeService.queryList(bo);
        userLabels.forEach(e -> {
            AppLabelVo labelVo = appLabelService.queryById(e.getLabelId());
            e.setLabel(labelVo);
        });
        return R.ok(userLabels);
    }

    /**
     * 批量更新用户选择喜欢的标签
     */
    @RepeatSubmit()
    @PostMapping("setLike")
    public R<ArrayList<AppLabelLikeBo>> setLike(@RequestParam(name = "ids") String ids) {
        String[] userIds = ids.split(",");
        //获取用户的超级喜欢标签列表
        AppLabelLikeBo likeBo = new AppLabelLikeBo();
        likeBo.setLikeStatus(LikeStatus.SUPER_LIKE.getCode());
        likeBo.setUserId(getUserId());
        List<AppLabelLikeVo> appLabelLikeVos = iAppLabelLikeService.queryList(likeBo);
        HashMap<Long, String> likeMaps = new HashMap<>();
        appLabelLikeVos.forEach(e -> likeMaps.put(e.getLabelId(), e.getCause()));
        //添加用户喜欢标签
        iAppLabelLikeService.deletes(getUserId());
        List<String> labelIds = Arrays.asList(userIds);
        ArrayList<AppLabelLikeBo> appLabelLikeBos = new ArrayList<>();
        labelIds.forEach(e -> {
            Long id = Long.valueOf(e);
            AppLabelLikeBo like = new AppLabelLikeBo();
            like.setLabelId(id);
            like.setStatus(Constants.SUCCESS);
            like.setUserId(getUserId());
            AppLabelVo appLabelVo = appLabelService.queryById(id);
            like.setTypeId(appLabelVo.getTypeId());
            if (likeMaps.containsKey(id)) {
                like.setLikeStatus(LikeStatus.SUPER_LIKE.getCode());
                like.setCause(likeMaps.get(id));
            } else {
                like.setLikeStatus(LikeStatus.LIKE.getCode());
            }
            iAppLabelLikeService.insertByBo(like);
            like.setLabel(appLabelVo);
            appLabelLikeBos.add(like);
        });
        return R.ok(appLabelLikeBos);
    }

    /**
     * 用户喜欢单个标签
     */
    @RepeatSubmit()
    @PostMapping("setLikeOne")
    public R<Object> setLikeOne(@RequestParam(name = "id") Long id) {
        AppLabelVo appLabelVo = appLabelService.queryById(id);
        if (Objects.isNull(appLabelVo)) {
            return R.fail("标签不存在!");
        }
        AppLabelLikeVo appLabelLikeVos = iAppLabelLikeService.queryLikeLabel(getUserId(), id);
        if (Objects.isNull(appLabelLikeVos)) {
            AppLabelLikeBo likeBo = new AppLabelLikeBo();
            likeBo.setLikeStatus(LikeStatus.LIKE.getCode());
            likeBo.setUserId(getUserId());
            likeBo.setTypeId(appLabelVo.getTypeId());
            likeBo.setLabelId(id);
            if (iAppLabelLikeService.insertByBo(likeBo)) {
                AppLabelLikeBo appLabelLikeBo = new AppLabelLikeBo();
                appLabelLikeBo.setLabelId(appLabelVo.getId());
                appLabelLikeBo.setLikeStatus(LikeStatus.LIKE.getCode());
                ArrayList<UserVo> userVos = new ArrayList<>();
                List<AppLabelLikeVo> userLikeLabels = iAppLabelLikeService.queryList(appLabelLikeBo);
                userLikeLabels.forEach(e -> userVos.add(appUserService.queryByIdUserVo(e.getUserId())));
                return R.ok("添加标签成功", userVos);
            }
            return R.ok("添加标签失败!");
        } else {
            return R.fail("已添加为我的标签");
        }
    }

    /**
     * 获取最近更新的超级喜欢列表
     *
     * @param
     */
    @GetMapping("newList")
    public TableDataInfo<AppLabelLikeVo> newList(PageQuery pageQuery) {
        AppLabelLikeBo likeBo = new AppLabelLikeBo();
        likeBo.setLikeStatus(LikeStatus.SUPER_LIKE.getCode());
        TableDataInfo<AppLabelLikeVo> list = iAppLabelLikeService.queryPageList(likeBo, pageQuery);
        list.getData().forEach(e -> {
            e.setLabel(appLabelService.queryById(e.getLabelId()));
            e.setUserVo(appUserService.queryByIdUserVo(e.getUserId()));
        });
        return list;
    }

    /**
     * 设置用户的超级喜欢标签
     */
    @RepeatSubmit()
    @PostMapping("setLabelLike")
    public R<Object> setLabelLike(@Validated(QueryGroup.class) AppLabelLikeBo labelLikeBo) {
        labelLikeBo.setUserId(getUserId());
        AppLabelLikeBo like = new AppLabelLikeBo();
        like.setUserId(getUserId());
        like.setLikeStatus(LikeStatus.SUPER_LIKE.getCode());
        List<AppLabelLikeVo> appLabelLikeVos = iAppLabelLikeService.queryList(like);
        if (appLabelLikeVos.size() >= 3) {
            return R.fail("最多三个超级喜欢标签");
        }
        return R.result(iAppLabelLikeService.setLabelLike(labelLikeBo), labelLikeBo);
    }

    /**
     * 删除用户的超级喜欢标签
     *
     * @param id 喜欢的标签ID
     */
    @PostMapping("cancelLabelLike")
    public R<Void> cancelLabelLike(@RequestParam(name = "id") Long id) {
        return R.result(iAppLabelLikeService.cancelLabelLike(id, getUserId()));
    }
}
