package com.ruoyi.web.controller.app;

import cn.dev33.satoken.annotation.SaIgnore;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.easemob.im.server.api.token.Token;
import com.ruoyi.app.domain.AppFriend;
import com.ruoyi.app.domain.AppUser;
import com.ruoyi.app.domain.Message;
import com.ruoyi.app.domain.bo.*;
import com.ruoyi.app.domain.vo.*;
import com.ruoyi.app.service.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.constant.*;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.FindStatus;
import com.ruoyi.common.enums.LikeStatus;
import com.ruoyi.common.enums.TableNumberType;
import com.ruoyi.common.helper.LoginHelper;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.EntityCopyUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.redis.RedisUtils;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.im.core.EasemobTemplate;
import com.ruoyi.web.service.message.SendMessageService;
import com.ruoyi.web.service.message.SendPushService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

/**
 * APP用户关注
 *
 * <AUTHOR>
 * @date 2025-01-04
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/friend")
public class AppFriendController extends BaseController {


    private final IAppUserService appUserService;
    private final IAppAssetService appAssetService;
    private final IAppGroupService appGroupService;
    private final IAppFriendService appFriendService;
    private final IAppUserVipService iAppUserVipService;
    private final IAppLikeNotesService iAppLikeNotesService;
    private final IAppLikeNumberService appLikeNumberService;
    private final IAppAddressBookService appAddressBookService;
    private final IAppGroupUserService appGroupUserService;
    private final IAppNoticeService iAppNoticeService;
    private final SendMessageService sendMessageService;
    private final SendPushService sendPushService;


    /**
     * 查找用户或者群组
     *
     * @param type 搜索类型[1=用户，2=群组]
     * @param name 搜索的名字，手机，群名称
     * @return
     */
    @GetMapping("find")
    public R<Object> find(@RequestParam(name = "type") Integer type,
                          @RequestParam(name = "name") String name) {
        if (type.equals(FindStatus.USER.getCode())) {
            AppUserBo userBo = new AppUserBo();
            userBo.setNickName(name);
            userBo.setUserName(name);
            List<AppUserVo> list = appUserService.findNameList(userBo);
            ArrayList<UserVo> userVos = new ArrayList<>();
            list.forEach(e -> {
                UserVo user = new UserVo();
                EntityCopyUtils.copyPropertiesIgnoreNull(e, user);
                user.setIsRelation(Objects.nonNull(appAddressBookService.queryBook(getUserId(), e.getId())) ? Constants.TWO : Constants.STRANG);
                userVos.add(user);
            });
            return R.ok(userVos);
        } else if (type.equals(FindStatus.GROUP.getCode())) {
            AppGroupBo appGroupBo = new AppGroupBo();
            appGroupBo.setGroupId(name);
            appGroupBo.setName(name);
            List<AppGroupVo> appGroupVos = appGroupService.queryNameList(appGroupBo);
            appGroupVos.forEach(e -> {
                List<AppGroupUserVo> appGroupUserVo = appGroupUserService.queryList(e.getId());
                ArrayList<AppUserVo> userVos = new ArrayList<>();
                appGroupUserVo.forEach(i -> userVos.add(appUserService.queryById(i.getUserId())));
                e.setUserVoList(userVos);
                AppGroupUserVo myGroup = appGroupUserService.queryByGroupAndUserId(e.getId(), getUserId());
                e.setIsJoin(Objects.nonNull(myGroup));
            });
            return R.ok(appGroupVos);
        } else {
            return R.fail("查找类型错误!");
        }
    }


    /**
     * 二维码功能
     *
     * @param type 搜索类型[1=用户，2=群组]
     * @param id   搜索的名字，手机，群名称
     * @return
     */
    @GetMapping("findCode")
    public R<Object> findCode(@RequestParam(name = "type") Integer type,
                              @RequestParam(name = "id") Long id) {
        if (type.equals(FindStatus.USER.getCode())) {
            UserVo e = appUserService.queryByIdUserVo(id);
            if (Objects.isNull(e)) {
                return R.fail("用户不存在！");
            }
            e.setIsRelation(appFriendService.userRelation(getUserId(), id));
            return R.ok(e);
        } else if (type.equals(FindStatus.GROUP.getCode())) {
            AppGroupVo appGroupVo = appGroupService.queryByGroupId(id.toString());
            if (Objects.isNull(appGroupVo)) {
                return R.fail("群组不存在！");
            }
            AppGroupUserVo appGroupUserVo = appGroupUserService.queryByGroupAndUserId(appGroupVo.getId(), getUserId());
            appGroupVo.setIsJoin(Objects.nonNull(appGroupUserVo));
            return R.ok(appGroupVo);
        } else {
            return R.fail("查找类型错误!");
        }
    }

    /**
     * 根据环信用户名查询用户信息
     *
     * @param chatName 环信用户名
     * @return
     */
    @GetMapping("findChatName")
    public R<UserVo> findChatName(
        @RequestParam(name = "chatName") String chatName
    ) {
        Long id = userId(chatName);
        UserVo userVo = appUserService.queryByIdUserVo(userId(chatName));
        if (Objects.isNull(userVo)) {
            return R.fail("用户不存在！");
        }
        userVo.setIsRelation(Objects.nonNull(appAddressBookService.queryBook(getUserId(), id)) ? Constants.TWO : Constants.STRANG);
        return R.ok(userVo);
    }

    @SaIgnore
    @GetMapping("sendMsg")
    public R<Object> find() {
        Message message = new Message();
        HashSet<Long> ids = new HashSet<>();
        ids.add(496L);
        message.setToUserIds(ids);
        message.setTitle("我是秦始皇");
        message.setContext("赶紧给我打钱");
        message.setType(MessageConstants.SUPER_LIKE);
        EasemobTemplate imTemplate = SpringUtils.getBean(EasemobTemplate.class);
        Token token;
        try {
            token = imTemplate.appToken();
        } catch (Exception e) {
            log.info("添加通讯录好友失败：{}", e.getMessage());
            return R.fail(e.getMessage());
        }
        message.setToken(token.getValue());
        message.setIconUrl("http://heike-chat.oss-cn-shanghai.aliyuncs.com/2023/03/28/aaaadf9e8f3640ec91554def39682424.jpg");
        sendPushService.messagePush(message);
        return R.ok();
    }

    /**
     * 添加好友申请
     *
     * @param id 用户ID
     * @return
     */
    @PostMapping("add")
    public R<String> add(
        @RequestParam(name = "id") Long id,
        @RequestParam(name = "msg", required = false) String msg
    ) {
        if (id.equals(getUserId())) {
            return R.fail("不能添加自己为好友!");
        }
        AppUserVo userVo = appUserService.queryById(id);
        if (Objects.isNull(userVo)) {
            return R.fail("用户不存在!");
        }
        if (iAppLikeNotesService.queryBlock(id, getUserId())) {
            return R.fail("对方拉黑了你，不能添加好友");
        }
        if (StringUtils.isBlank(msg)) {
            msg = "我是" + "\"" + userVo.getNickName() + "\"";
        }
        if (appFriendService.addFridend(getUserId(), id, msg)) {
            EasemobTemplate imTemplate = SpringUtils.getBean(EasemobTemplate.class);
            try {
                imTemplate.addFriend(userName(), userName(id));
            } catch (Exception e) {
                log.info("添加通讯录好友失败：{}", e.getMessage());
                return R.fail(e.getMessage());
            }
            sendData(getUserId(), id);
            return R.ok("添加好友成功");
        } else {
            return R.ok("发送好友申请成功");
        }

    }

    /**
     * 获取好友申请列表
     *
     * @return
     */
    @GetMapping("applyList")
    public R<Object> applyList(
        @RequestParam(name = "number", defaultValue = "4") Integer number
    ) {
        String key = CacheConstants.APP_USER_FRIEND + getUserId();
        Map<String, Map<String, Object>> map = RedisUtils.getCacheMap(key);
        ArrayList<AppApplyInfoVo> list = new ArrayList<>();
        for (String i : map.keySet()) {
            AppApplyInfoVo appApplyInfoVo = new AppApplyInfoVo();
            Long id = Long.valueOf(i);
            appApplyInfoVo.setId(id);
            appApplyInfoVo.setMsg(map.get(i).get("msg").toString());
            Long time = Long.valueOf(map.get(i).get("time").toString());
            appApplyInfoVo.setTime(time);
            appApplyInfoVo.setCreateTime(new Date(time));
            UserVo user = appUserService.queryByIdUserVo(id);
            appApplyInfoVo.setUserVo(user);
            list.add(appApplyInfoVo);
        }
        list.sort(Comparator.comparing(AppApplyInfoVo::getTime).reversed());
        HashMap<String, Object> hashMap = new HashMap<>(2);
        hashMap.put("applyList", list);
        hashMap.put("newUserList", appUserService.randNumberUser(getUserId(), number));
        appLikeNumberService.update(getUserId(), TableNumberType.ADD.getCode());
        return R.ok(hashMap);
    }

    /**
     * 获取新增朋友数量
     *
     * @return
     */
    @GetMapping("frientNumber")
    public R<Object> frientNumber() {
        //申请好友数量
        HashMap<String, Integer> data = new HashMap<>(3);
        AppLikeNumberBo appLikeNumber = new AppLikeNumberBo();
        appLikeNumber.setUserId(getUserId());
        appLikeNumber.setType(TableNumberType.ADD.getCode());
        List<AppLikeNumberVo> appLikeNumberVos = appLikeNumberService.queryList(appLikeNumber);
        data.put("frientNumber", appLikeNumberVos.size());
        //关注我的粉丝未读数量
        AppFriendBo bo = new AppFriendBo();
        bo.setFromStatus(Constants.SUCCESS);
        bo.setFriendId(getUserId());
        bo.setStatus(Constants.MSG_UNREAD);
        List<AppFriendVo> fans = appFriendService.queryList(bo);
        data.put("fansNumber", fans.size());
        //赞和收藏数量
        ArrayList<String> types = new ArrayList<>();
        types.add("4");
        types.add("5");
        List<AppNoticeVo> list = iAppNoticeService.queryList(getUserId(), types, Constants.MSG_UNREAD);
        data.put("likeNumber", list.size());
        //@和评论的数量
        ArrayList<String> type = new ArrayList<>();
        type.add("1");
        type.add("2");
        List<AppNoticeVo> commitList = iAppNoticeService.queryList(getUserId(), type, Constants.MSG_UNREAD);
        data.put("commitNumber", commitList.size());
        return R.ok(data);
    }

    /**
     * 获取我的角标数量
     *
     * @return
     */
    @GetMapping("subscriptNumber")
    public R<Object> subscriptNumber() {
        HashMap<String, Integer> data = new HashMap<>(4);
        AppFriendBo bo = new AppFriendBo();
        bo.setUserId(getUserId());
        bo.setFromStatus(Constants.SUCCESS);
        List<AppFriendVo> like = appFriendService.queryList(bo);
        data.put("likeNumber", like.size());
        AppFriendBo bo1 = new AppFriendBo();
        bo1.setFromStatus(Constants.SUCCESS);
        bo1.setFriendId(getUserId());
        List<AppFriendVo> fans = appFriendService.queryList(bo1);
        data.put("fansNumber", fans.size());
        ArrayList<String> types = new ArrayList<>();
        types.add("4");
        types.add("5");
        List<AppNoticeVo> list = iAppNoticeService.queryList(getUserId(), types, null);
        data.put("collectNumber", list.size());
        AppAssetVo appAssetVo = appAssetService.queryByUid(getUserId());
        data.put("balanceNumber", Objects.isNull(appAssetVo) ? 0 : appAssetVo.getIntegral().intValue());
        return R.ok(data);
    }


    /**
     * 拒绝好友申请
     *
     * @param id 用户ID
     * @return
     */
    @PostMapping("refuse")
    public R<Object> refuse(Long id) {
        String key = CacheConstants.APP_USER_FRIEND + LoginHelper.getUserId();
        Map<String, Map<String, Object>> data = RedisUtils.delCacheMapValue(key, id.toString());
        return R.ok(data);
    }

    /**
     * 申请通过好友
     *
     * @param id     添加的好友ID
     * @param remark 好友备注
     * @return
     */
    @PostMapping("pass")
    public R<Void> pass(@RequestParam(name = "id") Long id,
                        @RequestParam(name = "remark", required = false) String remark) {
        String key = CacheConstants.APP_USER_FRIEND + getUserId();
        Map<String, Map<String, Object>> data = RedisUtils.getCacheMapValue(key, id.toString());
        if (data.size() == 0) {
            return R.fail("好友申请已处理!");
        }
        if (appFriendService.passFridend(getUserId(), id, remark)) {
            EasemobTemplate imTemplate = SpringUtils.getBean(EasemobTemplate.class);
            try {
                imTemplate.addFriend(userName(), userName(id));
            } catch (Exception e) {
                log.info("添加通讯录好友失败：{}", e.getMessage());
                return R.fail();
            }
            //发送成为好友消息
            sendData(getUserId(), id);
        }
        //删除缓存的数据
        RedisUtils.delCacheMapValue(key, id.toString());
        return R.ok();
    }

    /**
     * 查询APP用户关注列表
     */
    @GetMapping("/likes")
    public TableDataInfo<AppFriendVo> list(AppFriendBo bo, PageQuery pageQuery) {
        if (StringUtils.isNotBlank(bo.getName())) {
            AppUserBo appUser = new AppUserBo();
            appUser.setNickName(bo.getName());
            List<AppUserVo> appUserVos = appUserService.queryList(appUser);
            List<Long> collect = appUserVos.stream().map(AppUserVo::getId).collect(Collectors.toList());
            if (!collect.isEmpty()) {
                bo.setFriendIds(collect);
            }
        }
        bo.setUserId(getUserId());
        bo.setFromStatus(Constants.SUCCESS);
        TableDataInfo<AppFriendVo> data = appFriendService.queryPageList(bo, pageQuery);
        data.getData().forEach(e -> {
            UserResultVo user = appUserService.filterateUserVo(e.getFriendId());
            AppFriendVo appFriendVo = appFriendService.queryUser(e.getFriendId(), getUserId());
            user.setIsRelation(Objects.nonNull(appFriendVo) ? Constants.TWO : Constants.LIKE);
            e.setUser(user);
            e.setCreateTimeFormat(DateUtils.getDatePoorSingle(e.getCreateTime()));
        });
        return data;
    }

    /**
     * 查询APP用户粉丝列表
     */
    @GetMapping("/fans")
    public TableDataInfo<AppFriendVo> fans(AppFriendBo bo, PageQuery pageQuery) {
        bo.setFromStatus(Constants.SUCCESS);
        bo.setFriendId(getUserId());
        TableDataInfo<AppFriendVo> data = appFriendService.queryPageList(bo, pageQuery);
        data.getData().forEach(e -> {
            UserResultVo user = appUserService.filterateUserVo(e.getUserId());
            AppFriendVo appFriendVo = appFriendService.queryUser(getUserId(), e.getUserId());
            user.setIsRelation(Objects.nonNull(appFriendVo) ? Constants.TWO : Constants.STRANG);
            e.setUser(user);
            e.setCreateTimeFormat(DateUtils.getDatePoorSingle(e.getCreateTime()));
        });
        return data;
    }

    /**
     * 查询APP用户新增粉丝列表
     * 新增标记清楚
     */
    @GetMapping("/newFans")
    public TableDataInfo<AppFriendVo> newFans(AppFriendBo bo, PageQuery pageQuery) {
        bo.setFriendId(getUserId());
        bo.setFromStatus(Constants.SUCCESS);
        TableDataInfo<AppFriendVo> data = appFriendService.queryPageList(bo, pageQuery);
        ArrayList<AppFriendVo> list = new ArrayList<>();
        data.getData().forEach(e -> {
            UserResultVo user = appUserService.filterateUserVo(e.getUserId());
            AppFriendVo appFriendVo = appFriendService.queryUser(getUserId(), e.getUserId());
            user.setIsRelation(Objects.nonNull(appFriendVo) ? Constants.TWO : Constants.STRANG);
            e.setUser(user);
            e.setCreateTimeFormat(DateUtils.getDatePoorSingle(e.getCreateTime()));
            list.add(e);
        });
        //把未读的所有记录，记录为已读
        list.forEach(e -> {
            e.setStatus(Constants.MSG_READ);
            appFriendService.updateByVo(e);
        });
        return data;
    }

    /**
     * 用户关注
     */
    @Log(title = "APP用户关注", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("like")
    public R<HashMap<String, Object>> like(@Validated(AddGroup.class) AppFriendBo appFriend) {
        if (getUserId().equals(appFriend.getFriendId())) {
            return R.fail("用户ID错误!");
        }
        if (iAppLikeNotesService.queryBlock(appFriend.getFriendId(), getUserId())) {
            return R.fail("对方拉黑了你，不能关注");
        }
        appFriend.setUserId(getUserId());
        HashMap<String, Object> hashMap = new HashMap<>(1);
        hashMap.put("isRelation", Constants.LIKE);
        appFriendService.addFriend(appFriend, LikeStatus.LIKE.getCode());
        //如果互为好友，添加到通讯录
        AppFriendVo relation = appFriendService.queryUser(appFriend.getFriendId(), getUserId());
        if (Objects.nonNull(relation)) {
            appAddressBookService.createFriend(getUserId(), appFriend.getFriendId());
            hashMap.put("isRelation", Constants.TWO);
        }
        //被关注通知
        if (appFriend.getFromStatus().equals(Constants.FAIL)) {
            Message message = new Message();
            message.setTitle("Ta刚刚关注了你");
            message.setContext("希望尽快得到你的回复");
            HashSet<Long> ids = new HashSet<>();
            ids.add(appFriend.getFriendId());
            message.setToUserIds(ids);
            message.setFromUserId(getUserId());
            message.setType(MessageConstants.LIKE_PUSH_TYPE);
            sendPushData(message);
        }
        return R.ok(hashMap);
    }

    /**
     * 关注置顶/添加备注
     */
    @Log(title = "APP用户关注修改", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("updateLike")
    public R<HashMap<String, Object>> updateLike(AppFriendBo appFriend) {
        if (appFriend.getId() == null) {
            return R.fail("ID不能为空！");
        }
        AppFriendVo appFriendVo = appFriendService.queryById(appFriend.getId());
        if (Objects.isNull(appFriendVo) || !appFriendVo.getUserId().equals(getUserId())) {
            return R.fail("关注信息不存在！");
        }
        if (StringUtils.isNotBlank(appFriend.getTopping())) {
            appFriendVo.setTopping(appFriend.getTopping());
        }
        if (StringUtils.isNotBlank(appFriend.getRemark())) {
            appFriendVo.setRemark(appFriend.getRemark());
        }
        return R.result(appFriendService.updateByVo(appFriendVo));
    }


    /**
     * 获取用户的右滑次数
     *
     * @return
     */
    @GetMapping("likeNumber")
    public R<Object> likeNumber() {
        HashMap<String, Object> map = new HashMap<>();
        String key = CacheConstants.DAY_LIKE_KEY + getUserId();
        Integer count = RedisUtils.getCacheObject(key) != null ? RedisUtils.getCacheObject(key) : 0;
        String vip = iAppUserVipService.userVip(getUserId());
        map.put("vip", StringUtils.isNotBlank(vip));
        map.put("likeNumber", CacheConstants.DAY_LIKE_NUMBER - count);
        String superKey = CacheConstants.SUPER_LIKE + getUserId().toString();
        Integer superCount = RedisUtils.getCacheObject(superKey) != null ? RedisUtils.getCacheObject(superKey) : 1;
        map.put("superLikeNumber", VipConstants.VIP_SUPER_LIKE + 1 - superCount);
        return R.ok(map);
    }


    /**
     * 用户取消关注
     *
     * @param friendId 操作用户ID
     */
    @Log(title = "用户取消关注", businessType = BusinessType.UPDATE)
    @PostMapping("cancelLike")
    public R<Void> cancelLike(@RequestParam(name = "friendId") Long friendId) {
        AppFriendVo appFriendVo = appFriendService.queryUser(getUserId(), friendId);
        if (Objects.isNull(appFriendVo)) {
            return R.fail("未关注用户!");
        }
        return R.result(appFriendService.canCelLike(appFriendVo));
    }


    /**
     * 删除好友
     *
     * @param friendId 操作用户ID
     */
    @Log(title = "删除好友", businessType = BusinessType.UPDATE)
    @PostMapping("removeLike")
    public R<Void> removeLike(@RequestParam(name = "friendId") Long friendId) {
        return R.result(appFriendService.canCelLike(getUserId(), friendId));
    }

    /**
     * 我的信息
     *
     * @return
     */
    @GetMapping("info")
    public R<UserVo> info() {
        return R.ok(appFriendService.info(getUserId()));
    }


    /**
     * 封装添加好友发送信息的方法
     *
     * @param fromId   发送用户ID
     * @param toUserId 接收用户ID
     */
    public void sendData(Long fromId, Long toUserId) {
        AppUserVo myUser = appUserService.queryById(fromId);
        AppUserVo userVo = appUserService.queryById(toUserId);
        HashMap<String, Object> exts = new HashMap<>(2);
        HashMap<String, Object> ext = new HashMap<>(2);
        ext.put("nickName", userVo.getNickName());
        exts.put("nickName", myUser.getNickName());
        ext.put("avatar", userVo.getAvatar());
        exts.put("avatar", myUser.getAvatar());
        Message message = new Message();
        message.setTitle("用户消息");
        message.setContext(MessageConstants.ADD_USER);
        message.setToUserId(toUserId);
        message.setExt(exts);
        message.setExts(ext);
        message.setFromUser(Constants.USER + fromId);
        sendMessageService.user(message);
    }


    /**
     * 发送推送消息
     *
     * @param message 消息体
     */
    public void sendPushData(Message message) {
        String tokenKey = CacheConstants.TOKEN_KEY;
        String tokenValue = RedisUtils.getCacheObject(tokenKey);
        Token token;
        if (tokenValue == null) {
            EasemobTemplate imTemplate = SpringUtils.getBean(EasemobTemplate.class);
            try {
                token = imTemplate.appToken();
                tokenValue = token.getValue();
                RedisUtils.setCacheObject(tokenKey, tokenValue, Duration.ofMinutes(Constants.APP_TOKEN_KEY));
            } catch (Exception e) {
                log.info("获取APP TOKEN失败：{}", e.getMessage());
            }
        }
        message.setToken(tokenValue);
        message.setIconUrl(appUserService.queryById(message.getFromUserId()).getAvatar());
        sendPushService.messagePush(message);
    }

    /**
     * 判断用户是VIP，还是普通用户，限制推送的次数
     *
     * @param isVip  vip信息
     * @param userId 用户ID
     * @return
     */
    private boolean checkUserSendPushNumber(String isVip, Long userId) {
        boolean sendBoolean = false;
        if (StringUtils.isNotBlank(isVip)) {
            sendBoolean = true;
        } else {
            String pushKey = CacheConstants.DAY_LIKE_PUSH + userId;
            Integer nowCount = RedisUtils.getCacheObject(pushKey);
            if (nowCount == null) {
                nowCount = 1;
            }
            if (nowCount <= CacheConstants.DAY_LIKE_PUSH_MAX) {
                sendBoolean = true;
                RedisUtils.setCacheObject(pushKey, ++nowCount, Duration.ofSeconds(DateUtils.seconds()));
            }
        }
        return sendBoolean;
    }
}
