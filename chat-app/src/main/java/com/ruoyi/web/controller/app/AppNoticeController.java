package com.ruoyi.web.controller.app;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.app.domain.bo.AppNoticeBo;
import com.ruoyi.app.domain.vo.AppDynamicCommentVo;
import com.ruoyi.app.domain.vo.AppNoticeVo;
import com.ruoyi.app.service.IAppDynamicCommentService;
import com.ruoyi.app.service.IAppDynamicService;
import com.ruoyi.app.service.IAppNoticeService;
import com.ruoyi.app.service.IAppUserService;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.helper.LoginHelper;
import com.ruoyi.common.utils.DateUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

/**
 * APP用户通知信息
 *
 * <AUTHOR>
 * @date 2025-12-27
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/appNotice")
public class AppNoticeController extends BaseController {

    private final IAppUserService appUserService;
    private final IAppNoticeService iAppNoticeService;
    private final IAppDynamicService appDynamicService;
    private final IAppDynamicCommentService appDynamicCommentService;


    /**
     * 查询APP用户通知信息列表
     */
    @GetMapping("list")
    public TableDataInfo<AppNoticeVo> list(PageQuery pageQuery) {
        AppNoticeBo bo = new AppNoticeBo();
        bo.setUserId(getUserId());
        return iAppNoticeService.queryPageList(bo, pageQuery);
    }


    /**
     * 查询APP用户通知信息列表
     */
    @GetMapping("page")
    public TableDataInfo<AppNoticeVo> page(PageQuery pageQuery) {
        ArrayList<String> types = new ArrayList<>();
        types.add("4");
        types.add("5");
        TableDataInfo<AppNoticeVo> page = iAppNoticeService.queryPageList(getUserId(), types, pageQuery);
        page.getData().forEach(e -> {
                e.setAppDynamicVo(appDynamicService.queryById(e.getRecordId()));
                e.setAppUserVo(e.getFromId()!=null ? appUserService.queryById(e.getFromId()) : null);
                e.setCreateTimeFormat(DateUtils.getDatePoorSingle(e.getCreateTime()));
        });
        return page;
    }


    /**
     * 查询APP用户通知信息列表
     */
    @GetMapping("commentPage")
    public TableDataInfo<AppNoticeVo> commentPage(PageQuery pageQuery) {
        ArrayList<String> types = new ArrayList<>();
        types.add("1");
        types.add("2");
        TableDataInfo<AppNoticeVo> page = iAppNoticeService.queryPageList(getUserId(), types, pageQuery);
        page.getData().forEach(e -> {
            long dynamoId = e.getRecordId();
            e.setAppUserVo(e.getFromId()!=null ? appUserService.queryById(e.getFromId()) : null);
            e.setCreateTimeFormat(DateUtils.getDatePoorSingle(e.getCreateTime()));
            if(e.getType().equals(Constants.FAIL)){
                AppDynamicCommentVo appDynamicCommentVo = appDynamicCommentService.queryById(e.getRecordId());
                e.setAppDynamicCommentVo(appDynamicCommentVo);
                dynamoId = appDynamicCommentVo.getDynamicId();
            }
            e.setAppDynamicVo(appDynamicService.queryById(dynamoId));
        });
        return page;
    }

    /**
     * 获取APP用户通知信息详细信息
     * 并同时标记信息为已读
     *
     * @param id 主键
     */
    @GetMapping("info")
    public R<AppNoticeBo> info(Long id) {
        AppNoticeVo noticeVo = iAppNoticeService.queryById(id);
        if (Objects.isNull(noticeVo)) {
            return R.fail("ID不存在!");
        }
        noticeVo.setStatusRead("1");
        AppNoticeBo update = BeanUtil.toBean(noticeVo, AppNoticeBo.class);
        iAppNoticeService.updateByBo(update);
        return R.ok(update);
    }

    /**
     * 标记未读信息为已读信息
     *
     * @return 提示信息
     */
    @PostMapping("readAll")
    public R<Void> readAll() {
        AppNoticeBo appNoticeBo = new AppNoticeBo();
        appNoticeBo.setStatusRead("0");
        appNoticeBo.setUserId(LoginHelper.getUserId());
        List<AppNoticeVo> noticeVo = iAppNoticeService.queryList(appNoticeBo);
        noticeVo.forEach(e -> iAppNoticeService.updateByStatus(e.getId()));
        return R.ok();
    }

    /**
     * 删除APP用户通知信息
     *
     * @param id 主键
     */
    @DeleteMapping()
    public R<Void> remove(Long id) {
        AppNoticeVo noticeVo = iAppNoticeService.queryById(id);
        if (Objects.isNull(noticeVo) || !noticeVo.getUserId().equals(LoginHelper.getUserId())) {
            return R.fail("ID不存在！");
        }
        return toAjax(iAppNoticeService.delete(id));
    }

    /**
     * 系统通知分页列表
     *
     * @param bo        实体
     * @param pageQuery 分页信息
     * @return
     */
    @GetMapping("noticePage")
    public TableDataInfo<AppNoticeVo> noticeList(AppNoticeBo bo, PageQuery pageQuery) {
        bo.setUserId(getUserId());
        TableDataInfo<AppNoticeVo> page = iAppNoticeService.queryPageList(bo, pageQuery);
        page.getData().forEach(e -> e.setTypeImage(typeImage(e.getType())));
        return page;
    }

    /**
     * 获取对应类型的图片
     *
     * @param type 类型
     * @return
     */
    public String typeImage(String type) {
        HashMap<String, String> map = new HashMap<>();
        map.put("2", "http://heike-chat.oss-cn-shanghai.aliyuncs.com/2023/09/07/2538541b45f1435ea97a48dadd7d0422.png");
        map.put("1", "http://heike-chat.oss-cn-shanghai.aliyuncs.com/2023/09/07/f7726c26b67b4a41ad21bda2934e6835.png");
        return map.get(type);
    }
}
