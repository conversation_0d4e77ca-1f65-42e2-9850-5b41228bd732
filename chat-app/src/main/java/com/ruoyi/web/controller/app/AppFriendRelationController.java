package com.ruoyi.web.controller.app;

import java.util.List;
import java.util.Arrays;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import com.ruoyi.app.domain.AppFriendRelation;
import com.ruoyi.app.domain.bo.AppUserBo;
import com.ruoyi.app.domain.vo.AppUserVipVo;
import com.ruoyi.app.domain.vo.AppUserVo;
import com.ruoyi.app.service.IAppUserService;
import com.ruoyi.app.service.IAppUserVipService;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.utils.MessageUtils;
import com.ruoyi.common.utils.StringUtils;
import com.sun.org.apache.bcel.internal.generic.NEW;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import com.ruoyi.common.core.validate.QueryGroup;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.app.domain.vo.AppFriendRelationVo;
import com.ruoyi.app.domain.bo.AppFriendRelationBo;
import com.ruoyi.app.service.IAppFriendRelationService;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * APP用户关系
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/friendRelation")
public class AppFriendRelationController extends BaseController {

    private final IAppUserService appUserService;
    private final IAppUserVipService appUserVipService;
    private final IAppFriendRelationService appFriendRelationService;

    /**
     * 足迹
     * 我看了谁列表
     * @param pageQuery 分页信息
     * @return
     */
    @GetMapping("/myFootprints")
    public TableDataInfo<AppUserVo> myFootprints(PageQuery pageQuery) {
        Long id = getUserId();
        AppFriendRelationBo relation = new AppFriendRelationBo();
        relation.setUserId(id);
        relation.setType(Constants.FAIL);
        List<AppFriendRelationVo> relationVos = appFriendRelationService.queryList(relation);
        List<Long> ids = relationVos.stream().map(AppFriendRelationVo::getToUserId).collect(Collectors.toList());
        if(ids.isEmpty()){
            return new TableDataInfo<>();
        }
        AppUserBo appUserBo = new AppUserBo();
        appUserBo.setIds(ids);
        TableDataInfo<AppUserVo> page = appUserService.queryPageList(appUserBo, pageQuery);
        page.getData().forEach(e -> {
            e.setEmail(null);
            e.setPhone(null);
            e.setPassword(null);
            List<AppFriendRelationVo> userCollect = appFriendRelationService.queryList(id, e.getId(), Constants.SUCCESS);
            e.setCollectStatus(!userCollect.isEmpty());
            e.setVipTime(appUserVipService.userVip(e.getId()));
        });
        return page;
    }


    /**
     * 足迹
     * 谁查看了我列表
     * @param pageQuery 分页信息
     * @return
     */
    @GetMapping("/viewFootprints")
    public Object viewFootprints(PageQuery pageQuery) {
        Long id = getUserId();
        String vipTime = appUserVipService.userVip(id);
        if(StringUtils.isBlank(vipTime)){
            return R.fail(MessageUtils.message("vip.open.view"));
        }
        AppFriendRelationBo relation = new AppFriendRelationBo();
        relation.setToUserId(id);
        relation.setType(Constants.FAIL);
        List<AppFriendRelationVo> relationVos = appFriendRelationService.queryList(relation);
        List<Long> ids = relationVos.stream().map(AppFriendRelationVo::getUserId).collect(Collectors.toList());
        if(ids.isEmpty()){
            return new TableDataInfo<>();
        }
        AppUserBo appUserBo = new AppUserBo();
        appUserBo.setIds(ids);
        TableDataInfo<AppUserVo> page = appUserService.queryPageList(appUserBo, pageQuery);
        page.getData().forEach(e -> {
            e.setEmail(null);
            e.setPhone(null);
            e.setPassword(null);
            List<AppFriendRelationVo> userCollect = appFriendRelationService.queryList(id, e.getId(), Constants.SUCCESS);
            e.setCollectStatus(!userCollect.isEmpty());
            e.setVipTime(appUserVipService.userVip(e.getId()));
        });
        return page;
    }


    /**
     * 收藏用户
     * @param userId 收藏的用户ID
     * @return
     */
    @PostMapping("/collect")
    public R<?> collect(@RequestParam(name="userId")Long userId) {
        Long id = getUserId();
        if(id.equals(userId)){
            return R.fail(MessageUtils.message("collect.user.id.error"));
        }
        List<AppFriendRelationVo> relationVos = appFriendRelationService.queryList(id, userId, Constants.SUCCESS);
        if(relationVos.isEmpty()){
            AppFriendRelationBo appFriendRelationBo = new AppFriendRelationBo();
            appFriendRelationBo.setUserId(id);
            appFriendRelationBo.setToUserId(userId);
            appFriendRelationBo.setType(Constants.SUCCESS);
            Boolean result = appFriendRelationService.insertByBo(appFriendRelationBo);
            return result? R.ok(MessageUtils.message("system.info.success")) : R.fail(MessageUtils.message("system.info.error"));
        }else{
            AppFriendRelationVo friendRelationVo = relationVos.get(0);
            return appFriendRelationService.deleteWithValidById(friendRelationVo.getRelationId()) ? R.ok(MessageUtils.message("system.info.success")) : R.fail(MessageUtils.message("system.info.error"));
        }
    }


    /**
     * 我收藏用户列表
     * @param pageQuery 分页信息
     * @return
     */
    @GetMapping("/myCollectPage")
    public Object myCollectPage(PageQuery pageQuery) {
        Long id = getUserId();
        AppFriendRelationBo relation = new AppFriendRelationBo();
        relation.setUserId(id);
        relation.setType(Constants.SUCCESS);
        List<AppFriendRelationVo> relationVos = appFriendRelationService.queryList(relation);
        List<Long> ids = relationVos.stream().map(AppFriendRelationVo::getToUserId).collect(Collectors.toList());
        if(ids.isEmpty()){
            return new TableDataInfo<>();
        }
        AppUserBo appUserBo = new AppUserBo();
        appUserBo.setIds(ids);
        TableDataInfo<AppUserVo> page = appUserService.queryPageList(appUserBo, pageQuery);
        page.getData().forEach(e -> {
            e.setEmail(null);
            e.setPhone(null);
            e.setPassword(null);
            e.setCollectStatus(true);
            e.setVipTime(appUserVipService.userVip(e.getId()));
        });
        return page;
    }

    /**
     * 收藏我的用户列表
     * @param pageQuery 分页信息
     * @return
     */
    @GetMapping("/collectMyPage")
    public Object collectMyPage(PageQuery pageQuery) {
        Long id = getUserId();
        AppUserVipVo userVipVo = appUserVipService.userVipId(id);
        if(Objects.isNull(userVipVo) || !StringUtils.equals(userVipVo.getType(),Constants.SUCCESS)){
            return R.fail(MessageUtils.message("vip.open.vip.view"));
        }
        AppFriendRelationBo relation = new AppFriendRelationBo();
        relation.setToUserId(id);
        relation.setType(Constants.SUCCESS);
        List<AppFriendRelationVo> relationVos = appFriendRelationService.queryList(relation);
        List<Long> ids = relationVos.stream().map(AppFriendRelationVo::getUserId).collect(Collectors.toList());
        if(ids.isEmpty()){
            return new TableDataInfo<>();
        }
        AppUserBo appUserBo = new AppUserBo();
        appUserBo.setIds(ids);
        TableDataInfo<AppUserVo> page = appUserService.queryPageList(appUserBo, pageQuery);
        page.getData().forEach(e -> {
            e.setEmail(null);
            e.setPhone(null);
            e.setPassword(null);
            List<AppFriendRelationVo> userCollect = appFriendRelationService.queryList(id, e.getId(), Constants.SUCCESS);
            e.setCollectStatus(!userCollect.isEmpty());
            e.setVipTime(appUserVipService.userVip(e.getId()));
        });
        return page;
    }
}
