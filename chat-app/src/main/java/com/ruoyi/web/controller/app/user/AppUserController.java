package com.ruoyi.web.controller.app.user;


import cn.dev33.satoken.annotation.SaIgnore;
import cn.dev33.satoken.secure.BCrypt;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.app.domain.AppFriendRelation;
import com.ruoyi.app.domain.bo.AppFriendRelationBo;
import com.ruoyi.app.domain.bo.AppUserBo;
import com.ruoyi.app.domain.vo.AppFriendRelationVo;
import com.ruoyi.app.domain.vo.AppUserVo;
import com.ruoyi.app.domain.vo.UserVo;
import com.ruoyi.app.service.IAppFriendRelationService;
import com.ruoyi.app.service.IAppFriendService;
import com.ruoyi.app.service.IAppUserService;
import com.ruoyi.common.annotation.RateLimiter;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.enums.LimitType;
import com.ruoyi.common.exception.user.CaptchaException;
import com.ruoyi.common.exception.user.CaptchaExpireException;
import com.ruoyi.common.helper.LoginHelper;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.MessageUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.VerifyCodeUtils;
import com.ruoyi.common.utils.redis.RedisUtils;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.im.core.EasemobTemplate;
import com.ruoyi.sms.config.properties.SmsProperties;
import com.ruoyi.sms.core.SmsTemplate;
import com.ruoyi.sms.entity.SmsResult;
import com.ruoyi.system.service.AppLoginService;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.web.service.message.SendPushService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 用户信息控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/user")
public class AppUserController extends BaseController {


    private final SmsProperties smsProperties;
    private final IAppUserService appUserService;
    private final AppLoginService appLoginService;
    private final SendPushService sendPushService;
    private final ISysConfigService sysConfigService;
    private final IAppFriendService appFriendService;
    private final IAppFriendRelationService appFriendRelationService;


    /**
     * 获取当前登录用户信息
     *
     * @return 用户信息
     */
    @GetMapping()
    public R<Object> userInfo(@RequestParam(name = "id", required = false) Long id) {
        id = id == null ? getUserId() : id;
        UserVo userVo = appUserService.setUserInfo(id, getUserId());
        footprint(id);
        return R.ok(MessageUtils.message("system.info.success"), userVo);
    }

    /**
     * 获取环信用户名获取基本信息
     *
     * @return 用户信息
     */
    @GetMapping("chatInfo")
    public R<Object> chatInfo(@RequestParam(name = "chatName") String chatName) {
        Long aLong = userId(chatName);
        UserVo userVo = appUserService.queryByIdUserVo(aLong);
        userVo.setIsRelation(appFriendService.userRelation(getUserId(), aLong));
        footprint(aLong);
        return R.ok(MessageUtils.message("system.info.success"), userVo);
    }



    /**
     * 记录足迹信息
     */
    private void footprint(Long toUserId) {
        Long id = getUserId();
        if (!id.equals(toUserId)) {
            LambdaQueryWrapper<AppFriendRelation> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(AppFriendRelation::getUserId, id);
            wrapper.eq(AppFriendRelation::getToUserId, toUserId);
            wrapper.eq(AppFriendRelation::getType, Constants.FAIL);
            AppFriendRelationVo relationVo = appFriendRelationService.queryByOne(wrapper);
            if (Objects.isNull(relationVo)) {
                AppFriendRelationBo relationBo = new AppFriendRelationBo();
                relationBo.setUserId(id);
                relationBo.setToUserId(toUserId);
                relationBo.setType(Constants.FAIL);
                relationBo.setStatus(Constants.SUCCESS);
                appFriendRelationService.insertByBo(relationBo);
            }else{
                AppFriendRelationBo relationBo = new AppFriendRelationBo();
                relationBo.setCreateTime(DateUtils.getNowDate());
                relationBo.setRelationId(relationVo.getRelationId());
                appFriendRelationService.updateByBo(relationBo);
            }
        }
    }


    /**
     * 校验验证码
     *
     * @param phonenumber 手机号
     * @param code        手机验证码
     * @return
     */
    @GetMapping("checkMsg")
    public R<Object> checkMsg(
        @RequestParam(name = "code") String code,
        @RequestParam(name = "phonenumber") String phonenumber
    ) {
        String key = CacheConstants.CAPTCHA_CODE_KEY + phonenumber;
        String msCode = RedisUtils.getCacheObject(key);
        if (smsProperties.getEnabled() && StringUtils.isBlank(msCode)) {
            throw new CaptchaExpireException();
        }
        if (smsProperties.getEnabled() && !msCode.equals(code)) {
            throw new CaptchaException();
        }
        return R.ok(MessageUtils.message("system.info.success"));
    }

    /**
     * 修改用户信息
     *
     * @param bo 用户信息对象
     * @return
     */
    @PostMapping("/updateInfo")
    public R<Object> updateInfo(AppUserBo bo) {
        bo.setId(getUserId());
        if (appUserService.updateInfo(bo)) {
            if (StringUtils.isNotBlank(bo.getNickName()) || StringUtils.isNotBlank(bo.getAvatar())) {
                HashMap<String, String> map = new HashMap<>(2);
                EasemobTemplate imTemplate = SpringUtils.getBean(EasemobTemplate.class);
                if (StringUtils.isNotBlank(bo.getNickName())) {
                    map.put("nickname", bo.getNickName());
                }
                if (StringUtils.isNotBlank(bo.getAvatar())) {
                    map.put("avatarurl", bo.getAvatar());
                }
                if (!map.isEmpty()) {
                    imTemplate.setMetadataToUser(userName(), map);
                }
            }
            return R.ok(MessageUtils.message("system.info.success"));
        }
        return R.fail(MessageUtils.message("system.info.error"));
    }

    /**
     * 修改密码
     *
     * @param oldPassword 旧密码
     * @param password    修改的新密码
     * @return
     */
    @PostMapping("/updatePassword")
    public R<String> updatePassword(String oldPassword, String password) {
        Long id = LoginHelper.getUserId();
        AppUserVo appUserVo = appUserService.selectVoById(id);
        if (!BCrypt.checkpw(oldPassword, appUserVo.getPassword())) {
            return R.fail(MessageUtils.message("user.oldPassword.error"));
        }
        return appUserService.updatePassword(id, password) ? R.ok(MessageUtils.message("system.info.success")) : R.fail(MessageUtils.message("system.info.error"));
    }

    /**
     * 注销账号
     *
     * @return
     */
    @PostMapping("logOff")
    public R<Void> logOff() {
        AppUserVo userVo = appUserService.queryById(getUserId());
        if (userVo.getStatus().equals(Constants.TWO)) {
            return R.fail(MessageUtils.message("userName.is.logOff"));
        }
        String key = CacheConstants.LOGIN_OUT + getUserId();
        //注销的处理的时间,缓存15天后的时间戳
        Long value = System.currentTimeMillis() + 14 * 86400000;
        HashMap<String, Object> map = new HashMap<>(2);
        map.put("endTime", value);
        map.put("id", getUserId());
        RedisUtils.setCacheObject(key, map, Duration.ofDays(CacheConstants.LOGIN_OUT_DAY));
        appLoginService.logout();
        return R.ok(MessageUtils.message("system.info.success"));
    }


    /**
     * 显示隐藏功能
     *
     * @return
     */
    @SaIgnore
    @GetMapping("showAndHidden")
    public R<HashMap<String, Object>> showAndHidden() {
        HashMap<String, Object> map = new HashMap<>(2);
        map.put("status", sysConfigService.selectConfigByKey("app.chat.show"));
        return R.ok(MessageUtils.message("system.info.success"), map);
    }
}
