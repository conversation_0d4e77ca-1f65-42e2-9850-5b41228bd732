package com.ruoyi.web.controller.app.user;


import cn.dev33.satoken.annotation.SaIgnore;
import cn.dev33.satoken.secure.BCrypt;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.app.domain.AppFriendRelation;
import com.ruoyi.app.domain.bo.AppDynamicBo;
import com.ruoyi.app.domain.bo.AppFriendRelationBo;
import com.ruoyi.app.domain.bo.AppUserBo;
import com.ruoyi.app.domain.bo.AppUserRemarkBo;
import com.ruoyi.app.domain.vo.*;
import com.ruoyi.app.service.IAppDynamicService;
import com.ruoyi.app.service.IAppFriendRelationService;
import com.ruoyi.app.service.IAppUserRemarkService;
import com.ruoyi.app.service.IAppUserService;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.exception.user.CaptchaException;
import com.ruoyi.common.exception.user.CaptchaExpireException;
import com.ruoyi.common.helper.LoginHelper;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.MessageUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.redis.RedisUtils;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.im.core.EasemobTemplate;
import com.ruoyi.sms.config.properties.SmsProperties;
import com.ruoyi.system.service.AppLoginService;
import com.ruoyi.system.service.ISysConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.regex.Pattern;

/**
 * 用户信息控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/user")
public class AppUserController extends BaseController {


    private final SmsProperties smsProperties;
    private final IAppUserService appUserService;
    private final AppLoginService appLoginService;
    private final ISysConfigService sysConfigService;
    private final IAppDynamicService appDynamicService;
    private final IAppUserRemarkService appUserRemarkService;
    private final IAppFriendRelationService appFriendRelationService;


    /**
     * 获取当前登录用户信息
     *
     * @return 用户信息
     */
    @GetMapping()
    public R<Object> userInfo(
        @RequestParam(name = "id", required = false) Long id,
        @RequestParam(name = "status", defaultValue = "0") String status
    ) {
        id = id == null ? getUserId() : id;
        UserVo userVo = appUserService.setUserInfo(id, getUserId());
        if (!StringUtils.equals(status, Constants.SUCCESS)) {
            AppDynamicBo dynamicBo = new AppDynamicBo();
            dynamicBo.setUserId(id);
            dynamicBo.setStatus(Constants.SUCCESS);
            List<AppDynamicVo> dynamicVos = appDynamicService.queryList(dynamicBo);
            userVo.setDynamicVos(appDynamicService.simplifyDate(dynamicVos, getUserId()));
        }
        footprint(id);
        return R.ok(MessageUtils.message("system.info.success"), userVo);
    }

    /**
     * 获取环信用户名获取基本信息
     *
     * @return 用户信息
     */
    @GetMapping("chatInfo")
    public R<Object> chatInfo(
        @RequestParam(name = "chatName") String chatName,
        @RequestParam(name = "status", defaultValue = "0") String status
    ) {
        Long aLong = userId(chatName);
        UserVo userVo = appUserService.queryByIdUserVo(aLong);
        if (!StringUtils.equals(status, Constants.SUCCESS)) {
            AppDynamicBo dynamicBo = new AppDynamicBo();
            dynamicBo.setUserId(aLong);
            dynamicBo.setStatus(Constants.SUCCESS);
            List<AppDynamicVo> dynamicVos = appDynamicService.queryList(dynamicBo);
            userVo.setDynamicVos(appDynamicService.simplifyDate(dynamicVos, getUserId()));
        }
        footprint(aLong);
        return R.ok(MessageUtils.message("system.info.success"), userVo);
    }


    /**
     * 记录足迹信息
     */
    private void footprint(Long toUserId) {
        Long id = getUserId();
        if (!id.equals(toUserId)) {
            LambdaQueryWrapper<AppFriendRelation> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(AppFriendRelation::getUserId, id);
            wrapper.eq(AppFriendRelation::getToUserId, toUserId);
            wrapper.eq(AppFriendRelation::getType, Constants.FAIL);
            AppFriendRelationVo relationVo = appFriendRelationService.queryByOne(wrapper);
            if (Objects.isNull(relationVo)) {
                AppFriendRelationBo relationBo = new AppFriendRelationBo();
                relationBo.setUserId(id);
                relationBo.setToUserId(toUserId);
                relationBo.setType(Constants.FAIL);
                relationBo.setStatus(Constants.SUCCESS);
                appFriendRelationService.insertByBo(relationBo);
            } else {
                AppFriendRelationBo relationBo = new AppFriendRelationBo();
                relationBo.setCreateTime(DateUtils.getNowDate());
                relationBo.setRelationId(relationVo.getRelationId());
                appFriendRelationService.updateByBo(relationBo);
            }
        }
    }


    /**
     * 校验验证码
     *
     * @param phonenumber 手机号
     * @param code        手机验证码
     * @return
     */
    @GetMapping("checkMsg")
    public R<Object> checkMsg(
        @RequestParam(name = "code") String code,
        @RequestParam(name = "phonenumber") String phonenumber
    ) {
        String key = CacheConstants.CAPTCHA_CODE_KEY + phonenumber;
        String msCode = RedisUtils.getCacheObject(key);
        if (smsProperties.getEnabled() && StringUtils.isBlank(msCode)) {
            throw new CaptchaExpireException();
        }
        if (smsProperties.getEnabled() && !msCode.equals(code)) {
            throw new CaptchaException();
        }
        return R.ok(MessageUtils.message("system.info.success"));
    }

    /**
     * 修改用户信息
     *
     * @param bo 用户信息对象
     * @return
     */
    @PostMapping("/updateInfo")
    public R<Object> updateInfo(AppUserBo bo) {
        Long id = getUserId();
        bo.setId(id);
        bo.setSex(null);
        if (StringUtils.isNotBlank(bo.getEmail())) {
            if (!Pattern.matches(Constants.EMAIL_REGEX, bo.getEmail())) {
                return R.fail(MessageUtils.message("user.email.not.valid"));
            }
            List<AppUserVo> appUserVos = appUserService.queryByEmail(bo.getEmail(), id);
            if (ObjectUtils.isNotEmpty(appUserVos)) {
                return R.fail(MessageUtils.message("user.email.not.use"));
            }
        }
        if (StringUtils.isNotBlank(bo.getPhone())) {
            if (bo.getPhone().length() <= 3) {
                return R.fail(MessageUtils.message("user.mobile.phone.number.not.valid"));
            }
            List<AppUserVo> appUserVos = appUserService.queryByPhone(bo.getPhone(), id);
            if (ObjectUtils.isNotEmpty(appUserVos)) {
                return R.fail(MessageUtils.message("user.phone.not.use"));
            }
        }
        if (appUserService.updateInfo(bo)) {
            if (StringUtils.isNotBlank(bo.getNickName()) || StringUtils.isNotBlank(bo.getAvatar())) {
                HashMap<String, String> map = new HashMap<>(2);
                EasemobTemplate imTemplate = SpringUtils.getBean(EasemobTemplate.class);
                if (StringUtils.isNotBlank(bo.getNickName())) {
                    map.put("nickname", bo.getNickName());
                }
                if (StringUtils.isNotBlank(bo.getAvatar())) {
                    map.put("avatarurl", bo.getAvatar());
                }
                if (!map.isEmpty()) {
                    imTemplate.setMetadataToUser(userName(), map);
                }
            }
            return R.ok(MessageUtils.message("system.info.success"));
        }
        return R.fail(MessageUtils.message("system.info.error"));
    }

    /**
     * 用户添加备注
     *
     * @param id     操作用户ID
     * @param remark 备注内容
     * @return
     */
    @PostMapping("/addRemark")
    public R<String> addRemark(
        @RequestParam(name = "id") Long id,
        @RequestParam(name = "remark") String remark
    ) {
        Long uid = getUserId();
        if (id.equals(uid)) {
            return R.fail(MessageUtils.message("user.error.remark.one"));
        }
        if (StringUtils.isBlank(remark)) {
            return R.fail(MessageUtils.message("user.error.remark.blank"));
        }
        AppUserRemarkVo remarkVo = appUserRemarkService.queryUser(uid, id);
        if (Objects.nonNull(remarkVo)) {
            remarkVo.setRemark(remark);
            appUserRemarkService.updateByVo(remarkVo);
        } else {
            AppUserRemarkBo remarkBo = new AppUserRemarkBo();
            remarkBo.setUserId(uid);
            remarkBo.setFriendId(id);
            remarkBo.setRemark(remark);
            appUserRemarkService.insertByBo(remarkBo);
        }
        return R.ok(MessageUtils.message("system.info.success"));
    }


    /**
     * 修改密码
     *
     * @param oldPassword 旧密码
     * @param password    修改的新密码
     * @return
     */
    @PostMapping("/updatePassword")
    public R<String> updatePassword(String oldPassword, String password) {
        Long id = LoginHelper.getUserId();
        AppUserVo appUserVo = appUserService.selectVoById(id);
        if (!BCrypt.checkpw(oldPassword, appUserVo.getPassword())) {
            return R.fail(MessageUtils.message("user.oldPassword.error"));
        }
        return appUserService.updatePassword(id, password) ? R.ok(MessageUtils.message("system.info.success")) : R.fail(MessageUtils.message("system.info.error"));
    }

    /**
     * 注销账号
     *
     * @return
     */
    @PostMapping("logOff")
    public R<Void> logOff() {
        AppUserVo userVo = appUserService.queryById(getUserId());
        if (userVo.getStatus().equals(Constants.TWO)) {
            return R.fail(MessageUtils.message("userName.is.logOff"));
        }
        String key = CacheConstants.LOGIN_OUT + getUserId();
        //注销的处理的时间,缓存15天后的时间戳
        Long value = System.currentTimeMillis() + 14 * 86400000;
        HashMap<String, Object> map = new HashMap<>(2);
        map.put("endTime", value);
        map.put("id", getUserId());
        RedisUtils.setCacheObject(key, map, Duration.ofDays(CacheConstants.LOGIN_OUT_DAY));
        appLoginService.logout();
        return R.ok(MessageUtils.message("system.info.success"));
    }


    /**
     * 显示隐藏功能
     *
     * @return
     */
    @SaIgnore
    @GetMapping("showAndHidden")
    public R<HashMap<String, Object>> showAndHidden() {
        HashMap<String, Object> map = new HashMap<>(2);
        map.put("status", sysConfigService.selectConfigByKey("app.chat.show"));
        return R.ok(MessageUtils.message("system.info.success"), map);
    }
}
