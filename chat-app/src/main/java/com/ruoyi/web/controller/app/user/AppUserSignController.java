package com.ruoyi.web.controller.app.user;

import com.ruoyi.app.domain.AppUserSign;
import com.ruoyi.app.service.IAppUserSignService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.enums.BusinessType;
import com.sun.org.apache.bcel.internal.generic.NEW;
import com.yomahub.tlog.utils.LocalhostUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.PostConstruct;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Objects;

/**
 * App用户签到记录
 *
 * <AUTHOR>
 * @date 2023-03-28
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/userSign")
public class AppUserSignController extends BaseController {

    private final IAppUserSignService iAppUserSignService;
    //签到最大天数
    private final static int MAX = 7;
    private final static HashMap<Integer, Integer> LIST = new HashMap<>(MAX);

    /**
     * 初始化签到的数据
     */
    @PostConstruct
    public void init() {
        for (int i = 1; i <= MAX; i++) {
            LIST.put(i, i);
        }
    }

    /**
     * 获取App用户签到记录详细信息
     */
    @GetMapping("info")
    public R<Object> getInfo() {
        HashMap<String, Object> sign = new HashMap<>();
        AppUserSign userSign = iAppUserSignService.queryUser(getUserId());
        long time = getNowTime();
        //今日是否已签到，1=已签到，0=未签到
        int isSign = Objects.nonNull(userSign) && userSign.getSignTime() >= time ? 1 : 0;
        sign.put("sign", isSign);
        //判断昨天是否签到
        long yestTime = time - 24 * 60 * 60 * 1000;
        Integer day = 0;
        if (isSign == 1) {
            day = userSign.getDay();
        } else {
            if (Objects.nonNull(userSign) && userSign.getSignTime() >= yestTime) {
                day = userSign.getDay();
            }
        }
        sign.put("signDay", day);
        sign.put("image", "http://heike-chat.oss-cn-shanghai.aliyuncs.com/2023/03/29/cb31d9a85b224c13ba5a951a2577e31f.png");
        sign.put("imageVip", "https://heike-chat.oss-cn-shanghai.aliyuncs.com/2023/05/12/e7f5eb621d8249bc8eb3fc56704b29e9.png");
        HashMap<String, Object> hashMap = new HashMap<>(2);
        ArrayList<Integer> list = new ArrayList<>(LIST.values());
        hashMap.put("signList", list);
        hashMap.put("signData", sign);
        return R.ok(hashMap);
    }

    /**
     * 新增App用户签到记录
     */
    @Log(title = "App用户签到记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Object> sign() {
        AppUserSign userSign = iAppUserSignService.queryUser(getUserId());
        long time = getNowTime();
        if (Objects.nonNull(userSign) && userSign.getSignTime() >= time) {
            return R.fail("今日已签到");
        }
        return R.ok(iAppUserSignService.userSign(getUserId(), time, userSign, LIST));
    }

    /**
     * 获取当日0点的时间戳(毫秒)
     *
     * @return
     */
    private long getNowTime() {
        LocalDateTime localDateTime = LocalDate.now().atTime(0, 0, 0);
        return localDateTime.toInstant(ZoneOffset.ofHours(8)).toEpochMilli();
    }
}
