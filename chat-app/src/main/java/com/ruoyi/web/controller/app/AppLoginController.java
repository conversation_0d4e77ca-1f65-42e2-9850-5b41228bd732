package com.ruoyi.web.controller.app;

import cn.dev33.satoken.annotation.SaIgnore;
import com.alibaba.fastjson.JSONObject;
import com.easemob.im.server.model.EMUser;
import com.ruoyi.app.domain.bo.AppFeedbackBo;
import com.ruoyi.app.domain.bo.AppUserBo;
import com.ruoyi.app.domain.vo.*;
import com.ruoyi.app.service.IAppAssetService;
import com.ruoyi.app.service.IAppFeedbackService;
import com.ruoyi.app.service.IAppThirdUserService;
import com.ruoyi.app.service.IAppUserService;
import com.ruoyi.common.annotation.RateLimiter;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.model.LoginBody;
import com.ruoyi.common.core.domain.model.SmsLoginBody;
import com.ruoyi.common.core.domain.model.UserRegisterBody;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.enums.LimitType;
import com.ruoyi.common.exception.user.CaptchaException;
import com.ruoyi.common.exception.user.CaptchaExpireException;
import com.ruoyi.common.helper.LoginHelper;
import com.ruoyi.common.utils.MessageUtils;
import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.VerifyCodeUtils;
import com.ruoyi.common.utils.redis.RedisUtils;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.im.core.EasemobTemplate;
import com.ruoyi.sms.config.properties.SmsProperties;
import com.ruoyi.system.domain.SysNotice;
import com.ruoyi.system.service.AppLoginService;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysNoticeService;
import com.ruoyi.web.service.AsyncCaller;
import com.ruoyi.web.service.message.SendMessageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Pattern;

/**
 * APP登录验证
 *
 * <AUTHOR> Li
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/app")
public class AppLoginController {

    @Autowired
    private RestTemplate restTemplate;
    private final IAppUserService appUserService;
    private final AppLoginService appLoginService;
    private final IAppAssetService appAssetService;
    private final ISysNoticeService sysNoticeService;
    private final ISysConfigService iSysConfigService;
    private final IAppFeedbackService appFeedbackService;
    private final IAppThirdUserService appThirdUserService;
    private final SendMessageService sendMessageService;
    private final SmsProperties smsProperties;
    private final AsyncCaller asyncCaller;


    /**
     * 账号密码登录
     *
     * @param loginBody 用户信息
     * @return
     */
    @SaIgnore
    @PostMapping("/login")
    public R<Map<String, Object>> login(@Valid LoginBody loginBody) {
        Map<String, Object> ajax = new HashMap<>();
        String token = appLoginService.userNameLogin(loginBody.getUsername(), loginBody.getPassword());
        UserVo userVo = appUserService.queryByIdUserVo(LoginHelper.getUserId());
        ajax.put(Constants.TOKEN, token);
        ajax.put(Constants.USER_INFO, userVo);
        ajax.put(Constants.EASEMOB_TOKEN, easemobToken(userVo));
        return R.ok(MessageUtils.message("system.info.success"), ajax);
    }

    /**
     * 手机验证码登录注册
     *
     * @param smsLoginBody 短信登录信息
     * @return 结果
     */
    @SaIgnore
    @PostMapping("/mobileLogin")
    public R<Map<String, Object>> mobileLogin(@Validated SmsLoginBody smsLoginBody) {
        Map<String, Object> ajax = new HashMap<>();
        String token = appLoginService.login(smsLoginBody.getPhonenumber(), smsLoginBody.getSmsCode());
        UserVo userVo = appUserService.queryByIdUserVo(LoginHelper.getUserId());
        ajax.put(Constants.TOKEN, token);
        ajax.put(Constants.USER_INFO, userVo);
        ajax.put(Constants.EASEMOB_TOKEN, easemobToken(userVo));
        return R.ok(MessageUtils.message("system.info.success"), ajax);
    }

    /**
     * 账号密码注册
     *
     * @param loginBody 登录信息
     * @return 结果
     */
    @SaIgnore
    @PostMapping("/register")
    public R<Map<String, Object>> register(@Valid UserRegisterBody loginBody) {
        AppUserVo user = appUserService.queryByUserName(loginBody.getUsername());
        if (Objects.nonNull(user)) {
            return R.fail(MessageUtils.message("user.error.register"));
        }
        AppUserVo emailUser = appUserService.queryByEmailOrPhone(loginBody.getOtherInfo());
        if (Objects.nonNull(emailUser)) {
            return R.fail(MessageUtils.message("user.error.email.register"));
        }
        Map<String, Object> ajax = new HashMap<>(10);
        String token = appLoginService.login(loginBody);
        UserVo userVo = appUserService.queryByIdUserVo(LoginHelper.getUserId());
        ajax.put(Constants.TOKEN, token);
        ajax.put(Constants.USER_INFO, userVo);
        ajax.put(Constants.EASEMOB_TOKEN, easemobToken(userVo));
        return R.ok(MessageUtils.message("system.info.success"), ajax);
    }

    /**
     * 发送短信验证码
     *
     * @param phonenumber 手机号码
     */
    @SaIgnore
    @RateLimiter(limitType = LimitType.IP)
    @PostMapping("/sendMessage")
    public R<Object> sendMessage(
        @RequestParam(name = "phonenumber") String phonenumber,
        @RequestParam(name = "uuid", required = false) String uuid,
        @RequestParam(name = "uuidCode", required = false) String uuidCode,
        @RequestParam(name = "type", defaultValue = "0") Integer type,
        @RequestParam(name = "otherType", defaultValue = "wechat") String otherType
    ) {
        String regex = "^1\\d{10}$";
        if (!Pattern.matches(regex, phonenumber)) {
            return R.fail("手机号码格式错误!");
        }
        HttpServletRequest request = ServletUtils.getRequest();
        appLoginService.validateCaptcha(phonenumber, uuidCode, uuid, request);
        if (Constants.FORGET.equals(type)) {
            AppUserVo userVo = appUserService.queryByPhone(phonenumber);
            if (Objects.isNull(userVo)) {
                return R.fail("账号未注册!");
            }
        }
        if (Constants.TWO.equals(type)) {
            AppUserVo user = appUserService.queryByPhone(phonenumber);
            if (Objects.nonNull(user)) {
                AppThirdUserVo userVo = appThirdUserService.queryByTypeAndUserId(otherType, user.getId());
                if (Objects.nonNull(userVo)) {
                    return R.fail("账号已绑定!");
                }
            } else {
                return R.fail("账号未注册!");
            }
        }
        if (!SpringUtils.containsBean("aliyunSmsTemplate")) {
            return R.fail("短信依赖未引入！");
        }
        String key = CacheConstants.CAPTCHA_CODE_KEY + phonenumber;
        String sendCode = RedisUtils.getCacheObject(key);
        if (StringUtils.isNotBlank(sendCode)) {
            return R.fail("验证码已发送，请稍后在再试");
        }
        String code = VerifyCodeUtils.generateVerifyNumberCode(4);
        HashMap<String, Object> data = new HashMap<>();
        data.put("appid", smsProperties.getAccessKeyId());
        data.put("key", smsProperties.getAccessKeySecret());
        data.put("code", code);
        data.put("mobile", phonenumber);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<HashMap<String, Object>> formEntity = new HttpEntity<>(data, headers);
        String body = restTemplate.postForEntity(smsProperties.getEndpoint(), formEntity, String.class).getBody();
        System.out.println("发送短信返回信息:" + body);
        JSONObject jsonObject = JSONObject.parseObject(body);
        boolean result = jsonObject.get("code").toString().equals(Constants.FAIL);
        if (result) {
            //缓存短信验证码
            RedisUtils.setCacheObject(key, code, Duration.ofMinutes(Constants.APP_CAPTCHA_EXPIRATION));
            log.info("手机号码[{}]的验证码是：{}", phonenumber, code);
            return R.ok("发送短信成功");
        } else {
            return R.fail("发送短信失败!");
        }
    }

    /**
     * 忘记密码
     *
     * @param email    邮箱地址
     * @param password 新密码
     * @param code     验证码
     * @return
     */
    @SaIgnore
    @RepeatSubmit()
    @PostMapping("/password")
    public R<String> password(@RequestParam(name = "email") String email, @RequestParam(name = "password") String password, @RequestParam(name = "code") String code) {
        if (!Pattern.matches(Constants.EMAIL_REGEX, email)) {
            return R.fail(MessageUtils.message("user.email.not.valid"));
        }
        if (StringUtils.isBlank(password)) {
            return R.fail(MessageUtils.message("user.password.blank"));
        }
        String key = CacheConstants.CAPTCHA_CODE_KEY + email;
        String msCode = RedisUtils.getCacheObject(key);
        if (smsProperties.getEnabled() && StringUtils.isBlank(msCode)) {
            throw new CaptchaExpireException();
        }
        if (smsProperties.getEnabled() && !msCode.equals(code)) {
            throw new CaptchaException();
        }
        AppUserVo user = appUserService.queryByEmail(email);
        if (Objects.isNull(user)) {
            return R.fail(MessageUtils.message("user.error.email.error"));
        }
        RedisUtils.deleteObject(key);
        return appUserService.updatePassword(user.getId(), password) ? R.ok(MessageUtils.message("system.info.success")) : R.fail(MessageUtils.message("system.info.error"));
    }


    /**
     * APP退出登录
     *
     * @return
     */
    @PostMapping("/logOut")
    public R<Object> logOut() {
        AppUserVo appUserVo = appUserService.queryById(LoginHelper.getUserId());
        if (Objects.nonNull(appUserVo)) {
            appUserService.updateOnline(appUserVo.getId());
        }
        appLoginService.logout();
        return R.ok(MessageUtils.message("system.info.success"));
    }

    /**
     * 获取隐私协议和用户协议
     *
     * @param type 协议类型
     * @return
     */
    @SaIgnore
    @GetMapping("/notice")
    public R<SysNotice> notice(Integer type) {
        String key = CacheConstants.APP_NOTICE + type;
        SysNotice notice;
        notice = RedisUtils.getCacheObject(key);
        if (Objects.isNull(notice)) {
            notice = sysNoticeService.queryByType(String.valueOf(type));
            RedisUtils.setCacheObject(key, notice);
        }
        return R.ok(MessageUtils.message("system.info.success"), notice);
    }


    /**
     * 获取帮助中心列表
     *
     * @param pageQuery 分页信息
     * @return
     */
    @GetMapping("/noticePage")
    public TableDataInfo<SysNotice> noticePage(PageQuery pageQuery) {
        SysNotice sysNotice = new SysNotice();
        sysNotice.setNoticeType(Constants.TWO);
        return sysNoticeService.selectPageNoticeList(sysNotice, pageQuery);
    }


    /**
     * 获取APP的配置参数
     *
     * @return
     */
    @SaIgnore
    @GetMapping("/download")
    public R<HashMap<String, String>> download() {
        HashMap<String, String> hashMap = new HashMap<>();
        hashMap.put("email", iSysConfigService.selectConfigByKey("app.info.email"));
        hashMap.put("downloadUrl", iSysConfigService.selectConfigByKey("app.share.h5"));
        return R.ok(MessageUtils.message("system.info.success"), hashMap);
    }


    /**
     * 用户忘记密码反馈
     */
    @SaIgnore
    @RepeatSubmit()
    @PostMapping("/forgotPassword")
    public R<Void> forgotPassword(@Validated(AddGroup.class) AppFeedbackBo bo) {
        AppFeedbackBo feedbackBo = new AppFeedbackBo();
        feedbackBo.setContext(bo.getContext());
        List<AppFeedbackVo> appFeedbackVos = appFeedbackService.queryList(feedbackBo);
        if (!appFeedbackVos.isEmpty()) {
            return R.fail(MessageUtils.message("user.error.email.submit"));
        }
        return appFeedbackService.insertByBo(bo) ? R.ok(MessageUtils.message("system.info.success")) : R.fail(MessageUtils.message("system.info.error"));
    }

    /**
     * 同步环信信息
     *
     * @param user 用户信息
     * @return
     */
    private String easemobToken(UserVo user) {
        String name = Constants.USER + user.getId().toString();
        String key = CacheConstants.APP_USER_EASEMOB + name;
        String userToken = RedisUtils.getCacheObject(key);
        if (StringUtils.isBlank(userToken)) {
            EasemobTemplate imTemplate = SpringUtils.getBean(EasemobTemplate.class);
            if (user.getUuid() == null) {
                EMUser emUser = null;
                try {
                    emUser = imTemplate.create(name, Constants.PASSWORD);
                } catch (Exception e) {
                    log.info("创建环信账号失败：{}", e.getMessage());
                }
                HashMap<String, String> map = new HashMap<>(2);
                map.put("nickname", user.getNickName());
                map.put("avatarurl", Constants.AVATAR);
                try {
                    //同步头像和昵称
                    imTemplate.setMetadataToUser(name, map);
                    //设置用户推送昵称
                    imTemplate.updateUserNickname(name, user.getNickName());
                } catch (Exception e) {
                    log.info("同步修改环信用户信息和推送昵称失败：{}", e.getMessage());
                }
                AppAssetVo appAssetVo = appAssetService.queryByUid(user.getId());
                if (Objects.isNull(appAssetVo)) {
                    appAssetService.insert(user.getId());
                    //第一次登录发送系统通知
//                    Message sendMessage = new Message();
//                    sendMessage.setTitle(NoticeConstants.NEW_USER_MESSAGE.CONTEXT);
//                    sendMessage.setUrl(NoticeConstants.NEW_USER_MESSAGE.URL);
//                    sendMessage.setImage(NoticeConstants.NEW_USER_MESSAGE.IMAGE);
//                    sendMessage.setContext(NoticeConstants.NEW_USER_MESSAGE.CONTEXT);
//                    sendMessage.setToUserId(user.getId());
//                    sendMessage.setMessageType(NoticeStatus.SYSTEM.getCode());
//                    sendMessageService.sendNoticeOne(sendMessage);
                }
                AppUserBo userBo = new AppUserBo();
                if (Objects.nonNull(emUser) && StringUtils.isNotBlank(emUser.getUuid())) {
                    userBo.setUuid(emUser.getUuid());
                }
                userBo.setId(user.getId());
                userBo.setAvatar(Constants.AVATAR);
                appUserService.updateByBo(userBo);
            }
            EMUser cathy = new EMUser(name, user.getUuid(), true, null);
            try {
                userToken = imTemplate.getUserToken(cathy, Constants.PASSWORD, 3600);
                RedisUtils.setCacheObject(key, userToken, Duration.ofMinutes(Constants.APP_USER_EASEMOB_TIME));
            } catch (Exception e) {
                log.info("{}获取环信的TOKEN失败：{}", user.getNickName(), e.getMessage());
            }
        }
        asyncCaller.updateUserLine();
        return userToken;
    }
}
