package com.ruoyi.web.job;


import com.ruoyi.app.domain.bo.AppHongbaoBo;
import com.ruoyi.app.domain.bo.AppUserBo;
import com.ruoyi.app.domain.vo.AppHongbaoVo;
import com.ruoyi.app.domain.vo.AppUserVo;
import com.ruoyi.app.service.IAppAssetService;
import com.ruoyi.app.service.IAppHongbaoService;
import com.ruoyi.app.service.IAppUserService;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.utils.redis.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Slf4j
@Component
public class LinePushJob {

    @Autowired
    private IAppUserService appUserService;
    @Autowired
    private IAppAssetService appAssetService;
    @Autowired
    private IAppHongbaoService appHongbaoService;


    /**
     * 每日凌晨1点处理要注销的账号
     */
    @Scheduled(cron = "0 0 1 * * ?")
    @Transactional(rollbackFor = Exception.class)
    public void checkUserJob() {
        Collection<String> keys = RedisUtils.keys(CacheConstants.LOGIN_OUT + "*");
        keys.forEach(e -> {
            Map<String, Long> data = RedisUtils.getCacheObject(e);
            if (data.get("endTime").compareTo(System.currentTimeMillis()) <= 0) {
                appUserService.updateStatus(data.get("id"), Constants.TWO);
                RedisUtils.deleteObject(e);
            }
        });
    }

    /**
     * 红包24小时未领取退回
     */
    @Scheduled(cron = "0 0 0 * * ?")
    public void redTimeOut() {
        AppHongbaoBo hongbaoBo = new AppHongbaoBo();
        hongbaoBo.setStatus(Constants.SUCCESS);
        List<AppHongbaoVo> appHongbaoVos = appHongbaoService.queryList(hongbaoBo);
    }



    /**
     * 每日凌晨执行，清除用户使用的免费字符数量
     */
    @Scheduled(cron = "0 0 0 * * ?")
    public void clear() {
        appUserService.resetDailyFreeQuota();
    }

    /**
     * 每天凌晨记录当天的用户总数
     */
    @Scheduled(cron = "0 50 23 * * ?")
    public void updateUser() {
        List<AppUserVo> userVos = appUserService.queryList(new AppUserBo());
        RedisUtils.setCacheObject(CacheConstants.USER_SUM, userVos.size());
    }


    /**
     * 获取指定天数之前的条件
     *
     * @param time   创建记录时间
     * @param number 加减几天
     * @return
     */
    public boolean selectDate(Date time, Integer number) {
        Calendar instance = Calendar.getInstance();
        instance.setTime(time);
        instance.add(Calendar.DATE, number);
        return new Date().compareTo(instance.getTime()) >= 0;
    }

    /**
     * 登录过或者7天没有登录的用户
     *
     * @param time   创建用户时间
     * @param number 加减几天
     * @return
     */
    public boolean queryDate(Date time, Integer number) {
        Calendar instance = Calendar.getInstance();
        instance.setTime(new Date());
        instance.add(Calendar.DATE, number);
        return time.compareTo(instance.getTime()) <= 0;
    }

}
