package com.ruoyi.web.job;


import com.ruoyi.app.domain.bo.AppHongbaoBo;
import com.ruoyi.app.domain.bo.AppUserBo;
import com.ruoyi.app.domain.vo.AppHongbaoVo;
import com.ruoyi.app.domain.vo.AppUserVo;
import com.ruoyi.app.service.IAppAssetService;
import com.ruoyi.app.service.IAppHongbaoService;
import com.ruoyi.app.service.IAppUserService;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.utils.redis.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

@Slf4j
@Component
public class LinePushJob {

    @Autowired
    private IAppUserService appUserService;
    @Autowired
    private IAppAssetService appAssetService;
    @Autowired
    private IAppHongbaoService appHongbaoService;


    /**
     * 每日凌晨1点处理要注销的账号
     */
    @Scheduled(cron = "0 0 1 * * ?")
    @Transactional(rollbackFor = Exception.class)
    public void checkUserJob() {
        Collection<String> keys = RedisUtils.keys(CacheConstants.LOGIN_OUT + "*");
        keys.forEach(e -> {
            Map<String, Long> data = RedisUtils.getCacheObject(e);
            if (data.get("endTime").compareTo(System.currentTimeMillis()) <= 0) {
                appUserService.updateStatus(data.get("id"), Constants.TWO);
                RedisUtils.deleteObject(e);
            }
        });
    }

    /**
     * 红包24小时未领取退回
     */
    @Scheduled(cron = "0 0 0 * * ?")
    @Transactional(rollbackFor = Exception.class)
    public void redTimeOut() {
        log.info("开始执行红包24小时未领取退回任务");

        // 查询所有状态为0（待领取）的红包
        AppHongbaoBo hongbaoBo = new AppHongbaoBo();
        hongbaoBo.setStatus(Constants.SUCCESS); // 0=待领取
        List<AppHongbaoVo> appHongbaoVos = appHongbaoService.queryList(hongbaoBo);

        if (appHongbaoVos == null || appHongbaoVos.isEmpty()) {
            log.info("没有待处理的红包，任务结束");
            return;
        }

        int processedCount = 0;
        int refundCount = 0;

        for (AppHongbaoVo hongbaoVo : appHongbaoVos) {
            try {
                // 检查红包创建时间是否超过24小时
                if (selectDate(hongbaoVo.getCreateTime(), 1)) {
                    // 计算红包总金额
                    BigDecimal totalAmount = hongbaoVo.getHongbaoMoney()
                        .multiply(BigDecimal.valueOf(hongbaoVo.getHongbaoNumber()));

                    // 修改红包状态为2（未领取退回）
                    hongbaoVo.setStatus(Constants.TWO); // 2=未领取退回
                    boolean updateResult = appHongbaoService.updateByVo(hongbaoVo);

                    if (updateResult) {
                        // 退回金额到发送用户
                        boolean refundResult = appAssetService.addBalance(hongbaoVo.getUserId(), totalAmount);

                        if (refundResult) {
                            refundCount++;
                            log.info("红包ID: {} 已退回，金额: {}，退回用户ID: {}",
                                hongbaoVo.getHongbaoId(), totalAmount, hongbaoVo.getUserId());
                        } else {
                            log.error("红包ID: {} 退款失败，用户ID: {}",
                                hongbaoVo.getHongbaoId(), hongbaoVo.getUserId());
                        }
                    } else {
                        log.error("红包ID: {} 状态更新失败", hongbaoVo.getHongbaoId());
                    }
                }
                processedCount++;
            } catch (Exception e) {
                log.error("处理红包ID: {} 时发生异常: {}", hongbaoVo.getHongbaoId(), e.getMessage(), e);
            }
        }

        log.info("红包24小时未领取退回任务完成，处理总数: {}，退回数量: {}", processedCount, refundCount);
    }



    /**
     * 每日凌晨执行，清除用户使用的免费字符数量
     */
    @Scheduled(cron = "0 0 0 * * ?")
    public void clear() {
        appUserService.resetDailyFreeQuota();
    }

    /**
     * 每天凌晨记录当天的用户总数
     */
    @Scheduled(cron = "0 50 23 * * ?")
    public void updateUser() {
        List<AppUserVo> userVos = appUserService.queryList(new AppUserBo());
        RedisUtils.setCacheObject(CacheConstants.USER_SUM, userVos.size());
    }


    /**
     * 获取指定天数之前的条件
     *
     * @param time   创建记录时间
     * @param number 加减几天
     * @return
     */
    public boolean selectDate(Date time, Integer number) {
        Calendar instance = Calendar.getInstance();
        instance.setTime(time);
        instance.add(Calendar.DATE, number);
        return new Date().compareTo(instance.getTime()) >= 0;
    }

    /**
     * 登录过或者7天没有登录的用户
     *
     * @param time   创建用户时间
     * @param number 加减几天
     * @return
     */
    public boolean queryDate(Date time, Integer number) {
        Calendar instance = Calendar.getInstance();
        instance.setTime(new Date());
        instance.add(Calendar.DATE, number);
        return time.compareTo(instance.getTime()) <= 0;
    }

}
