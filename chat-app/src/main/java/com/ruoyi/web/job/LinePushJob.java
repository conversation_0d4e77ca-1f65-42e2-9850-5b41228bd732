package com.ruoyi.web.job;


import com.easemob.im.server.api.token.Token;
import com.ruoyi.app.domain.Message;
import com.ruoyi.app.domain.bo.AppLikeNotesBo;
import com.ruoyi.app.domain.bo.AppUserBo;
import com.ruoyi.app.domain.vo.*;
import com.ruoyi.app.service.*;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.constant.MessageConstants;
import com.ruoyi.common.constant.TaskConstants;
import com.ruoyi.common.enums.LikeStatus;
import com.ruoyi.common.utils.redis.RedisUtils;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.im.core.EasemobTemplate;
import com.ruoyi.web.service.message.SendPushService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.util.*;

@Slf4j
@Component
public class LinePushJob {

    @Autowired
    private IAppUserService appUserService;
    @Autowired
    private IAppDetailService appDetailService;
    @Autowired
    private IAppAssetService appAssetService;
    @Autowired
    private SendPushService sendPushService;
    @Autowired
    private IAppLikeNotesService iAppLikeNotesService;


    /**
     * 给时间长离线的用户发送推送信息
     */
//    @Scheduled(cron = "0 0 12,18 ? * *")
    public void offLinePushtJob() {
        AppUserBo appUserBo = new AppUserBo();
        appUserBo.setRegister(Constants.SUCCESS);
        List<AppUserVo> userVos = appUserService.queryList(appUserBo);
        String tokenKey = CacheConstants.TOKEN_KEY;
        String tokenValue = RedisUtils.getCacheObject(tokenKey);
        Token token;
        if (tokenValue == null) {
            EasemobTemplate imTemplate = SpringUtils.getBean(EasemobTemplate.class);
            try {
                token = imTemplate.appToken();
                tokenValue = token.getValue();
                RedisUtils.setCacheObject(tokenKey, tokenValue, Duration.ofMinutes(Constants.APP_TOKEN_KEY));
            } catch (Exception e) {
                log.info("计划任务发送用户离线推送，获取APP TOKEN失败：{}", e.getMessage());
            }
        }
        for (AppUserVo e : userVos) {
            //未登录过或者7天没有登录的用户
            if (e.getLoginDate() == null || queryDate(e.getLoginDate(), -7)) {
                Message message = new Message();
                HashSet<Long> ids = new HashSet<>();
                ids.add(e.getId());
                message.setToUserIds(ids);
                message.setTitle("「新消息+1」");
                message.setContext("有位Ta匹配到了你，并留言:好像上次有碰到过你呢...去APP里回复一下Ta吧~");
                message.setType(MessageConstants.OFF_LINE_PUSH);
                message.setToken(tokenValue);
                message.setIconUrl(Constants.AVATAR);
                sendPushService.messagePush(message);
            }
        }
    }

    /**
     * 每日凌晨1点处理要注销的账号
     */
    @Scheduled(cron = "0 0 1 * * ?")
    @Transactional(rollbackFor = Exception.class)
    public void checkUserJob() {
        Collection<String> keys = RedisUtils.keys(CacheConstants.LOGIN_OUT + "*");
        keys.forEach(e -> {
            Map<String, Long> data = RedisUtils.getCacheObject(e);
            if (data.get("endTime").compareTo(System.currentTimeMillis()) <= 0) {
                appUserService.updateStatus(data.get("id"), Constants.TWO);
                RedisUtils.deleteObject(e);
            }
        });
    }

    /**
     * 每日凌晨5点解除不喜欢的用户(2天)
     */
    @Scheduled(cron = "0 0 5 * * ?")
    public void updateNoLike() {
        AppLikeNotesBo labelLikeBo = new AppLikeNotesBo();
        labelLikeBo.setStatus(LikeStatus.DISLIKE.getCode());
        ArrayList<Long> ids = new ArrayList<>();
        List<AppLikeNotesVo> likeNotesVos = iAppLikeNotesService.queryList(labelLikeBo);
        likeNotesVos.forEach(e -> {
            if (selectDate(e.getCreateTime(), 2)) {
                ids.add(e.getId());
            }
        });
        if (!ids.isEmpty()) {
            iAppLikeNotesService.deleteWithValidByIds(ids, true);
        }
    }

    /**
     * 每日凌晨执行，清除用户使用的免费字符数量
     */
    @Scheduled(cron = "0 0 0 * * ?")
    public void clear() {
        appUserService.resetDailyFreeQuota();
    }

    /**
     * 每天凌晨记录当天的用户总数
     */
    @Scheduled(cron = "0 50 23 * * ?")
    public void updateUser() {
        List<AppUserVo> userVos = appUserService.queryList(new AppUserBo());
        RedisUtils.setCacheObject(CacheConstants.USER_SUM, userVos.size());
    }


    /**
     * 获取指定天数之前的条件
     *
     * @param time   创建记录时间
     * @param number 加减几天
     * @return
     */
    public boolean selectDate(Date time, Integer number) {
        Calendar instance = Calendar.getInstance();
        instance.setTime(time);
        instance.add(Calendar.DATE, number);
        return new Date().compareTo(instance.getTime()) >= 0;
    }

    /**
     * 登录过或者7天没有登录的用户
     *
     * @param time   创建用户时间
     * @param number 加减几天
     * @return
     */
    public boolean queryDate(Date time, Integer number) {
        Calendar instance = Calendar.getInstance();
        instance.setTime(new Date());
        instance.add(Calendar.DATE, number);
        return time.compareTo(instance.getTime()) <= 0;
    }

}
