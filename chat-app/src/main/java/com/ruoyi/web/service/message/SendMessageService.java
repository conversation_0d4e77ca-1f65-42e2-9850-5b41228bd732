package com.ruoyi.web.service.message;

import com.ruoyi.app.domain.Message;

public interface SendMessageService {


    /**
     * 发送添加好友双方消息
     */
    void user(Message message);

    /**
     * 发送超级喜欢消息
     */
    void like(Message message);

    /**
     * 发送消息到多个用户
     * 不指定用户则使用系统通知发送
     */
    void users(Message message);

    /**
     * 发送通知到单个用户，记录到通知表
     * 不指定用户则使用系统通知发送
     */
    void sendNoticeOne(Message message);

    /**
     * 发送通知到单个用户，记录到通知表
     * 不指定用户则使用系统通知发送
     */
    void sendNotices(Message message);

    /**
     * 发送群组自定义消息
     */
    void group(Message message);


}
