package com.ruoyi.web.service;


import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class BaiduTranslateService {

    private static String APP_ID = "20231203001899761";
    private static String APP_KEY = "IOSIhBMfI2Osd6UFwJ4t";
    private static String URL = "http://api.fanyi.baidu.com/api/trans/vip/translate";


    private static Map<String, Object> buildParams(String query, String from, String to) throws UnsupportedEncodingException {
        Map<String, Object> params = new HashMap<>();
        params.put("q", java.net.URLEncoder.encode(query, "UTF-8"));
        params.put("from", StringUtils.isBlank(from) ? "auto" : from);
        params.put("to", to);
        params.put("appid", APP_ID);
        // 随机数
        String salt = String.valueOf(System.currentTimeMillis());
        params.put("salt", salt);
        // 签名
        String src = APP_ID + query + salt + APP_KEY; // 加密前的原文
        params.put("sign", DigestUtil.md5Hex(src));
        return params;
    }


    public static HashMap<String, Object> translate(String query, String from, String to) {
        HashMap<String, Object> resultMap = new HashMap<>();
        String result = null;
        try {
            Map<String, Object> map = buildParams(query, from, to);
            result = HttpUtil.get(URL, map);
        } catch (Exception e) {
            log.info("百度翻译报错：{}", e.getMessage());
        }
        if(StringUtils.isBlank(result)) {
            return resultMap;
        }
        JSONObject jsonObject = JSONObject.parseObject(result);
        if (StringUtils.isBlank(jsonObject.getString("error_code"))) {
            resultMap.put("from", jsonObject.getString("from"));
            resultMap.put("to", jsonObject.getString("to"));
            JSONArray transResult = jsonObject.getJSONArray("trans_result");
            JSONObject trans_result = JSONObject.parseObject(transResult.get(0).toString());
            resultMap.put("dst", trans_result.getString("dst"));
        }
        return resultMap;
    }
}
