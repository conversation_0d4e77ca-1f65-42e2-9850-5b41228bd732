package com.ruoyi.web.service.message;


import com.alibaba.fastjson.JSONObject;
import com.ruoyi.app.domain.Message;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.im.config.properties.ImProperties;
import com.ruoyi.web.idVerification.HttpUtil;
import com.ruoyi.web.idVerification.Request;
import com.ruoyi.web.idVerification.constant.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class SendPushService {

    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private ImProperties imProperties;

    // 验证码身份证正确性参数配置
    private final static String HOST = "https://id2meta.market.alicloudapi.com";
    private final static String APPCODE = "d3365bbb302d4e36a3a6a7d9b16547fe";
    private final static String APPSECRET = "KVF10VQ5OBvfSiTbhjttfUzWvjeMVAhp";
    private final static String APPKEY = "204360568";
    private final static String PATH = "/id2meta";


    /**
     * 发送PUSH推送消息
     *
     * @param message 推送消息实体
     */
    @Async
    public void messagePush(Message message) {
        List<String> users = new ArrayList<>();
        message.getToUserIds().forEach(e -> users.add(Constants.USER + e));
        HashMap<String, Object> data = new HashMap<>(10);
        //推送的用户列表
        data.put("targets", users);
        data.put("strategy", 3);
        //推送内容
        JSONObject sendData = new JSONObject();
        sendData.put("title", message.getTitle());
        sendData.put("content", message.getContext());
        sendData.put("sound", 1);

        //扩展字段
        HashMap<String, Object> ext = new HashMap<>(1);
        ext.put("type", message.getType());
        sendData.put("ext", ext);

        //跳转信息
        HashMap<String, Object> map = new HashMap<>();
        map.put("action", "com.client.hacker.push.action.chatnotify");
        map.put("activity", "com.client.hacker.main.ChatNotifyActivity");
        HashMap<String, Object> clickAction = new HashMap<>();
        clickAction.put("clickAction", map);
        sendData.put("config", clickAction);

        //环信配置
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("iconUrl", message.getIconUrl());
        sendData.put("easemob", hashMap);

        data.put("pushMessage", sendData);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        String authorization = "Bearer " + message.getToken();
        headers.set("Authorization", authorization);
        HttpEntity<HashMap<String, Object>> formEntity = new HttpEntity<>(data, headers);
        //发送短信请求地址 POST /{orgname}/{appname}/push/single
        String url = getUrl("/push/single");
        restTemplate.postForEntity(url, formEntity, String.class).getBody();
        log.info("线程：{}", Thread.currentThread().getName());
    }

//    /**
//     * 发送PUSH推送消息
//     *
//     * @param message 推送消息实体
//     */
//    @Async
//    public void messagePush(Message message) {
//        List<String> users = new ArrayList<>();
//        message.getToUserIds().forEach(e -> users.add(Constants.USER + e));
//        HashMap<String, Object> data = new HashMap<>(10);
//        //推送的用户列表
//        data.put("targets", users);
//        data.put("strategy", 3);
//        //推送内容
//        JSONObject sendData = new JSONObject();
//        sendData.put("title", message.getTitle());
//        sendData.put("content", message.getContext());
//        sendData.put("type", message.getType());
//        sendData.put("sound", 1);
//        HashMap<String, Object> hashMap = new HashMap<>();
//        hashMap.put("iconUrl", message.getIconUrl());
//        sendData.put("easemob", hashMap);
//        data.put("pushMessage", sendData);
//        HttpHeaders headers = new HttpHeaders();
//        headers.setContentType(MediaType.APPLICATION_JSON);
//        String authorization = "Bearer " + message.getToken();
//        headers.set("Authorization", authorization);
//        HttpEntity<HashMap<String, Object>> formEntity = new HttpEntity<>(data, headers);
//        //发送短信请求地址 POST /{orgname}/{appname}/push/single
//        String url = getUrl("/push/single");
//        restTemplate.postForEntity(url, formEntity, String.class).getBody();
//        log.info("线程：{}", Thread.currentThread().getName());
//    }

    /**
     * 验证身份证号码和姓名
     *
     * @param codeName 身份证姓名
     * @param code     身份证号码
     * @return
     */
    public Boolean checkCode(String codeName, String code) {
        Map<String, String> headers = new HashMap<String, String>();
        //最后在header中的格式(中间是英⽂空格)为Authorization:APPCODE83359fd73fe94948385f570e3c139105
        headers.put("Authorization", "APPCODE " + APPCODE);
        Map<String, String> querys = new HashMap<String, String>();
        querys.put("identifyNum", code);
        querys.put("userName", codeName);
        Boolean result = false;
        try {
            Request request = new Request();
            request.setHost(HOST);
            request.setPath(PATH);
            request.setAppKey(APPKEY);
            request.setAppSecret(APPSECRET);
            request.setQuerys(querys);
            request.setHeaders(headers);
            request.setTimeout(com.ruoyi.web.idVerification.constant.Constants.DEFAULT_TIMEOUT);
            Response response = HttpUtil.httpGet(request.getHost(), request.getPath(), request.getTimeout(), request.getHeaders(), request.getQuerys(), request.getSignHeaderPrefixList(), request.getAppKey(), request.getAppSecret());
            JSONObject body = JSONObject.parseObject(response.getBody());
            if (body.get("code").equals("200")) {
                JSONObject data = body.getJSONObject("data");
                if (data.getString("bizCode").equals(Constants.FAIL)) {
                    result = true;
                }
            }
            //账号欠费，先让通过验证
            if (body.get("code").equals("403")) {
                result = true;
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return result;
    }

    /**
     * 拆分appkey，组装环信请求接口地址
     *
     * @param address 请求环信接口的拼接地址
     * @return
     */
    public String getUrl(String address) {
        String[] split = imProperties.getAppkey().split("#");
        return imProperties.getApi() + "/" + split[0] + "/" + split[1] + address;
    }
}
