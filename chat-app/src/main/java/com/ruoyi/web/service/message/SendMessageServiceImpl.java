package com.ruoyi.web.service.message;

import com.easemob.im.server.model.EMCustomMessage;
import com.easemob.im.server.model.EMKeyValue;
import com.easemob.im.server.model.EMTextMessage;
import com.ruoyi.app.domain.AppNotice;
import com.ruoyi.app.domain.Message;
import com.ruoyi.app.domain.bo.AppNoticeBo;
import com.ruoyi.app.service.IAppNoticeService;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.im.core.EasemobTemplate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.Set;

@Slf4j
@Service
@RequiredArgsConstructor
public class SendMessageServiceImpl implements SendMessageService {

    @Autowired
    private IAppNoticeService appNoticeService;

    /**
     * 发送添加好友双方消息
     */
    @Override
    public void user(Message message) {
        String fromName = message.getFromUser();
        String id = Constants.USER + message.getToUserId();
        Set<String> toUsers = new HashSet<>();
        toUsers.add(id);
        Set<String> fromUsers = new HashSet<>();
        fromUsers.add(fromName);
        Set<EMKeyValue> exts = new HashSet<>();
        if (message.getExts() != null) {
            message.getExts().keySet().forEach(e -> exts.add(EMKeyValue.of(e, message.getExts().get(e))));
        }
        Set<EMKeyValue> ext = new HashSet<>();
        if (message.getExt() != null) {
            message.getExt().keySet().forEach(e -> ext.add(EMKeyValue.of(e, message.getExts().get(e))));
        }
        EMTextMessage textMessage = new EMTextMessage().text(message.getContext());
        EasemobTemplate imTemplate = SpringUtils.getBean(EasemobTemplate.class);
        try {
            imTemplate.send(fromName, "users", toUsers, textMessage, exts);
            imTemplate.send(id, "users", fromUsers, textMessage, ext);
        } catch (Exception e) {
            log.info("添加好友互相发送消息失败:{}", e.getMessage());
        }
        log.info("添加好友互相发送消息成功：{}", message);
    }

    /**
     * 发送超级喜欢消息
     */
    @Override
    public void like(Message message) {
        String fromName = message.getFromUser();
        String id = Constants.USER + message.getToUserId();
        Set<String> toUsers = new HashSet<>();
        toUsers.add(id);
        EMTextMessage textMessage = new EMTextMessage().text(message.getContext());
        EasemobTemplate imTemplate = SpringUtils.getBean(EasemobTemplate.class);
        try {
            imTemplate.send(fromName, "users", toUsers, textMessage, null);
        } catch (Exception e) {
            log.info("发送超级喜欢消息失败:{}", e.getMessage());
        }
        log.info("发送超级喜欢消息成功：{}", message);
    }


    /**
     * 发送消息到多个用户
     * 不指定用户则使用系统通知发送
     */
    @Override
    public void users(Message message) {
        Set<String> toUsers = new HashSet<>();
        String fromName = Constants.SYSTEM_NAME;
        if (message.getFromUserId() != null) {
            fromName = Constants.USER + message.getFromUserId();
        }
        message.getToUserIds().forEach(e -> toUsers.add(Constants.USER + e));
        EMTextMessage textMessage = new EMTextMessage().text(message.getContext());
        EasemobTemplate imTemplate = SpringUtils.getBean(EasemobTemplate.class);
        imTemplate.send(fromName, "users", toUsers, textMessage, null);
    }

    /**
     * 发送通知到多个用户,记录通知消息
     * 不指定用户则使用系统通知发送
     */
    @Override
    public void sendNotices(Message message) {
        ArrayList<AppNotice> appNotices = new ArrayList<>();
        message.getToUserIds().forEach(e -> {
            AppNotice appNotice = new AppNotice();
            appNotice.setUserId(e);
            appNotice.setUrl(message.getUrl());
            appNotice.setImage(message.getImage());
            appNotice.setTitle(message.getTitle());
            appNotice.setContent(message.getContext());
            appNotice.setType(message.getMessageType());
            appNotice.setRecordId(message.getRecordId());
            appNotice.setStatusRead(Constants.MSG_UNREAD);
            appNotices.add(appNotice);
        });
        if (appNoticeService.inserts(appNotices)) {
            Set<String> toUsers = new HashSet<>();
            String fromName = Constants.APP_SYSTEM_NAME;
            if (message.getFromUserId() != null) {
                fromName = Constants.USER + message.getFromUserId();
            }
            message.getToUserIds().forEach(e -> toUsers.add(Constants.USER + e));
            EMTextMessage textMessage = new EMTextMessage().text(message.getContext());
            EasemobTemplate imTemplate = SpringUtils.getBean(EasemobTemplate.class);
            imTemplate.send(fromName, "users", toUsers, textMessage, null);
        }
    }

    /**
     * 发送通知到单个用户,记录通知消息
     * 不指定用户则使用系统通知发送
     */
    @Async
    @Override
    public void sendNoticeOne(Message message) {
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            log.info("休眠2秒失败!");
        }
        AppNoticeBo appNoticeBo = new AppNoticeBo();
        appNoticeBo.setUrl(message.getUrl());
        appNoticeBo.setTitle(message.getTitle());
        appNoticeBo.setUserId(message.getToUserId());
        appNoticeBo.setContent(message.getContext());
        appNoticeBo.setStatusRead(Constants.MSG_UNREAD);
        appNoticeBo.setType(message.getMessageType());
        appNoticeBo.setImage(message.getImage());
        appNoticeBo.setRecordId(message.getRecordId());
        if (appNoticeService.insertByBo(appNoticeBo)) {
            Set<String> toUsers = new HashSet<>();
            String fromName = Constants.APP_SYSTEM_NAME;
            if (message.getFromUserId() != null) {
                fromName = Constants.USER + message.getFromUserId();
            }
            toUsers.add(Constants.USER + message.getToUserId());
            EMTextMessage textMessage = new EMTextMessage().text(message.getContext());
            EasemobTemplate imTemplate = SpringUtils.getBean(EasemobTemplate.class);
            imTemplate.send(fromName, "users", toUsers, textMessage, null);
        }
    }

    /**
     * 发送群组自定义消息
     */
    @Override
    public void group(Message message) {
        Set<EMKeyValue> exts = new HashSet<>();
        if (message.getExts() != null) {
            message.getExts().keySet().forEach(e -> {
                exts.add(EMKeyValue.of(e, message.getExts().get(e)));
            });
        }
        Set<String> toGroups = new HashSet<>();
        toGroups.add(message.getGroupId());
        EMCustomMessage custMessage = new EMCustomMessage().customExtension("context", message.getContext()).customEvent(message.getType());
        EasemobTemplate imTemplate = SpringUtils.getBean(EasemobTemplate.class);
        imTemplate.send(message.getFromUser(), "chatgroups", toGroups, custMessage, exts);
    }

}
