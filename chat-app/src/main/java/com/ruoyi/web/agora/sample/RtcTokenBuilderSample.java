package com.ruoyi.web.agora.sample;


import com.ruoyi.web.agora.media.RtcTokenBuilder;

public class RtcTokenBuilderSample {
    static String appId = "970CA35de60c44645bbae8a215061b33";
    static String appCertificate = "5CFd2fd1755d40ecb72977518be15d3b";
    static String channelName = "7d72365eb983485397e3e3f9d460bdda";
    static String userAccount = "**********";
    static int uid = **********;
    static int expirationTimeInSeconds = 3600;

    public static void main(String[] args) throws Exception {
        RtcTokenBuilder token = new RtcTokenBuilder();
        int timestamp = (int) (System.currentTimeMillis() / 1000 + expirationTimeInSeconds);
        String result = token.buildTokenWithUserAccount(appId, appCertificate,
            channelName, userAccount, RtcTokenBuilder.Role.Role_Publisher, timestamp);
        System.out.println(result);

        result = token.buildTokenWithUid(appId, appCertificate,
            channelName, uid, RtcTokenBuilder.Role.Role_Publisher, timestamp);
        System.out.println(result);
    }

    public static String getRtcToken(int uid) {
        RtcTokenBuilder token = new RtcTokenBuilder();
        int timestamp = (int) (System.currentTimeMillis() / 1000 + expirationTimeInSeconds);

        return token.buildTokenWithUid(appId, appCertificate,
            channelName, uid, RtcTokenBuilder.Role.Role_Publisher, timestamp);
    }
}
