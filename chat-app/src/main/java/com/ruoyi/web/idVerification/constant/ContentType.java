/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package com.ruoyi.web.idVerification.constant;

/**
 * 常用HTTP Content-Type常量
 */
public class ContentType {
    //表单类型Content-Type
    public static final String CONTENT_TYPE_FORM = "application/x-www-form-urlencoded; charset=UTF-8";
    // 流类型Content-Type
    public static final String CONTENT_TYPE_STREAM = "application/octet-stream; charset=UTF-8";
    //JSON类型Content-Type
    public static final String CONTENT_TYPE_JSON = "application/json; charset=UTF-8";
    //XML类型Content-Type
    public static final String CONTENT_TYPE_XML = "application/xml; charset=UTF-8";
    //文本类型Content-Type
    public static final String CONTENT_TYPE_TEXT = "application/text; charset=UTF-8";
}
