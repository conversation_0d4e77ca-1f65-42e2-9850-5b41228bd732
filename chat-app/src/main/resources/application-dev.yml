--- # 监控中心配置
spring.boot.admin.client:
  # 增加客户端开关
  enabled: true
  url: http://localhost:9090/admin
  instance:
    service-host-type: IP
  username: admin
  password: 123456

--- # xxl-job 配置
xxl.job:
  # 执行器开关
  enabled: true
  # 调度中心地址：如调度中心集群部署存在多个地址则用逗号分隔。
  admin-addresses: http://localhost:9100/xxl-job-admin
  # 执行器通讯TOKEN：非空时启用
  access-token: xxl-job
  executor:
    # 执行器AppName：执行器心跳注册分组依据；为空则关闭自动注册
    appname: xxl-job-executor
    # 执行器端口号 执行器从9101开始往后写
    port: 9101
    # 执行器注册：默认IP:PORT
    address:
    # 执行器IP：默认自动获取IP
    ip:
    # 执行器运行日志文件存储磁盘路径
    logpath: ./logs/xxl-job
    # 执行器日志文件保存天数：大于3生效
    logretentiondays: 30

--- # 数据源配置
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    # 动态数据源文档 https://www.kancloud.cn/tracy5546/dynamic-datasource/content
    dynamic:
      # 性能分析插件(有性能损耗 不建议生产环境使用)
      p6spy: true
      # 设置默认的数据源或者数据源组,默认值即为 master
      primary: master
      # 严格模式 匹配不到数据源则报错
      strict: true
      datasource:
        # 主库数据源
        master:
          driverClassName: com.mysql.cj.jdbc.Driver
          # jdbc 所有参数配置参考 https://lionli.blog.csdn.net/article/details/122018562
          # rewriteBatchedStatements=true 批处理优化 大幅提升批量插入更新删除性能(对数据库有性能损耗 使用批量操作应考虑性能问题)
          url: *******************************************************************************************************************************************************************************************************
          username: 524_chat
          password: aw476XeRkrAaWZHT
        # 从库数据源
        slave:
          lazy: true
          driverClassName: com.mysql.cj.jdbc.Driver
          url:
          username:
          password:
      druid:
        # 初始连接数
        initialSize: 5
        # 最小连接池数量
        minIdle: 10
        # 最大连接池数量
        maxActive: 20
        # 配置获取连接等待超时的时间
        maxWait: 60000
        # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
        timeBetweenEvictionRunsMillis: 60000
        # 配置一个连接在池中最小生存的时间，单位是毫秒
        minEvictableIdleTimeMillis: 300000
        # 配置一个连接在池中最大生存的时间，单位是毫秒
        maxEvictableIdleTimeMillis: 900000
        # 配置检测连接是否有效
        validationQuery: SELECT 1
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        # 注意这个值和druid原生不一致，默认启动了stat
        filters: stat

--- # druid 配置
spring.datasource.druid:
  webStatFilter:
    enabled: true
  statViewServlet:
    enabled: true
    # 设置白名单，不填则允许所有访问
    allow:
    url-pattern: /druid/*
    # 控制台管理用户名和密码
    login-username: ruoyi
    login-password: 123456
  filter:
    stat:
      enabled: true
      # 慢SQL记录
      log-slow-sql: true
      slow-sql-millis: 1000
      merge-sql: true
    wall:
      config:
        multi-statement-allow: true

--- # redis 单机配置(单机与集群只能开启一个另一个需要注释掉)
spring:
  redis:
    # 地址
    host: **************
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 5
    # 密码(如没有密码请注释掉)
    password: redis123@
    # 连接超时时间
    timeout: 10s
    # 是否开启ssl
    ssl: false


redisson:
  # redis key前缀
  keyPrefix:
  # 线程池数量
  threads: 4
  # Netty线程池数量
  nettyThreads: 8
  # 单节点配置
  singleServerConfig:
    # 客户端名称
    clientName: ${ruoyi.name}
    # 最小空闲连接数
    connectionMinimumIdleSize: 8
    # 连接池大小
    connectionPoolSize: 32
    # 连接空闲超时，单位：毫秒
    idleConnectionTimeout: 10000
    # 命令等待超时，单位：毫秒
    timeout: 3000
    # 发布和订阅连接池大小
    subscriptionConnectionPoolSize: 50

--- # mail 邮件发送
mail:
  enabled: true
  host: smtp.office365.com
  port: 587
  # 是否需要用户名密码验证
  auth: true
  # 发送方，遵循RFC-822标准
  from: <EMAIL>
  # 用户名（注意：如果使用foxmail邮箱，此处user为qq号）
  user: hbbservice
  # 密码（注意，某些邮箱需要为SMTP服务单独设置密码，详情查看相关帮助）
  pass: 147852qq
  # 使用 STARTTLS安全连接，STARTTLS是对纯文本通信协议的扩展。
  starttlsEnable: true
  # 使用SSL安全连接
  sslEnable: true
  # SMTP超时时长，单位毫秒，缺省值不超时
  timeout: 0
  # Socket连接超时值，单位毫秒，缺省值不超时
  connectionTimeout: 0

# 百度翻译API
baidu:
  appid: 20231203001899761
  keys: IOSIhBMfI2Osd6UFwJ4t

--- # sms 短信
sms:
  enabled: false
  # 阿里云 dysmsapi.aliyuncs.com
  # 腾讯云 sms.tencentcloudapi.com
  endpoint: http://sms.lifala.com.cn/api/KehuSms/send
  accessKeyId: apsms0973329883
  accessKeySecret: nJgbTyWhYAc7F7jr0sLqxcr4UqqvfPpD
  signName:
  # 腾讯专用
  sdkAppId:

--- # IM 聊天功能
im:
  # 环信服务地址
  api: https://a1.easemob.com
  #环信 APPKEY
  appkey: 1122231130162043#jya
  #环信 Client ID
  clientId: YXA6PuvlxwLqTT6plFbRyxorjg
  #环信 ClientSecret
  clientSecret: YXA6v1Ncm7CeQKG0rW1f8e_qyYGEa3I

--- # AGORA 声望音视频
agora:
  # 声网控制台创建项目时生成的 App ID
  appId:
  # 项目的 App 证书
  appCertificate:
  # 频道名称，长度在 64 个字节以内
  channelName: chat
  # 从生成到过期的时间长度，单位为秒
  expirationTimeInSeconds: 7200

--- # 支付回调地址配置
pay-url:
  # 支付宝VIP开通回调地址
  alipayNotifyUrl: https://app.huiyouhy.top/app/vipOrder/aliPayCallBack
  # 微信VIP开通回调地址
  wechatNotifyUrl: https://app.huiyouhy.top/app/vipOrder/wxPayCallBack
  # 支付宝购买商品回调地址
  alipayNotifyShopUrl: http://**************:8081/app/goodsOrders/aliPayCallBack
  # 微信购买商品回调地址
  wechatNotifyShopUrl: http://**************:8081/app/goodsOrders/wxPayCallBack
