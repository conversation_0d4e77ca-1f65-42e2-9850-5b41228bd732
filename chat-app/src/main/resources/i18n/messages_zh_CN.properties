#错误消息
not.null=* 必须填写
userName.not.exists=账号不存在
userName.is.disabled=账号已禁用，请联系管理员!
userName.is.logOff=账号已注销
user.jcaptcha.error=验证码错误
user.jcaptcha.expire=验证码已失效
user.not.exists=对不起, 您的账号：{0} 不存在.

id.not.blank=ID不能为空
gift.not.blank=礼物不存在
user.not.blank=用户不存在
data.not.exist=数据不存在
moments.not.delete.rule=无权限删除动态
moments.not.type=暂不支持的动态类型
moments.not.video.blank=动态视频不能为空
moments.upload.max.image=最多上传{0}张照片
moments.not.emoji.blank=点赞表情不存在

translate.not.to.blank=目标语言不能为空
translate.not.context.blank=翻译内容不能为空
translate.not.context.max=翻译内容最多2000个字符

hongbao.money.not.blank=红包金额不能为空
hongbao.user.not.blank=接收用户不能为空
hongbao.not.blank=红包不存在
hongbao.receive.end=红包已领取或退回

vip.open.view=请开通会员查看
vip.open.vip.view=请开通VIP会员查看
collect.user.id.end=已经收藏了用户


user.password.not.match=用户不存在/密码错误
user.oldPassword.error=原密码错误
user.password.retry.limit.count=密码输入错误{0}次
user.password.retry.limit.exceed=密码输入错误{0}次，帐户锁定{1}分钟
user.password.blank = 密码不能为空
user.password.delete=对不起，您的账号：{0} 已被删除
user.blocked=对不起，您的账号：{0} 已禁用，请联系管理员
role.blocked=角色已封禁，请联系管理员
user.logout.success=退出成功

#验证消息
user.username.not.blank=用户名不能为空
user.password.not.blank=密码不能为空
user.username.length.valid=用户名长度最少{min}个字符
user.sex.not.blank=请选择性别
user.sex.not.birthday=请选择生日


length.not.valid=长度必须在{min}到{max}个字符之间
user.username.not.valid=* 2到20个汉字、字母、数字或下划线组成，且必须以非数字开头
user.password.length.valid=用户密码长度必须在{min}到{max}个字符之间
user.password.not.valid=* 5-50个字符
user.email.not.valid=邮箱格式错误
user.email.not.blank=邮箱地址不能为空
user.phonenumber.not.blank=用户手机号不能为空
user.mobile.phone.number.not.valid=手机号格式错误

#提示消息
system.info.error=失败
system.info.success=成功
user.register.success=注册成功
user.register.error=注册失败，请联系系统管理人员
user.login.success=登录成功
user.error.register=用户名已注册!
user.error.email.register=邮箱|电话已注册!
user.error.email.submit = 邮箱地址已提交
user.error.email.error = 邮箱地址错误
user.error.balance=金币数量不足
user.error.balance.recharge=金币数量不足请充值!
user.error.integral=字符数量不足
user.error.context.blank=发送内容不能为空


user.register.save.error=保存用户 {0} 失败，注册账号已存在
user.notfound=请重新登录
user.forcelogout=管理员强制退出，请重新登录
user.unknown.error=未知错误，请重新登录
upload.exceed.maxSize=上传的文件大小超出限制的文件大小！<br/>允许的文件最大大小是：{0}MB！
upload.filename.exceed.length=上传的文件名最长{0}个字符
##权限
no.permission=您没有数据的权限，请联系管理员添加权限 [{0}]
no.create.permission=您没有创建数据的权限，请联系管理员添加权限 [{0}]
no.update.permission=您没有修改数据的权限，请联系管理员添加权限 [{0}]
no.delete.permission=您没有删除数据的权限，请联系管理员添加权限 [{0}]
no.export.permission=您没有导出数据的权限，请联系管理员添加权限 [{0}]
no.view.permission=您没有查看数据的权限，请联系管理员添加权限 [{0}]
repeat.submit.message=不允许重复提交，请稍候再试
rate.limiter.message=访问过于频繁，请稍候再试
sms.code.not.blank=短信验证码不能为空
sms.code.retry.limit.count=短信验证码输入错误{0}次
sms.code.retry.limit.exceed=短信验证码输入错误{0}次，帐户锁定{0}分钟
xcx.code.not.blank=小程序code不能为空
