<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.mapper.AppGroupMapper">

    <resultMap type="com.ruoyi.app.domain.AppGroup" id="AppGroupResult">
        <result property="id" column="id"/>
        <result property="groupId" column="group_id"/>
        <result property="name" column="name"/>
        <result property="channelName" column="channel_name"/>
        <result property="description" column="description"/>
        <result property="type" column="type"/>
        <result property="allowinvites" column="allowinvites"/>
        <result property="membersonly" column="membersonly"/>
        <result property="inviteNeedConfirm" column="invite_need_confirm"/>
        <result property="disabled" column="disabled"/>
        <result property="custom" column="custom"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
    </resultMap>


</mapper>
