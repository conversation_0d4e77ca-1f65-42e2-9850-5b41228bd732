<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.mapper.AppGroupUserMapper">

    <resultMap type="com.ruoyi.app.domain.AppGroupUser" id="AppGroupUserResult">
        <result property="id" column="id"/>
        <result property="groupId" column="group_id"/>
        <result property="userId" column="user_id"/>
        <result property="nikeName" column="nike_name"/>
        <result property="groupName" column="group_name"/>
        <result property="groupShow" column="group_show"/>
        <result property="place" column="place"/>
        <result property="status" column="status"/>
        <result property="talkStatus" column="talk_status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
    </resultMap>


</mapper>
