<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.mapper.AppRecordMapper">

    <resultMap type="com.ruoyi.app.domain.AppRecord" id="AppRecordResult">
        <result property="id" column="id"/>
        <result property="recordId" column="record_id"/>
        <result property="userId" column="user_id"/>
        <result property="recordUserId" column="record_user_id"/>
        <result property="commentId" column="comment_id"/>
        <result property="type" column="type"/>
        <result property="readStatus" column="read_status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>


</mapper>
