<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.mapper.AppUserMapper">

    <resultMap type="com.ruoyi.app.domain.AppUser" id="AppUserResult">
        <result property="id" column="id"/>
        <result property="userName" column="user_name"/>
        <result property="nickName" column="nick_name"/>
        <result property="email" column="email"/>
        <result property="phone" column="phone"/>
        <result property="uuid" column="uuid"/>
        <result property="sex" column="sex"/>
        <result property="inviteCode" column="invite_code"/>
        <result property="parentId" column="parent_id"/>
        <result property="signature" column="signature"/>
        <result property="avatar" column="avatar"/>
        <result property="photo" column="photo"/>
        <result property="password" column="password"/>
        <result property="occupation" column="occupation"/>
        <result property="school" column="school"/>
        <result property="birthday" column="birthday"/>
        <result property="height" column="height"/>
        <result property="target" column="target"/>
        <result property="targetStatus" column="target_status"/>
        <result property="home" column="home"/>
        <result property="province" column="province"/>
        <result property="city" column="city"/>
        <result property="latitude" column="latitude"/>
        <result property="longitude" column="longitude"/>
        <result property="register" column="register"/>
        <result property="onlineStatus" column="online_status"/>
        <result property="status" column="status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="loginIp" column="login_ip"/>
        <result property="loginDate" column="login_date"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="remark" column="remark"/>
    </resultMap>


</mapper>
