package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP超级喜欢的标记对象 app_like_sign
 *
 * <AUTHOR>
 * @date 2023-02-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_like_sign")
public class AppLikeSign extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 超级喜欢的用户ID
     */
    private Long sideId;
    /**
     * 已读状态(1=已读,0=未读)
     */
    private String status;
    /**
     * 备注
     */
    private String remark;
    /**
     * 删除标志（0=存在,2=删除）
     */
    @TableLogic
    private String delFlag;

}
