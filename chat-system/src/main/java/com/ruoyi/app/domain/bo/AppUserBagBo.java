package com.ruoyi.app.domain.bo;

import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;

import java.util.Date;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * App用户签到领取奖励业务对象 app_user_bag
 *
 * <AUTHOR>
 * @date 2023-03-29
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class AppUserBagBo extends BaseEntity {

    /**
     *
     */
    @NotNull(message = "不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long userId;

    /**
     * 背包类型
     */
    @NotBlank(message = "背包类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private String type;

    /**
     * 背包数量
     */
    @NotNull(message = "背包数量不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer number;

    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空", groups = {AddGroup.class, EditGroup.class})
    private String remark;
}
