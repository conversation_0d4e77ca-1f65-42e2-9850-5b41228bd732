package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * APP举报类型对象 app_report
 *
 * <AUTHOR>
 * @date 2023-01-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_report")
public class AppReport extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 举报内容
     */
    private String context;
    /**
     * 举报类型(0=群组,1=个人)
     */
    private String type;
    /**
     * 删除标志（0=存在,2=删除）
     */
    @TableLogic
    private String delFlag;
    /**
     * 帐号状态(0=正常,1=停用)
     */
    private String status;

}
