package com.ruoyi.app.domain.bo;

import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * APP用户地址信息业务对象 app_address
 *
 * <AUTHOR>
 * @date 2022-12-21
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class AppAddressBo extends BaseEntity {

    /**
     * ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 收货姓名
     */
    @NotBlank(message = "收货姓名不能为空", groups = {AddGroup.class, EditGroup.class})
    private String nickName;

    /**
     * 收货电话
     */
    @NotBlank(message = "收货电话不能为空", groups = {AddGroup.class, EditGroup.class})
    private String mobile;

    /**
     * 收货城市(省市区)
     */
    private String city;

    /**
     * 城市代码
     */
    @NotBlank(message = "收货(省市区)不能为空", groups = {AddGroup.class})
    private String cityCode;

    /**
     * 详细地址
     */
    @NotBlank(message = "详细地址不能为空", groups = {AddGroup.class, EditGroup.class})
    private String cityDetails;

    /**
     * 地址是否默认
     */
    private String status;


}
