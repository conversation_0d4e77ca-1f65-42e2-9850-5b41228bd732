package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP用户喜欢记录对象 app_like_notes
 *
 * <AUTHOR>
 * @date 2025-02-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_like_notes")
public class AppLikeNotes extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 操作的用户ID
     */
    private Long sideId;
    /**
     * 状态(0=喜欢，2=不喜欢)
     */
    private String status;
    /**
     * 状态(0=喜欢，1=超级喜欢)
     */
    private String likeStatus;
    /**
     * 已读状态(1=已读,0=未读)
     */
    private String readStatus;
    /**
     * 备注
     */
    private String remark;
    /**
     * 删除标志（0=存在,2=删除）
     */
    @TableLogic
    private String delFlag;

}
