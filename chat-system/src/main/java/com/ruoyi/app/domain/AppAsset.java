package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import java.math.BigDecimal;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP用户资产信息对象 app_asset
 *
 * <AUTHOR>
 * @date 2025-12-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_asset")
public class AppAsset extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 用户资产
     */
    private BigDecimal balance;
    /**
     * 用户积分
     */
    private BigDecimal integral;
    /**
     * 帐号状态(0=正常,1=停用)
     */
    private String status;

}
