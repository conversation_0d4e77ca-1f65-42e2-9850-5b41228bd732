package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * APP用户通知信息对象 app_notice
 *
 * <AUTHOR>
 * @date 2022-12-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_notice")
public class AppNotice extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 标题
     */
    private String title;
    /**
     * 内容
     */
    private String content;

    /**
     * 追朔用户ID
     */
    private Long fromId;

    /**
     * 接收人
     */
    private Long userId;
    /**
     * 通知的背景图片
     */
    private String image;
    /**
     * 通知类型
     */
    private String type;

    /**
     * 记录的ID
     */
    private Long recordId;

    /**
     * 跳转的地址
     */
    private String url;

    /**
     * 已读状态(1=已读，0=未读)
     */
    private String statusRead;
    /**
     * 备注
     */
    private String remark;
    /**
     * 删除标志（0=存在 2=删除）
     */
    @TableLogic
    private String delFlag;

}
