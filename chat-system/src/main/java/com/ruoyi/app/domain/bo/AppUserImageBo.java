package com.ruoyi.app.domain.bo;

import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * APP用户照片业务对象 app_user_image
 *
 * <AUTHOR>
 * @date 2025-01-15
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class AppUserImageBo extends BaseEntity {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户图片
     */
    @NotBlank(message = "图片不能为空", groups = {AddGroup.class, EditGroup.class})
    private String image;

    /**
     * 备注
     */
    private String remark;


}
