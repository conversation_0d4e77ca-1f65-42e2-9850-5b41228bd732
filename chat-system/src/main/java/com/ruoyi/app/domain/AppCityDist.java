package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP城市列对象 app_city_dist
 *
 * <AUTHOR>
 * @date 2023-03-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_city_dist")
public class AppCityDist extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 省直辖市
     */
    private String province;
    /**
     * 类型
     */
    private String type;
    /**
     * 城市
     */
    private String city;
    /**
     * 上级ID
     */
    private Long provinceId;
    /**
     * 县城
     */
    private String district;
    /**
     * 城市代码
     */
    private Long cityId;

}
