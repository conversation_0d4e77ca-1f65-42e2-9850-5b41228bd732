package com.ruoyi.app.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;



/**
 * APP用户VIP视图对象 app_user_vip
 *
 * <AUTHOR>
 * @date 2023-03-16
 */
@Data
@ExcelIgnoreUnannotated
public class AppUserVipVo {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * VIP结束时间
     */
    @ExcelProperty(value = "VIP结束时间")
    private Long endTime;

    /**
     * 当前等级
     */
    private Integer level;

    /**
     * 当前经验
     */
    private Integer experience;

    /**
     * VIP的类型
     */
    private String type;

    /**
     * VIP的ID
     */
    private Long vipId;
    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * vip到期时间
     */
    private String vipExpireTime;

    /**
     * vip名称
     */
    private String vipName;
}
