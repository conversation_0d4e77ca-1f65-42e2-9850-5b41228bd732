package com.ruoyi.app.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ruoyi.common.annotation.ExcelDictFormat;
import com.ruoyi.common.convert.ExcelDictConvert;
import lombok.Data;

import java.util.Date;


/**
 * APP用户意见反馈视图对象 app_feedback
 *
 * <AUTHOR>
 * @date 2025-02-09
 */
@Data
@ExcelIgnoreUnannotated
public class AppFeedbackVo {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 反馈类型
     */
    @ExcelProperty(value = "反馈类型")
    private String type;

    /**
     * 反馈内容
     */
    @ExcelProperty(value = "反馈内容")
    private String context;

    /**
     * 反馈图片
     */
    @ExcelProperty(value = "反馈图片")
    private String image;

    /**
     * 联系电话
     */
    @ExcelProperty(value = "联系电话")
    private String phone;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

}
