package com.ruoyi.app.domain.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;

import java.util.Date;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP群组业务对象 app_group_user
 *
 * <AUTHOR>
 * @date 2023-01-29
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class AppGroupUserBo extends BaseEntity {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 群组记录ID
     */
    @NotNull(message = "群组记录ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long groupId;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long userId;


    /**
     * 群内用户昵称
     */
    @NotBlank(message = "群内用户昵称", groups = {AddGroup.class, EditGroup.class})
    private String nikeName;

    /**
     * 群组描述
     */
    @NotBlank(message = "群组描述不能为空", groups = {AddGroup.class, EditGroup.class})
    private String groupName;

    /**
     * 是否显示群昵称
     */
    @ExcelProperty(value = "是否显示群昵称")
    private String groupShow;

    /**
     * 群组身份(1=群主，2=管理员，3=群成员)
     */
    @NotBlank(message = "群组身份(1=群主，2=管理员，3=群成员)不能为空", groups = {AddGroup.class, EditGroup.class})
    private String place;

    /**
     * 状态(0=正常，1=禁言)
     */
    @NotBlank(message = "状态(0=正常，1=禁言)不能为空", groups = {AddGroup.class, EditGroup.class})
    private String status;

    /**
     * 对讲状态(0=正常，1=勿扰)
     */
    private String talkStatus;

    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空", groups = {AddGroup.class, EditGroup.class})
    private String remark;


}
