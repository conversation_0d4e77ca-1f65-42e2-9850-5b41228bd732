package com.ruoyi.app.domain.bo;

import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP用户关系业务对象 app_friend_relation
 *
 * <AUTHOR>
 * @date 2025-07-28
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class AppFriendRelationBo extends BaseEntity {

    /**
     * ID
     */
    private Long relationId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 关注用户ID
     */
    @NotNull(message = "关注用户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long toUserId;

    /**
     * 关注来源(0=收藏，1=足迹，2=点赞)
     */
    @NotBlank(message = "关注来源(0=收藏，1=足迹，2=点赞)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String type;

    /**
     * 已读状态(1=已读,0=未读)
     */
    private String status;

    /**
     * 备注
     */
    private String remark;


}
