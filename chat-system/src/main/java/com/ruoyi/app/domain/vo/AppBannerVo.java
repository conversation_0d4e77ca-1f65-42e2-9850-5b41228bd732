package com.ruoyi.app.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ruoyi.common.annotation.ExcelDictFormat;
import com.ruoyi.common.convert.ExcelDictConvert;
import lombok.Data;
import java.util.Date;



/**
 * APP商城的轮播图视图对象 app_banner
 *
 * <AUTHOR>
 * @date 2025-04-20
 */
@Data
@ExcelIgnoreUnannotated
public class AppBannerVo {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 图片
     */
    @ExcelProperty(value = "图片")
    private String image;

    /**
     * 类型
     */
    @ExcelProperty(value = "类型")
    private String type;

    /**
     * 状态(1=不显示,0=显示)
     */
    @ExcelProperty(value = "状态(1=不显示,0=显示)")
    private String status;

    /**
     * 跳转的商品ID
     */
    private String goodsId;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


}
