package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP学校信息对象 app_school
 *
 * <AUTHOR>
 * @date 2023-01-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_school")
public class AppSchool extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 学校名称
     */
    private String name;
    /**
     * 状态(0=生效，1=不生效)
     */
    private String status;

}
