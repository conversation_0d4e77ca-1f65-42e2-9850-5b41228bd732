package com.ruoyi.app.domain.bo;

import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;


@Data
public class AppTranslateBo {

    /**
     * 源目标语言
     */
    private String from;

    /**
     * 翻译后语言
     */
    @NotBlank(message = "{translate.not.to.blank}")
    private String to;


    /**
     * 翻译内容
     */
    @NotBlank(message = "{translate.not.context.blank}")
    @Length(min = 1, max = 2000, message = "{translate.not.context.max}")
    private String context;
}
