package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 活动展馆管理对象 app_show
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_show")
public class AppShow extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 活动标题
     */
    private String title;
    /**
     * 活动内容
     */
    private String context;
    /**
     * 活动图片
     */
    private String images;
    /**
     * 描述
     */
    private String remark;

}
