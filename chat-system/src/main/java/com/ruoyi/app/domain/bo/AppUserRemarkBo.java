package com.ruoyi.app.domain.bo;

import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP用户备注业务对象 app_user_remark
 *
 * <AUTHOR>
 * @date 2025-08-12
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class AppUserRemarkBo extends BaseEntity {

    /**
     * ID

     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 备注操作的用户
     */
    private Long friendId;

    /**
     * 备注内容
     */
    @NotBlank(message = "备注内容不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remark;


}
