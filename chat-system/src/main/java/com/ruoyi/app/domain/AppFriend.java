package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * APP用户关注对象 app_friend
 *
 * <AUTHOR>
 * @date 2023-01-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_friend")
public class AppFriend extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 关注用户ID
     */
    private Long friendId;

    /**
     * 来源状态（0=关注,1=喜欢）
     */
    private String fromStatus;

    /**
     * 已读状态(1=已读,0=未读)
     */
    private String status;

    /**
     * 是否置顶(0=不置顶，1=置顶)
     */
    private String topping;

    /**
     * 别名
     */
    private String remark;

    /**
     * 删除标志（0=存在,2=删除）
     */
    @TableLogic
    private String delFlag;

}
