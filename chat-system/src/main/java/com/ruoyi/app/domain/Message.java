package com.ruoyi.app.domain;


import lombok.Data;

import java.util.HashMap;
import java.util.Set;

@Data
public class Message {


    /**
     * 发送用户ID
     */
    private Long fromUserId;

    /**
     * 发送用户名
     */
    private String fromUser;

    /**
     * 接收用户ID
     */
    private Long toUserId;

    /**
     * 接收用户IDS
     */
    private Set<Long> toUserIds;

    /**
     * 接收群组IDS
     */
    private String groupId;

    /**
     * 发送的消息标题
     */
    private String title;

    /**
     * 发送的消息内容
     */
    private String context;

    /**
     * 消息发送类型
     * addGroupContext
     */
    private String type;

    /**
     * 消息系统内定义类型
     * addGroupContext
     */
    private String messageType;

    /**
     * 消息扩展字段
     * addGroupContext
     */
    private HashMap<String, Object> exts;

    /**
     * 消息扩展字段2
     * addGroupContext
     */
    private HashMap<String, Object> ext;

    /**
     * 环信rest api token
     */
    private String token;

    /**
     * 推送的图标地址
     */
    private String iconUrl;

    /**
     * 自定义的跳转地址
     */
    private String url;

    /**
     * 自定义的图片信息
     */
    private String image;

    /**
     * 记录ID
     */
    private Long recordId;

}
