package com.ruoyi.app.domain.bo;

import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * App用户签到记录业务对象 app_user_sign
 *
 * <AUTHOR>
 * @date 2025-03-28
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class AppUserSignBo extends BaseEntity {

    /**
     *
     */
    @NotNull(message = "不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 签到天数
     */
    @NotNull(message = "签到天数不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer day;

    /**
     * 签到时间
     */
    @NotNull(message = "签到时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long signTime;

    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remark;


}
