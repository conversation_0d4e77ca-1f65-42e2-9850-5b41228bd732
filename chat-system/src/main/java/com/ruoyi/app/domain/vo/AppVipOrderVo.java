package com.ruoyi.app.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ruoyi.common.annotation.ExcelDictFormat;
import com.ruoyi.common.convert.ExcelDictConvert;
import lombok.Data;
import java.util.Date;



/**
 * APPv会员VIP订单视图对象 app_vip_order
 *
 * <AUTHOR>
 * @date 2025-03-17
 */
@Data
@ExcelIgnoreUnannotated
public class AppVipOrderVo {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 充值VIP类型
     */
    @ExcelProperty(value = "充值VIP类型")
    private Long vipId;

    /**
     * 订单号
     */
    @ExcelProperty(value = "订单号")
    private String orderNo;

    /**
     * 支付状态(0=未支付  1=已支付)
     */
    @ExcelProperty(value = "支付状态(0=未支付  1=已支付)")
    private String status;

    /**
     * 支付方式
     */
    @ExcelProperty(value = "支付方式")
    private String payType;

    /**
     * 会员类型 1 正常购买 2 1元体验 3 积分兑换
     */
    @ExcelProperty(value = "会员类型 1 正常购买 2 1元体验 3 积分兑换")
    private String type;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 用户名称
     */
    @ExcelProperty(value = "用户名称")
    private String nickName;


}
