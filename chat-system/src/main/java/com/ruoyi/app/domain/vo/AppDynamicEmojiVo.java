package com.ruoyi.app.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ruoyi.common.annotation.ExcelDictFormat;
import com.ruoyi.common.convert.ExcelDictConvert;
import lombok.Data;
import java.util.Date;



/**
 * 动态表情视图对象 app_dynamic_emoji
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Data
@ExcelIgnoreUnannotated
public class AppDynamicEmojiVo {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 表情名称
     */
    @ExcelProperty(value = "表情名称")
    private String emojiName;

    /**
     * 表情图片
     */
    @ExcelProperty(value = "表情图片")
    private String emojiImage;

    /**
     * 表情动态
     */
    @ExcelProperty(value = "表情动态")
    private String emojiSvgImage;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


    private Integer sort;


}
