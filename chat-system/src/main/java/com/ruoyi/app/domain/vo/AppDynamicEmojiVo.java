package com.ruoyi.app.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.amazonaws.partitions.PartitionRegionImpl;
import com.ruoyi.common.annotation.ExcelDictFormat;
import com.ruoyi.common.convert.ExcelDictConvert;
import lombok.Data;
import java.util.Date;
import java.util.List;


/**
 * 动态表情视图对象 app_dynamic_emoji
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Data
@ExcelIgnoreUnannotated
public class AppDynamicEmojiVo {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 表情名称
     */
    @ExcelProperty(value = "表情名称")
    private String emojiName;

    /**
     * 表情图片
     */
    @ExcelProperty(value = "表情图片")
    private String emojiImage;

    /**
     * 表情动态
     */
    @ExcelProperty(value = "表情动态")
    private String emojiSvgImage;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    private Integer sort;
    /**
     * 状态(0=生效，1=不生效)
     */
    private String status;

    /**
     * 点赞数量
     */
    private Integer starNumber;

    /**
     * 当前表情点赞的用户列表
     */
    private List<UserFilterVo> userVos;
}
