package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP礼物记录对象 app_gift_order
 *
 * <AUTHOR>
 * @date 2022-12-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_gift_order")
public class AppGiftOrder extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 赠送用户ID
     */
    private Long toUserId;
    /**
     * 礼物名称
     */
    private String name;
    /**
     * 礼物ID
     */
    private Long giftId;

    private BigDecimal amount;

    /**
     * 礼物数量
     */
    private Integer number;
    /**
     * 1=礼物
     */
    private Integer type;
    /**
     * 备注
     */
    private String remark;

}
