package com.ruoyi.app.domain.bo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP时光约会业务对象 app_agree
 *
 * <AUTHOR>
 * @date 2024-03-25
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class AppAgreeBo extends BaseEntity {

    /**
     * ID
     */
    private Long id;

    /**
     * 发出邀请用户的ID
     */
    private Long userId;

    /**
     * 用户昵称
     */
    private String userName;

    /**
     * 被邀请的用户ID
     */
    @NotNull(message = "被邀请的用户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long sideId;

    /**
     * 邀请用户昵称
     */
    private String sideName;

    /**
     * 约会的时间
     */
    private Long agreeTime;

    /**
     * 状态(0=通过，1=拒绝，2=申请中，3=超时)
     */
    private String status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 类型(1=我邀请的列表，2=邀请我的列表)
     */
    private String type;

}
