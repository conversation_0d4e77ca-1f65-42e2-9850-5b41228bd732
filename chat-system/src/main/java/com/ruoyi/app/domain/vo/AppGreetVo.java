package com.ruoyi.app.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ruoyi.common.annotation.ExcelDictFormat;
import com.ruoyi.common.convert.ExcelDictConvert;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.Date;


/**
 * APP打招呼记录
 * 视图对象 app_greet
 *
 * <AUTHOR>
 * @date 2023-03-23
 */
@Data
@ExcelIgnoreUnannotated
public class AppGreetVo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 对方ID
     */
    @ExcelProperty(value = "对方ID")
    private Long fromId;

    /**
     * 发送数量
     */
    @ExcelProperty(value = "发送数量")
    private Integer number;

    /**
     * 发送内容
     */
    private String context;

    /**
     * 记录状态
     * 已读状态(1=已读,0=未读)
     */
    private String status;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 发送用户信息
     */
    private UserVo user;

    /**
     * 对方用户信息
     */
    private UserVo fromUser;

    /**
     * 打招呼和提示的类型
     */
    private String type;


}
