package com.ruoyi.app.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;



/**
 * APP用户的通讯录视图对象 app_address_book
 *
 * <AUTHOR>
 * @date 2022-12-26
 */
@Data
@ExcelIgnoreUnannotated
public class AppAddressBookVo {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 通讯录ID
     */
    @ExcelProperty(value = "通讯录ID")
    private Long bookId;

    /**
     * 备注名称
     */
    @ExcelProperty(value = "备注名称")
    private String remark;

    /**
     * 通讯录用户信息
     */
    private UserVo userVo;

}
