package com.ruoyi.app.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ruoyi.common.annotation.ExcelDictFormat;
import com.ruoyi.common.convert.ExcelDictConvert;
import lombok.Data;

import java.util.Date;
import java.util.List;


/**
 * APP群组视图对象 app_group
 *
 * <AUTHOR>
 * @date 2025-01-29
 */
@Data
@ExcelIgnoreUnannotated
public class AppGroupVo {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 群组ID
     */
    @ExcelProperty(value = "群组ID")
    private String groupId;

    /**
     * 群名称
     */
    @ExcelProperty(value = "群名称")
    private String name;

    /**
     * 群频道名称
     */
    private String channelName;

    /**
     * 群公告
     */
    @ExcelProperty(value = "群公告")
    private String description;

    /**
     * 群类型(0=公开群，1=私有群)
     */
    @ExcelProperty(value = "群类型(0=公开群，1=私有群)")
    private String type;

    /**
     * 是否允许群成员邀请别人加入此群(0=允许，1=不允许)只对私有群有效
     */
    @ExcelProperty(value = "是否允许群成员邀请别人加入此群(0=允许，1=不允许)只对私有群有效")
    private String allowinvites;

    /**
     * 用户申请入群是否需要群主或者群管理员审批(0=不需要，1=需要)
     */
    @ExcelProperty(value = "用户申请入群是否需要群主或者群管理员审批(0=不需要，1=需要)")
    private String membersonly;

    /**
     * 邀请用户入群时是否需要被邀用户同意(0=不需要，1=需要)
     */
    @ExcelProperty(value = "邀请用户入群时是否需要被邀用户同意(0=不需要，1=需要)")
    private String inviteNeedConfirm;

    /**
     * 群组状态(0=正常，1=封禁)
     */
    @ExcelProperty(value = "群组状态(0=正常，1=封禁)")
    private String disabled;

    /**
     * 群扩展信息
     */
    @ExcelProperty(value = "群扩展信息")
    private String custom;

    /**
     * 全局禁言(0=未禁言，1=禁言)
     */
    @ExcelProperty(value = "全局禁言")
    private String muteAll;


    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 群组当前用户信息
     */
    private AppGroupUserVo appGroupUserVo;

    /**
     * 群组成员信息
     */
    private List<AppUserVo> userVoList;

    /**
     * 是否加入了群组
     */
    private Boolean isJoin;

}
