package com.ruoyi.app.domain.bo;

import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APPv会员VIP订单业务对象 app_vip_order
 *
 * <AUTHOR>
 * @date 2023-03-17
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class AppVipOrderBo extends BaseEntity {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 充值VIP类型
     */
    @NotNull(message = "充值VIP类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long vipId;

    /**
     * 订单号
     */
    @NotBlank(message = "订单号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String orderNo;

    /**
     * 支付状态(0=未支付  1=已支付)
     */
    @NotBlank(message = "支付状态(0=未支付  1=已支付)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;

    /**
     * 支付方式
     */
    @NotBlank(message = "支付方式不能为空", groups = { AddGroup.class, EditGroup.class })
    private String payType;

    /**
     * 会员类型 1 正常购买 2 1元体验 3 积分兑换
     */
    @NotBlank(message = "会员类型 1 正常购买 2 1元体验 3 积分兑换不能为空", groups = { AddGroup.class, EditGroup.class })
    private String type;

    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remark;


}
