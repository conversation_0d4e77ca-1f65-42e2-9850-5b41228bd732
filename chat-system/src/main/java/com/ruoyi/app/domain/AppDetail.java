package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import java.math.BigDecimal;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP用户账单明细对象 app_detail
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_detail")
public class AppDetail extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 积分数量
     */
    private BigDecimal balance;
    /**
     * 积分手续费
     */
    private BigDecimal freeBalance;
    /**
     * 账单类型[]
     */
    private String type;
    /**
     * 类型(1=收入，2=支出)
     */
    private String flow;
    /**
     * 金币类型(1=余额，2=积分)
     */
    private String coinTyp;
    /**
     * 来源用户ID
     */
    private Long fromId;
    /**
     * 创建时间戳
     */
    private Integer insertTime;
    /**
     * 删除标志（0=存在,2=删除）
     */
    @TableLogic
    private String delFlag;
    /**
     * 备注
     */
    private String remark;

}
