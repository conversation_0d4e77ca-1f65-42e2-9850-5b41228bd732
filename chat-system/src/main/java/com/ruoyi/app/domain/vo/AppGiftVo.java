package com.ruoyi.app.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ruoyi.common.annotation.ExcelDictFormat;
import com.ruoyi.common.convert.ExcelDictConvert;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;



/**
 * APP礼物视图对象 app_gift
 *
 * <AUTHOR>
 * @date 2025-12-14
 */
@Data
@ExcelIgnoreUnannotated
public class AppGiftVo {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 礼物名称
     */
    @ExcelProperty(value = "礼物名称")
    private String name;

    /**
     * 礼物图片地址
     */
    @ExcelProperty(value = "礼物图片地址")
    private String image;

    /**
     * 礼物特效
     */
    @ExcelProperty(value = "礼物特效")
    private String imageSvg;

    /**
     * 礼物价格
     */
    @ExcelProperty(value = "礼物价格")
    private Integer amount;

    /**
     * 排序字段
     */
    @ExcelProperty(value = "排序字段")
    private Integer sort;

    /**
     * 状态(0=生效，1=不生效)
     */
    @ExcelProperty(value = "状态(0=生效，1=不生效)")
    private String status;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


    /**
     * 折扣价格
     */
    private BigDecimal discountPrice;


}
