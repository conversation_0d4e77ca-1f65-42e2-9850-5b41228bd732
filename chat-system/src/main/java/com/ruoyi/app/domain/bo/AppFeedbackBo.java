package com.ruoyi.app.domain.bo;

import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;

import java.util.Date;

import com.ruoyi.common.core.domain.BaseEntity;
import org.hibernate.validator.constraints.Length;

/**
 * APP用户意见反馈业务对象 app_feedback
 *
 * <AUTHOR>
 * @date 2023-02-09
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class AppFeedbackBo extends BaseEntity {

    /**
     * ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 反馈类型
     */
    private String type;

    /**
     * 反馈内容
     */
    @NotBlank(message = "{user.email.not.blank}", groups = {AddGroup.class, EditGroup.class})
    private String context;

    /**
     * 反馈图片
     */
    private String image;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 备注
     */
    private String remark;


}
