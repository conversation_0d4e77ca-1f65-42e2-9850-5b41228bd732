package com.ruoyi.app.domain.bo;

import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * APP标签类型业务对象 app_label_type
 *
 * <AUTHOR>
 * @date 2025-01-09
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class AppLabelTypeBo extends BaseEntity {

    /**
     * ID
     */
    private Long id;

    /**
     * 标签类型名称
     */
    @NotBlank(message = "标签类型名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String name;

    /**
     * 标签类型图标
     */
    @NotBlank(message = "标签类型图标不能为空", groups = {AddGroup.class, EditGroup.class})
    private String image;

    /**
     * 标签选中图标
     */
    @NotBlank(message = "标签选中图标不能为空", groups = {AddGroup.class, EditGroup.class})
    private String choiceImage;


    /**
     * 标签类型图标
     */
    @NotBlank(message = "图标不能为空", groups = {AddGroup.class, EditGroup.class})
    private String blackImage;

    /**
     * 标签选中图标
     */
    @NotBlank(message = "图标不能为空", groups = {AddGroup.class, EditGroup.class})
    private String choiceBlackImage;

    /**
     * 排序字段
     */
    private Integer sort;

    /**
     * 状态(0=生效，1=不生效)
     */
    @NotBlank(message = "状态(0=生效，1=不生效)不能为空", groups = {AddGroup.class, EditGroup.class})
    private String status;

    /**
     * 备注
     */
    private String remark;


}
