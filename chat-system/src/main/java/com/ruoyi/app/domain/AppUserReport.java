package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * APP举报信息对象 app_user_report
 *
 * <AUTHOR>
 * @date 2023-01-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_user_report")
public class AppUserReport extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 举报用户和群组的ID
     */
    private Long reportId;
    /**
     * 举报内容
     */
    private String context;

    /**
     * 描述信息
     */
    private String info;

    /**
     * 举报图片
     */
    private String images;

    /**
     * 举报类型(0=群组,1=个人)
     */
    private String type;
    /**
     * 删除标志（0=存在,2=删除）
     */
    @TableLogic
    private String delFlag;
    /**
     * 帐号状态(0=已处理,1=未处理)
     */
    private String status;

}
