package com.ruoyi.app.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;


/**
 * APP用户标签喜欢视图对象 app_label_like
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
@Data
@ExcelIgnoreUnannotated
public class AppLabelLikeVo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 标签ID
     */
    @ExcelProperty(value = "标签ID")
    private Long labelId;

    /**
     * 标签类型ID
     */
    @ExcelProperty(value = "标签类型ID")
    private Long typeId;

    /**
     * 超级喜欢(0=是，1=否)
     */
    @ExcelProperty(value = "超级喜欢(0=是，1=否)")
    private String likeStatus;

    /**
     * 超级喜欢原因
     */
    @ExcelProperty(value = "超级喜欢原因")
    private String cause;

    /**
     * 状态(0=生效，1=不生效)
     */
    @ExcelProperty(value = "状态(0=生效，1=不生效)")
    private String status;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 标签信息
     */
    private AppLabelVo label;

    /**
     * 用户信息
     */
    private UserVo userVo;
}
