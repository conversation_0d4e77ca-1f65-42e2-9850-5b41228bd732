package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * APP用户第三方登录对象 app_third_user
 *
 * <AUTHOR>
 * @date 2023-01-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_third_user")
public class AppThirdUser extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 第三方Token
     */
    private String token;
    /**
     * 第三方类型
     */
    private String type;
    /**
     * 头像地址
     */
    private String image;
    /**
     * 删除标志（0=存在,2=删除）
     */
    @TableLogic
    private String delFlag;

}
