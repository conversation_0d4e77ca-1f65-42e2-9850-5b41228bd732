package com.ruoyi.app.domain.bo;

import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * APPVIP类型业务对象 app_vip_type
 *
 * <AUTHOR>
 * @date 2023-03-16
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class AppVipTypeBo extends BaseEntity {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 套餐名称
     */
    @NotBlank(message = "套餐名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String name;

    /**
     * VIP时间
     */
    @NotNull(message = "VIP时间不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer day;

    /**
     * 安卓价格
     */
    @NotNull(message = "安卓价格不能为空", groups = {AddGroup.class, EditGroup.class})
    private Double androidPrice;

    /**
     * 苹果价格
     */
    @NotNull(message = "苹果价格不能为空", groups = {AddGroup.class, EditGroup.class})
    private Double iosPrice;

    /**
     * 划线价格
     */
    private Double listPrice;

    /**
     * 苹果指定套餐名称
     */
    private String iosName;

    /**
     * 折扣
     */
    @NotNull(message = "折扣不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer discount;

    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空", groups = {AddGroup.class, EditGroup.class})
    private String remark;

    /**
     * 会员每日赠送的字符串
     */
    private Integer giveNumber;

    /**
     * 会员的礼物折扣
     */
    private BigDecimal giveDiscount;

    private String type;

}
