package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * APP标签类型对象 app_label_type
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_label_type")
public class AppLabelType extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 标签类型名称
     */
    private String name;
    /**
     * 标签类型图标
     */
    private String image;
    /**
     * 标签选择图标
     */
    private String choiceImage;

    /**
     * 标签类型图标
     */
    private String blackImage;

    /**
     * 标签选中图标
     */
    private String choiceBlackImage;

    /**
     * 排序字段
     */
    private Integer sort;
    /**
     * 状态(0=生效，1=不生效)
     */
    private String status;
    /**
     * 备注
     */
    private String remark;

}
