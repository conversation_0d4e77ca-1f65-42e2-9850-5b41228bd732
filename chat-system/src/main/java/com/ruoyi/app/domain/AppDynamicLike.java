package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP动态点赞对象 app_dynamic_like
 *
 * <AUTHOR>
 * @date 2025-01-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_dynamic_like")
public class AppDynamicLike extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 动态或者评论ID
     */
    private Long likeId;
    /**
     * 点赞用户ID
     */
    private Long userId;
    /**
     * 点赞类型(0=动态点赞,1=评论点赞)
     */
    private String type;
    /**
     * 删除标志（0=代表存在 2=代表删除）
     */
    @TableLogic
    private String delFlag;

    private Long emojiId;

}
