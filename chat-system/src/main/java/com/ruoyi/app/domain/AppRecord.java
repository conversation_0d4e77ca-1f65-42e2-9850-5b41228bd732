package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP用户通知记录对象 app_record
 *
 * <AUTHOR>
 * @date 2023-04-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_record")
public class AppRecord extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 记录ID
     */
    private Long recordId;
    /**
     * 评论点赞用户ID
     */
    private Long userId;
    /**
     * 记录发布人的用户ID
     */
    private Long recordUserId;

    /**
     * 评论ID
     */
    private Long commentId;

    /**
     * 类型1=点赞2=评论

     */
    private String type;
    /**
     * 已读状态(1=已读,0=未读)
     */
    private String readStatus;
    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

}
