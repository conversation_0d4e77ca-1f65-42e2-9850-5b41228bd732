package com.ruoyi.app.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;

import javax.validation.constraints.NotBlank;


/**
 * APP标签类型视图对象 app_label_type
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
@Data
@ExcelIgnoreUnannotated
public class AppLabelTypeVo {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 标签类型名称
     */
    @ExcelProperty(value = "标签类型名称")
    private String name;

    /**
     * 标签类型图标
     */
    @ExcelProperty(value = "标签类型图标")
    private String image;

    /**
     * 标签类型选中图标
     */
    @ExcelProperty(value = "标签类型选中图标")
    private String choiceImage;

    /**
     * 标签类型图标
     */
    private String blackImage;

    /**
     * 标签选中图标
     */
    private String choiceBlackImage;

    /**
     * 排序字段
     */
    @ExcelProperty(value = "排序字段")
    private Integer sort;

    /**
     * 状态(0=生效，1=不生效)
     */
    @ExcelProperty(value = "状态(0=生效，1=不生效)")
    private String status;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


}
