package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 动态表情对象 app_dynamic_emoji
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_dynamic_emoji")
public class AppDynamicEmoji extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 表情名称
     */
    private String emojiName;
    /**
     * 表情图片
     */
    private String emojiImage;
    /**
     * 表情动态
     */
    private String emojiSvgImage;

    private Integer sort;
    /**
     * 删除标志（0=存在,2=删除）
     */
    @TableLogic
    private String delFlag;
    /**
     * 备注
     */
    private String remark;

    /**
     * 状态(0=生效，1=不生效)
     */
    private String status;
}
