package com.ruoyi.app.domain.bo;

import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * APP动态视频信息业务对象 app_dynamic_video
 *
 * <AUTHOR>
 * @date 2025-01-11
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class AppDynamicVideoBo extends BaseEntity {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 动态ID
     */
    @NotNull(message = "动态ID不能为空", groups = {EditGroup.class})
    private Long dynamicId;

    /**
     * 视频地址
     */
    @NotBlank(message = "视频地址不能为空", groups = {AddGroup.class, EditGroup.class})
    private String url;

    /**
     * 视频图片/动态图片
     */
    @NotBlank(message = "视频图片/动态图片", groups = {AddGroup.class, EditGroup.class})
    private String image;

    /**
     * 视频/图片图片缩略图
     */
    @NotBlank(message = "视频/图片图片缩略图", groups = {AddGroup.class, EditGroup.class})
    private String smallImage;

    /**
     * 封面高度
     */
    @NotNull(message = "封面高度不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer height;

    /**
     * 封面宽度
     */
    @NotNull(message = "封面宽度不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer width;

    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空", groups = {AddGroup.class, EditGroup.class})
    private String remark;


}
