package com.ruoyi.app.domain.bo;

import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP礼物业务对象 app_gift
 *
 * <AUTHOR>
 * @date 2025-12-14
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class AppGiftBo extends BaseEntity {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 礼物名称
     */
    @NotBlank(message = "礼物名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 礼物图片地址
     */
    @NotBlank(message = "礼物图片不能为空", groups = { AddGroup.class, EditGroup.class })
    private String image;

    /**
     * 礼物特效
     */
    @NotBlank(message = "礼物特效不能为空", groups = { AddGroup.class, EditGroup.class })
    private String imageSvg;

    /**
     * 礼物价格
     */
    @NotNull(message = "礼物价格不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer amount;

    /**
     * 排序字段
     */
    private Integer sort;

    /**
     * 状态(0=生效，1=不生效)
     */
    @NotBlank(message = "状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;

    /**
     * 备注
     */
    private String remark;


}
