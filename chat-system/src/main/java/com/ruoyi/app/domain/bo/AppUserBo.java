package com.ruoyi.app.domain.bo;

import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * APP用户信息业务对象 app_user
 *
 * <AUTHOR>
 * @date 2025-12-08
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class AppUserBo extends BaseEntity {

    /**
     * 用户ID
     */
    private Long id;

    /**
     * 用户账号
     */
    @NotBlank(message = "用户账号不能为空", groups = {AddGroup.class, EditGroup.class})
    private String userName;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 用户邮箱
     */
    private String email;

    /**
     * 身份证号码
     */
    private String code;

    /**
     * 身份证姓名
     */
    private String codeName;

    /**
     * 手机号码
     */
    @NotBlank(message = "手机号码不能为空", groups = {AddGroup.class, EditGroup.class})
    private String phone;

    /**
     * 用户性别（0=男 1=女 2=未知）
     */
    @NotBlank(message = "用户性别（0=男 1=女 2=未知）不能为空", groups = {AddGroup.class, EditGroup.class})
    private String sex;

    /**
     * 邀请码
     */
    @NotBlank(message = "邀请码不能为空", groups = {AddGroup.class, EditGroup.class})
    private String inviteCode;

    /**
     * 父级ID
     */
    @NotNull(message = "父级ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long parentId;

    /**
     * 头像地址
     */
    private String avatar;

    /**
     * 环信uuid
     */
    private String uuid;

    /**
     * 生活照片地址
     */
    private String photo;

    /**
     * 个性签名
     */
    private String signature;

    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空", groups = {AddGroup.class, EditGroup.class})
    private String password;

    /**
     * 职业
     */
    @NotBlank(message = "职业不能为空", groups = {AddGroup.class, EditGroup.class})
    private String occupation;

    /**
     * 学校
     */
    @NotBlank(message = "学校不能为空", groups = {AddGroup.class, EditGroup.class})
    private String school;

    /**
     * 出生年月
     */
    @NotNull(message = "出生年月不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long birthday;

    /**
     * 身高(cm)
     */
    private String height;

    /**
     * 纬度
     */
    private Double latitude;
    /**
     * 经度
     */
    private Double longitude;

    /**
     * 帐号类型(0=用户注册，1=自动注册)
     */
    private String register;

    /**
     * 帐号状态（0=正常 1=冻结 ）
     */
    @NotBlank(message = "帐号状态（0=正常 1=冻结 ）不能为空", groups = {AddGroup.class, EditGroup.class})
    private String status;

    /**
     * 帐号状态（0=不在线 1=在线 ）
     */
    private String onlineStatus;

    /**
     * 最后登录IP
     */
    private String loginIp;

    /**
     * 最后登录时间
     */
    private Date loginDate;

    /**
     * 备注
     */
    private String remark;


    private Integer vipId;


    private Long vipExpireTime;

    /**
     * 用户语言
     */
    private String userLanguage;

    /**
     * 国家
     */
    private String userCountry;

    /**
     * 区号
     */
    private String areaCode;

    private List<Long> ids;
}
