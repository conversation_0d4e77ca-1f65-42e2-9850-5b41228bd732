package com.ruoyi.app.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ruoyi.common.annotation.ExcelDictFormat;
import com.ruoyi.common.convert.ExcelDictConvert;
import lombok.Data;
import java.util.Date;



/**
 * APP超级喜欢的标记视图对象 app_like_sign
 *
 * <AUTHOR>
 * @date 2025-02-22
 */
@Data
@ExcelIgnoreUnannotated
public class AppLikeSignVo {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 超级喜欢的用户ID
     */
    @ExcelProperty(value = "超级喜欢的用户ID")
    private Long sideId;

    /**
     * 已读状态(1=已读,0=未读)
     */
    @ExcelProperty(value = "已读状态(1=已读,0=未读)")
    private String status;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


}
