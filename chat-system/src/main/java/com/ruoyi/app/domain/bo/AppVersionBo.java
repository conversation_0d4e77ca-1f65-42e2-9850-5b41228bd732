package com.ruoyi.app.domain.bo;

import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * APP版本更新记录业务对象 app_version
 *
 * <AUTHOR>
 * @date 2023-04-11
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class AppVersionBo extends BaseEntity {

    /**
     * id
     */
    private Long id;

    /**
     * 标题
     */
    @NotBlank(message = "标题不能为空", groups = {AddGroup.class, EditGroup.class})
    private String title;

    /**
     * 更新内容
     */
    @NotBlank(message = "更新内容不能为空", groups = {AddGroup.class, EditGroup.class})
    private String context;

    /**
     * 苹果版本
     */
    @NotBlank(message = "苹果版本不能为空", groups = {AddGroup.class, EditGroup.class})
    private String iosVersion;

    /**
     * 安卓版本
     */
    @NotBlank(message = "安卓版本不能为空", groups = {AddGroup.class, EditGroup.class})
    private String androidVersion;

    /**
     * 版本号
     */
    private Integer versionCord;

    /**
     * 更新状态(0=不强制，1=强制)
     */
    @NotBlank(message = "更新状态不能为空", groups = {AddGroup.class, EditGroup.class})
    private String forceUpdate;

    /**
     * 备注
     */
    private String remark;


}
