package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP版本更新记录对象 app_version
 *
 * <AUTHOR>
 * @date 2023-04-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_version")
public class AppVersion extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 标题
     */
    private String title;
    /**
     * 更新内容
     */
    private String context;
    /**
     * 苹果版本
     */
    private String iosVersion;
    /**
     * 安卓版本名称
     */
    private String androidVersion;

    /**
     * 安卓版本号
     */
    private Integer versionCord;

    /**
     * 更新状态(0=不强制，1=强制)
     */
    private String forceUpdate;
    /**
     * 备注
     */
    private String remark;
    /**
     * 删除标志（0=存在,2=删除）
     */
    @TableLogic
    private String delFlag;

}
