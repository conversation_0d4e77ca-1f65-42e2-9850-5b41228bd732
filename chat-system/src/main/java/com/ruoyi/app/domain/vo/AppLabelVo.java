package com.ruoyi.app.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.List;


/**
 * APP标签视图对象 app_label
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
@Data
@ExcelIgnoreUnannotated
public class AppLabelVo {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;


    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 标签名称
     */
    @ExcelProperty(value = "标签名称")
    private String name;

    /**
     * 标签图标
     */
    @ExcelProperty(value = "标签图标")
    private String image;

    /**
     * 排序字段
     */
    @ExcelProperty(value = "排序字段")
    private Integer sort;

    /**
     * 标签类型ID
     */
    @ExcelProperty(value = "标签类型ID")
    private Long typeId;

    /**
     * 推荐状态(0=推荐，1=不推荐)
     */
    @ExcelProperty(value = "推荐状态(0=推荐，1=不推荐)")
    private String hotStatus;

    /**
     * 状态(0=生效，1=不生效)
     */
    @ExcelProperty(value = "状态(0=生效，1=不生效)")
    private String status;

    /**
     * 超级喜欢原因
     */
    private String cause;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 标签类型名称
     */
    private String typeName;

    /**
     * 新建标签的用户信息
     */
    private UserVo userVo;

    /**
     * 标签是否用户选择的标签
     */
    private Boolean check;

    /**
     * 喜欢该标签的用户列表
     */
    private List<UserVo> userLikeVos;

    /**
     * 超级喜欢该标签的用户列表
     */
    private List<UserVo> userLoveVos;

    /**
     * 当前用户是否加入了标签
     * true加入
     * false未加入
     */
    private Boolean isJoin;
}
