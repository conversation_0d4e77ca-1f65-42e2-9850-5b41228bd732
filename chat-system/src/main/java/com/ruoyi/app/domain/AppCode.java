package com.ruoyi.app.domain;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP瓶盖码对象 app_code
 *
 * <AUTHOR>
 * @date 2023-04-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_code")
@ExcelIgnoreUnannotated
public class AppCode extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(value = "id")
    private Long id;

    /**
     *
     */
    private String code;

    /**
     *
     */
    @ExcelProperty(value = "desCode")
    private String desCode;


    /**
     * 状态(0=未使用 1=已使用)
     */
    private Integer status;

    /**
     * 用户ID
     */
    private Long userId;


}
