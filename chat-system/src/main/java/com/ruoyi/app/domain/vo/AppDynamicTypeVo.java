package com.ruoyi.app.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ruoyi.common.annotation.ExcelDictFormat;
import com.ruoyi.common.convert.ExcelDictConvert;
import lombok.Data;

import java.util.Date;


/**
 * APP用户动态话题类型视图对象 app_dynamic_type
 *
 * <AUTHOR>
 * @date 2023-05-18
 */
@Data
@ExcelIgnoreUnannotated
public class AppDynamicTypeVo {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 动态标签名称
     */
    @ExcelProperty(value = "动态标签名称")
    private String name;

    /**
     * 动态标签描述
     */
    @ExcelProperty(value = "动态标签描述")
    private String introduce;

    /**
     * 动态标签图片
     */
    @ExcelProperty(value = "动态标签图片")
    private String image;

    /**
     * 动态标签类型(1=用户话题,0=系统话题,2=活动话题)
     */
    @ExcelProperty(value = "动态标签类型(1=用户话题,0=系统话题,2=活动话题)")
    private String type;

    /**
     * 话题热度
     */
    private Long heat;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    private String status;
}
