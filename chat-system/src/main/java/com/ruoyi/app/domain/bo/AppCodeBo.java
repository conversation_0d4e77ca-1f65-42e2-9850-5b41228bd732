package com.ruoyi.app.domain.bo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;

import java.util.Date;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP瓶盖码业务对象 app_code
 *
 * <AUTHOR>
 * @date 2023-04-03
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class AppCodeBo extends BaseEntity {


    private Long id;


    private String code;


    private String desCode;

    /**
     * 状态(0=未使用 1=已使用)
     */
    private Integer status;


    private Long userId;


}
