package com.ruoyi.app.domain.bo;

import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * APP标签业务对象 app_label
 *
 * <AUTHOR>
 * @date 2025-01-09
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class AppLabelBo extends BaseEntity {

    /**
     * ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 标签名称
     */
    @NotBlank(message = "标签名称不能为空", groups = {AddGroup.class, EditGroup.class})
    @Length(min = 2, max = 12, message = "标签名字超限")
    private String name;

    /**
     * 标签图片
     */
    @NotBlank(message = "标签图片不能为空", groups = {AddGroup.class, EditGroup.class})
    private String image;

    /**
     * 排序字段
     */
    private Integer sort;

    /**
     * 标签分类不能为空
     */
    @NotNull(message = "标签分类不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long typeId;

    /**
     * 推荐状态(0=推荐，1=不推荐)
     */
    private String hotStatus;

    /**
     * 状态(0=生效，1=不生效)
     */
    private String status;

    /**
     * 备注
     */
    private String remark;


}
