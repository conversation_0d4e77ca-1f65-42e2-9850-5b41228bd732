package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * App用户签到领取奖励对象 app_user_bag
 *
 * <AUTHOR>
 * @date 2023-03-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_user_bag")
public class AppUserBag extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 背包类型

     */
    private String type;
    /**
     * 背包数量

     */
    private Integer number;
    /**
     * 备注
     */
    private String remark;
    /**
     * 删除标志（0=存在,2=删除）
     */
    @TableLogic
    private String delFlag;

}
