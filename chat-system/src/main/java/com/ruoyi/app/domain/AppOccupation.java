package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP职业信息对象 app_occupation
 *
 * <AUTHOR>
 * @date 2025-12-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_occupation")
public class AppOccupation extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 职业名称
     */
    private String name;
    /**
     * 记录状态
     */
    private String status;
    /**
     * 排序字段
     */
    private Integer sort;

}
