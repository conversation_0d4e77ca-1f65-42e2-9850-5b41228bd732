package com.ruoyi.app.domain.bo;

import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;

import java.math.BigDecimal;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP用户账单明细业务对象 app_detail
 *
 * <AUTHOR>
 * @date 2025-08-26
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class AppDetailBo extends BaseEntity {

    /**
     * ID
     */
    private Long id;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 积分数量
     */
    @NotNull(message = "积分数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal balance;

    /**
     * 积分手续费
     */
    @NotNull(message = "积分手续费不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal freeBalance;

    /**
     * 账单类型
     */
    @NotBlank(message = "账单类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String type;

    /**
     * 类型(1=收入，2=支出)
     */
    @NotBlank(message = "类型(1=收入，2=支出)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String flow;

    /**
     * 金币类型(1=余额，2=积分)
     */
    @NotBlank(message = "金币类型(1=余额，2=积分)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String coinTyp;

    /**
     * 来源用户ID
     */
    @NotNull(message = "来源用户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long fromId;

    /**
     * 创建时间戳
     */
    @NotNull(message = "创建时间戳不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer insertTime;

    /**
     * 备注
     */
    private String remark;


}
