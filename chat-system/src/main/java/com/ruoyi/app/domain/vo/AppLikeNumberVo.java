package com.ruoyi.app.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ruoyi.common.annotation.ExcelDictFormat;
import com.ruoyi.common.convert.ExcelDictConvert;
import lombok.Data;
import java.util.Date;



/**
 * APP通知数量控制视图对象 app_like_number
 *
 * <AUTHOR>
 * @date 2023-03-04
 */
@Data
@ExcelIgnoreUnannotated
public class AppLikeNumberVo {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 记录ID
     */
    @ExcelProperty(value = "记录ID")
    private Long recordId;

    /**
     * 操作的类型
     */
    @ExcelProperty(value = "操作的类型")
    private String type;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


}
