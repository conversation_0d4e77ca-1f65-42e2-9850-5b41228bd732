package com.ruoyi.app.domain.vo;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ruoyi.common.annotation.ExcelDictFormat;
import com.ruoyi.common.convert.ExcelDictConvert;
import lombok.Data;
import java.util.Date;



/**
 * APP红包视图对象 app_hongbao
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Data
@ExcelIgnoreUnannotated
public class AppHongbaoVo {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long hongbaoId;

    /**
     * 红包名称
     */
    @ExcelProperty(value = "红包名称")
    private String hongbaoName;

    /**
     * 红包数量
     */
    @ExcelProperty(value = "红包数量")
    private Long hongbaoNumber;

    /**
     * 红包金额
     */
    @ExcelProperty(value = "红包金额")
    private BigDecimal hongbaoMoney;

    /**
     * 发送红包者
     */
    @ExcelProperty(value = "发送红包者")
    private Long userId;

    /**
     * 接收红包者
     */
    @ExcelProperty(value = "接收红包者")
    private Long sendUser;

    /**
     * 接收时间
     */
    @ExcelProperty(value = "接收时间")
    private Date receiptTime;

    /**
     * 状态(0=待领取，1=已领取，2=未领退回)
     */
    @ExcelProperty(value = "状态(0=待领取，1=已领取，2=未领退回)")
    private String status;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    private Date createTime;


    private UserFilterVo userVo;

    private UserFilterVo sendUserVo;


    private String userName;

    private String sendUserName;
}
