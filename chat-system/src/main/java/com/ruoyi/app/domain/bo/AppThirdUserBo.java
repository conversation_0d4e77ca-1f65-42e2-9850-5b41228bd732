package com.ruoyi.app.domain.bo;

import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * APP用户第三方登录业务对象 app_third_user
 *
 * <AUTHOR>
 * @date 2023-01-07
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class AppThirdUserBo extends BaseEntity {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 第三方Token
     */
    @NotBlank(message = "第三方Token不能为空", groups = { AddGroup.class, EditGroup.class })
    private String token;

    /**
     * 第三方类型
     */
    @NotBlank(message = "第三方类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String type;

    /**
     * 头像地址
     */
    @NotBlank(message = "头像地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String image;


}
