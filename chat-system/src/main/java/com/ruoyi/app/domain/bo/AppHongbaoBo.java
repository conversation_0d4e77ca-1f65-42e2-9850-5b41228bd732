package com.ruoyi.app.domain.bo;

import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP红包业务对象 app_hongbao
 *
 * <AUTHOR>
 * @date 2025-07-28
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class AppHongbaoBo extends BaseEntity {

    /**
     * ID
     */
    @NotBlank(message = "ID不能为空", groups = { EditGroup.class })
    private Long hongbaoId;

    /**
     * 红包名称
     */
    private String hongbaoName;

    /**
     * 红包数量
     */
    @NotNull(message = "红包数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long hongbaoNumber;

    /**
     * 红包金额
     */
    @NotNull(message = "红包金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal hongbaoMoney;

    /**
     * 发送红包者
     */
    private Long userId;

    /**
     * 接收红包者
     */
    @NotNull(message = "接收红包者不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long sendUser;

    /**
     * 接收时间
     */
    private Date receiptTime;

    /**
     * 状态(0=待领取，1=已领取，2=未领退回)
     */
    @NotBlank(message = "状态(0=待领取，1=已领取，2=未领退回)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;

    /**
     * 备注
     */
    private String remark;


}
