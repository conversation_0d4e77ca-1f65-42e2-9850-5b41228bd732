package com.ruoyi.app.domain.bo;

import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;

import java.util.Date;

import java.math.BigDecimal;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP用户信息设置业务对象 app_user_setting
 *
 * <AUTHOR>
 * @date 2025-02-02
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class AppUserSettingBo extends BaseEntity {

    /**
     * ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 年龄最小岁
     */
    @Min(value = 18, message = "年龄最少不能超过18岁")
    private Integer ageMin;

    /**
     * 年龄最大岁
     */
    @Max(value = 50, message = "年龄最大不能超过50")
    private Integer ageMax;

    /**
     * 用户性别筛选（0=男 1=女 2=不限）
     */
    private String sex;

    /**
     * 搜索距离
     */
    private Integer distance;

    /**
     * 只看同城(0=是,1=否)
     */
    private String city;

    /**
     * 纬度
     */
    private Double latitude;

    /**
     * 经度
     */
    private Double longitude;


}
