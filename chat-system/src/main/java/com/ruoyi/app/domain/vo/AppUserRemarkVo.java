package com.ruoyi.app.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ruoyi.common.annotation.ExcelDictFormat;
import com.ruoyi.common.convert.ExcelDictConvert;
import lombok.Data;
import java.util.Date;



/**
 * APP用户备注视图对象 app_user_remark
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
@ExcelIgnoreUnannotated
public class AppUserRemarkVo {

    private static final long serialVersionUID = 1L;

    /**
     * ID

     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 备注操作的用户
     */
    @ExcelProperty(value = "备注操作的用户")
    private Long friendId;

    /**
     * 备注内容
     */
    @ExcelProperty(value = "备注内容")
    private String remark;


}
