package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * APP用户动态对象 app_dynamic
 *
 * <AUTHOR>
 * @date 2025-12-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_dynamic")
public class AppDynamic extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 动态内容
     */
    private String context;
    /**
     * 动态图片，多个逗号分隔
     */
    private String images;
    /**
     * 选择位置
     */
    private String address;
    /**
     * 经度
     */
    private Double longitude;
    /**
     * 纬度
     */
    private Double latitude;
    /**
     * 状态(0=展示，1=不展示，2=待审核)
     */
    private String status;

    /**
     * 动态类型(1=图片文字，2=视频)
     */
    private String type;

    /**
     * 动态范围(1=所有人，2=自己，3=部分人,4=不给谁看)
     */
    private String scope;

    /**
     * 备注
     */
    private String remark;


    private String statusTop;


}
