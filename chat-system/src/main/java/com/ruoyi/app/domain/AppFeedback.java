package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP用户意见反馈对象 app_feedback
 *
 * <AUTHOR>
 * @date 2023-02-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_feedback")
public class AppFeedback extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 反馈类型
     */
    private String type;
    /**
     * 反馈内容
     */
    private String context;
    /**
     * 反馈图片
     */
    private String image;
    /**
     * 联系电话
     */
    private String phone;
    /**
     * 备注
     */
    private String remark;

}
