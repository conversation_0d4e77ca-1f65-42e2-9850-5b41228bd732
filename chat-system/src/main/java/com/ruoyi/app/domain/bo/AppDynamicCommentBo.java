package com.ruoyi.app.domain.bo;

import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * APP用户动态评论业务对象 app_dynamic_comment
 *
 * <AUTHOR>
 * @date 2022-12-23
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class AppDynamicCommentBo extends BaseEntity {

    /**
     * ID
     */
    private Long id;

    /**
     * 动态ID
     */
    @NotNull(message = "动态ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long dynamicId;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long userId;

    /**
     * 指定用户的评论
     */
    @NotNull(message = "指定用户的评论不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long toUserId;

    /**
     * 评论内容
     */
    @NotBlank(message = "评论内容不能为空", groups = {AddGroup.class, EditGroup.class})
    private String context;


}
