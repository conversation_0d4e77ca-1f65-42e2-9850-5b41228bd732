package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * APP用户的通讯录对象 app_address_book
 *
 * <AUTHOR>
 * @date 2022-12-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_address_book")
public class AppAddressBook extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 通讯录ID
     */
    private Long bookId;
    /**
     * 备注名称
     */
    private String remark;

    /**
     * 删除标志（0=代表存在 2=代表删除）
     */
    @TableLogic
    private String delFlag;

}
