package com.ruoyi.app.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.enums.DynamicType;
import lombok.Data;


/**
 * APP用户通知信息视图对象 app_notice
 *
 * <AUTHOR>
 * @date 2022-12-27
 */
@Data
@ExcelIgnoreUnannotated
public class AppNoticeVo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 标题
     */
    @ExcelProperty(value = "标题")
    private String title;

    /**
     * 内容
     */
    @ExcelProperty(value = "内容")
    private String content;

    /**
     * 追朔用户ID
     */
    private Long fromId;

    /**
     * 接收人
     */
    @ExcelProperty(value = "接收人")
    private Long userId;


    /**
     * 通知的背景图片
     */
    private String image;

    /**
     * 通知类型
     */
    private String type;

    /**
     * 记录的ID
     */
    private Long recordId;

    /**
     * 跳转的地址
     */
    private String url;

    /**
     * 已读状态(1=已读，0=未读)
     */
    @ExcelProperty(value = "已读状态(1=已读，0=未读)")
    private String statusRead;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 分类图片
     */
    private String typeImage;

    /**
     * 动态信息
      */
    private AppDynamicVo appDynamicVo;


    /**
     * 评论信息
     */
    private AppDynamicCommentVo appDynamicCommentVo;


    /**
     * 操作用户信息
     */
    private AppUserVo appUserVo;

    /**
     * 时间格式化
     */
    private String createTimeFormat;
}
