package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP用户备注对象 app_user_remark
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_user_remark")
public class AppUserRemark extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * ID

     */
    @TableId(value = "id")
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 备注操作的用户
     */
    private Long friendId;
    /**
     * 备注内容
     */
    private String remark;

}
