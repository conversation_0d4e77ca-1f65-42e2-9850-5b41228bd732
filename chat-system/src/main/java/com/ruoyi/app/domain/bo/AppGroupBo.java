package com.ruoyi.app.domain.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import com.ruoyi.common.core.validate.QueryGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;

import java.util.Date;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP群组业务对象 app_group
 *
 * <AUTHOR>
 * @date 2023-01-29
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class AppGroupBo extends BaseEntity {

    /**
     * ID
     */
    @NotNull(message = "群组ID不能为空!", groups = QueryGroup.class)
    private Long id;

    /**
     * 群组ID
     */
    private String groupId;

    /**
     * 群名称
     */
    private String name;

    /**
     * 群组图片
     */
    private String channelName;

    /**
     * 群公告
     */
    private String description;

    /**
     * 群类型(0=公开群，1=私有群)
     */
    @NotBlank(message = "群类型(0=公开群，1=私有群)不能为空", groups = {AddGroup.class, EditGroup.class})
    private String type = "0";

    /**
     * 是否允许群成员邀请别人加入此群(0=允许，1=不允许)只对私有群有效
     */
    @NotBlank(message = "是否允许群成员邀请别人加入此群(0=允许，1=不允许)只对私有群有效不能为空", groups = {AddGroup.class, EditGroup.class})
    private String allowinvites = "1";

    /**
     * 用户申请入群是否需要群主或者群管理员审批(0=不需要，1=需要)
     */
    @NotBlank(message = "用户申请入群是否需要群主或者群管理员审批(0=不需要，1=需要)不能为空", groups = {AddGroup.class, EditGroup.class})
    private String membersonly = "0";

    /**
     * 邀请用户入群时是否需要被邀用户同意(0=不需要，1=需要)
     */
    @NotBlank(message = "邀请用户入群时是否需要被邀用户同意(0=不需要，1=需要)不能为空", groups = {AddGroup.class, EditGroup.class})
    private String inviteNeedConfirm = "0";

    /**
     * 群组状态(0=正常，1=封禁)
     */
    private String disabled;

    /**
     * 群扩展信息
     */
    private String custom;

    /**
     * 全局禁言(0=未禁言，1=禁言)
     */
    private String muteAll;

    /**
     * 备注
     */
    private String remark;

    /**
     * 成员数组
     */
    @NotBlank(message = "成员不能为空!")
    private String ids;


    /**
     * 发送群组的消息内容
     */
    private String context;
}
