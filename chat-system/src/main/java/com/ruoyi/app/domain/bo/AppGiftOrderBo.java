package com.ruoyi.app.domain.bo;

import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.math.BigDecimal;
import java.util.Date;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP礼物记录业务对象 app_gift_order
 *
 * <AUTHOR>
 * @date 2025-12-21
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class AppGiftOrderBo extends BaseEntity {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 赠送用户ID
     */
    @NotNull(message = "赠送用户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long toUserId;

    /**
     * 礼物名称
     */
    @NotBlank(message = "礼物名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 礼物ID
     */
    @NotNull(message = "礼物ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long giftId;

    /**
     * 礼物价格
     */
    @NotNull(message = "礼物价格不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal amount;

    /**
     * 礼物数量
     */
    private Integer number;

    /**
     * 1=礼物
     */
    @NotNull(message = "1=礼物不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer type;


    /**
     * 备注
     */
    private String remark;


}
