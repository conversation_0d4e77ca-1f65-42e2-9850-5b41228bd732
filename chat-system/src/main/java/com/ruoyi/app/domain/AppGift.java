package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP礼物对象 app_gift
 *
 * <AUTHOR>
 * @date 2025-12-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_gift")
public class AppGift extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 礼物名称
     */
    private String name;
    /**
     * 礼物图片地址
     */
    private String image;
    /**
     * 礼物特效
     */
    private String imageSvg;
    /**
     * 礼物价格
     */
    private Integer amount;
    /**
     * 排序字段
     */
    private Integer sort;
    /**
     * 状态(0=生效，1=不生效)
     */
    private String status;
    /**
     * 备注
     */
    private String remark;

}
