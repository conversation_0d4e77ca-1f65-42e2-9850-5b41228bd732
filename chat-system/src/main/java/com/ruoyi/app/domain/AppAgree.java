package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP时光约会对象 app_agree
 *
 * <AUTHOR>
 * @date 2025-03-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_agree")
public class AppAgree extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 发出邀请用户的ID
     */
    private Long userId;

    private String userName;

    /**
     * 被邀请的用户ID
     */
    private Long sideId;

    private String sideName;

    /**
     * 约会的时间
     */
    private Long agreeTime;
    /**
     * 状态(0=通过，1=拒绝，2=申请中，3=超时)
     */
    private String status;
    /**
     * 备注
     */
    private String remark;
    /**
     * 删除标志（0=存在,2=删除）
     */
    @TableLogic
    private String delFlag;

}
