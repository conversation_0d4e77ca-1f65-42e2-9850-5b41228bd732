package com.ruoyi.app.domain.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;

import java.util.Date;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP用户喜欢记录业务对象 app_like_notes
 *
 * <AUTHOR>
 * @date 2025-02-14
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class AppLikeNotesBo extends BaseEntity {

    /**
     * ID
     */
    private Long id;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long userId;

    /**
     * 操作的用户ID
     */
    @NotNull(message = "操作的用户ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long sideId;

    /**
     * 状态(0=喜欢，2=不喜欢)
     */
    @NotBlank(message = "状态不能为空", groups = {AddGroup.class, EditGroup.class})
    private String status;

    /**
     * 喜欢状态(0=喜欢，1=超级喜欢)
     */
    @ExcelProperty(value = "状态(0=喜欢，1=超级喜欢)")
    private String likeStatus;

    /**
     * 已读状态(1=已读,0=未读)
     */
    private String readStatus;

    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空", groups = {AddGroup.class, EditGroup.class})
    private String remark;

}
