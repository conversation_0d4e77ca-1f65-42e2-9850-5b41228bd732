package com.ruoyi.app.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ruoyi.common.annotation.ExcelDictFormat;
import com.ruoyi.common.convert.ExcelDictConvert;
import lombok.Data;
import java.util.Date;



/**
 * 活动展馆管理视图对象 app_show
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
@Data
@ExcelIgnoreUnannotated
public class AppShowVo {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 活动标题
     */
    @ExcelProperty(value = "活动标题")
    private String title;

    /**
     * 活动内容
     */
    @ExcelProperty(value = "活动内容")
    private String context;

    /**
     * 活动图片
     */
    @ExcelProperty(value = "活动图片")
    private String images;

    /**
     * 描述
     */
    @ExcelProperty(value = "描述")
    private String remark;


}
