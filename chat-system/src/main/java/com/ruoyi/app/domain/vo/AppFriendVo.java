package com.ruoyi.app.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;


/**
 * APP用户关注视图对象 app_friend
 *
 * <AUTHOR>
 * @date 2025-01-04
 */
@Data
@ExcelIgnoreUnannotated
public class AppFriendVo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 关注用户ID
     */
    @ExcelProperty(value = "关注用户ID")
    private Long friendId;


    /**
     * 来源状态（0=关注,1=喜欢）
     */
    private String fromStatus;

    /**
     * 已读状态(1=已读,0=未读)
     */
    private String status;

    /**
     * 关注的用户信息
     */
    private UserResultVo user;

    /**
     * 关注时间格式化
     */
    private String createTimeFormat;


    /**
     * 是否置顶(0=不置顶，1=置顶)
     */
    private String topping;

    /**
     * 别名
     */
    private String remark;
}
