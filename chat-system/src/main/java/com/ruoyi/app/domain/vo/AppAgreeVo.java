package com.ruoyi.app.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ruoyi.common.annotation.ExcelDictFormat;
import com.ruoyi.common.convert.ExcelDictConvert;
import lombok.Data;

import java.util.Date;


/**
 * APP时光约会视图对象 app_agree
 *
 * <AUTHOR>
 * @date 2024-03-25
 */
@Data
@ExcelIgnoreUnannotated
public class AppAgreeVo {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 发出邀请用户的ID
     */
    @ExcelProperty(value = "发出邀请用户的ID")
    private Long userId;

    /**
     * 邀请用户昵称
     */
    private String userName;

    private String userAvatar;

    /**
     * 被邀请的用户ID
     */
    @ExcelProperty(value = "被邀请的用户ID")
    private Long sideId;

    /**
     * 邀请用户昵称
     */
    private String sideName;

    private String sideAvatar;

    /**
     * 约会的时间
     */
    @ExcelProperty(value = "约会的时间")
    private Long agreeTime;


    /**
     * 约会的时间
     */
    @ExcelProperty(value = "约会时间格式化")
    private String agreeTimeString;

    /**
     * 状态(0=通过，1=拒绝，2=申请中，3=超时)
     */
    @ExcelProperty(value = "状态(0=通过，1=拒绝，2=申请中，3=超时)")
    private String status;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


}
