package com.ruoyi.app.domain.bo;

import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP动态点赞业务对象 app_dynamic_like
 *
 * <AUTHOR>
 * @date 2023-01-16
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class AppDynamicLikeBo extends BaseEntity {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 动态或者评论ID
     */
    @NotNull(message = "动态或者评论ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long likeId;

    /**
     * 点赞用户ID
     */
    @NotNull(message = "点赞用户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 点赞类型(0=动态点赞,1=评论点赞)
     */
    @NotBlank(message = "点赞类型(0=动态点赞,1=评论点赞)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String type;


}
