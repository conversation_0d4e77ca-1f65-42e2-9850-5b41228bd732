package com.ruoyi.app.domain.bo;

import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * APP学校信息业务对象 app_school
 *
 * <AUTHOR>
 * @date 2023-01-28
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class AppSchoolBo extends BaseEntity {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 学校名称
     */
    @NotBlank(message = "学校名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 状态(0=生效，1=不生效)
     */
    @NotBlank(message = "状态(0=生效，1=不生效)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;


}
