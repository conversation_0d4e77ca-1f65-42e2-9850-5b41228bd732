package com.ruoyi.app.domain.bo;

import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;

import java.math.BigDecimal;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP用户资产信息业务对象 app_asset
 *
 * <AUTHOR>
 * @date 2022-12-09
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class AppAssetBo extends BaseEntity {

    /**
     * ID

     */
    @NotNull(message = "ID 不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 用户ID
     */
    @NotBlank(message = "用户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 用户资产
     */
    @NotNull(message = "用户资产不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal balance;

    /**
     * 用户积分
     */
    @NotNull(message = "用户积分不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal integral;

    /**
     * 帐号状态(0=正常,1=停用)
     */
    @NotBlank(message = "帐号状态(0=正常,1=停用)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;


}
