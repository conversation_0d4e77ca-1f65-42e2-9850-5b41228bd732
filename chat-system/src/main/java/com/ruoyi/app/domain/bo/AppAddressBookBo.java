package com.ruoyi.app.domain.bo;

import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * APP用户的通讯录业务对象 app_address_book
 *
 * <AUTHOR>
 * @date 2022-12-26
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class AppAddressBookBo extends BaseEntity {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 通讯录ID
     */
    @NotNull(message = "通讯录ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long bookId;

    /**
     * 备注名称
     */
    @NotBlank(message = "备注名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remark;


}
