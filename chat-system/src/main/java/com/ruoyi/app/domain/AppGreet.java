package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP打招呼记录
 * 对象 app_greet
 *
 * <AUTHOR>
 * @date 2023-03-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_greet")
public class AppGreet extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 对方ID
     */
    private Long fromId;
    /**
     * 发送数量
     */
    private Integer number;

    /**
     * 发送内容
     */
    private String context;

    /**
     * 记录状态
     */
    private String status;

    /**
     * 备注
     */
    private String remark;
    /**
     * 删除标志（0=存在,2=删除）
     */
    @TableLogic
    private String delFlag;

}
