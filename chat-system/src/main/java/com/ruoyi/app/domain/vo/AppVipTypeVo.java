package com.ruoyi.app.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ruoyi.common.annotation.ExcelDictFormat;
import com.ruoyi.common.convert.ExcelDictConvert;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;


/**
 * APPVIP类型视图对象 app_vip_type
 *
 * <AUTHOR>
 * @date 2025-03-16
 */
@Data
@ExcelIgnoreUnannotated
public class AppVipTypeVo {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 套餐名称
     */
    @ExcelProperty(value = "套餐名称")
    private String name;

    /**
     * VIP时间
     */
    @ExcelProperty(value = "VIP时间")
    private Integer day;

    /**
     * 安卓价格
     */
    @ExcelProperty(value = "安卓价格")
    private Double androidPrice;

    /**
     * 苹果价格
     */
    @ExcelProperty(value = "苹果价格")
    private Double iosPrice;

    /**
     * 划线价格
     */
    @ExcelProperty(value = "划线价格")
    private Double listPrice;

    /**
     * 苹果指定套餐名称
     */
    @ExcelProperty(value = "苹果指定套餐名称")
    private String iosName;

    /**
     * 赠送时间
     */
    @ExcelProperty(value = "赠送时间")
    private Integer discount;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 会员每日赠送的字符串
     */
    private Integer giveNumber;

    /**
     * 会员的礼物折扣
     */
    private BigDecimal giveDiscount;


    private String type;
}
