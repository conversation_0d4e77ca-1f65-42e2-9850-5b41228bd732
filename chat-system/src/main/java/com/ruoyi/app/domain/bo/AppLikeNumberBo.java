package com.ruoyi.app.domain.bo;

import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * APP通知数量控制业务对象 app_like_number
 *
 * <AUTHOR>
 * @date 2025-03-04
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class AppLikeNumberBo extends BaseEntity {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long userId;

    /**
     * 记录ID
     */
    @NotNull(message = "记录ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long recordId;

    /**
     * 操作的类型
     */
    @NotBlank(message = "操作的类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private String type;

    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空", groups = {AddGroup.class, EditGroup.class})
    private String remark;


}
