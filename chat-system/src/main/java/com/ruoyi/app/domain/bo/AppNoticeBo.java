package com.ruoyi.app.domain.bo;

import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * APP用户通知信息业务对象 app_notice
 *
 * <AUTHOR>
 * @date 2025-12-27
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class AppNoticeBo extends BaseEntity {

    /**
     * ID
     */
    private Long id;

    /**
     * 标题
     */
    @NotBlank(message = "标题不能为空", groups = {AddGroup.class, EditGroup.class})
    private String title;

    /**
     * 内容
     */
    @NotBlank(message = "内容不能为空", groups = {AddGroup.class, EditGroup.class})
    private String content;

    /**
     * 通知的背景图片
     */
    private String image;


    /**
     * 发送人
     */
    @NotNull(message = "发送人不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long userId;

    /**
     * 追朔用户ID
     */
    private Long fromId;

    /**
     * 通知类型 5=收藏，4=点赞 ，3=@别人，2=评论，1=回复
     */
    private String type;

    /**
     * 记录的ID
     */
    private Long recordId;

    /**
     * 跳转的地址
     */
    private String url;

    /**
     * 已读状态(1=已读，0=未读)
     */
    @NotBlank(message = "已读状态(1=已读，0=未读)不能为空", groups = {AddGroup.class, EditGroup.class})
    private String statusRead;

    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空", groups = {AddGroup.class, EditGroup.class})
    private String remark;


}
