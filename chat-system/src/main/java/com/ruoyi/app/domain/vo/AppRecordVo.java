package com.ruoyi.app.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ruoyi.common.annotation.ExcelDictFormat;
import com.ruoyi.common.convert.ExcelDictConvert;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.Date;


/**
 * APP用户通知记录视图对象 app_record
 *
 * <AUTHOR>
 * @date 2025-04-27
 */
@Data
@ExcelIgnoreUnannotated
public class AppRecordVo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 记录ID
     */
    @ExcelProperty(value = "记录ID")
    private Long recordId;

    /**
     * 评论点赞用户ID
     */
    @ExcelProperty(value = "评论点赞用户ID")
    private Long userId;

    /**
     * 记录发布人的用户ID
     */
    @ExcelProperty(value = "记录发布人的用户ID")
    private Long recordUserId;

    /**
     * 类型1=点赞2=评论
     */
    @ExcelProperty(value = "类型1=点赞2=评论")
    private String type;

    /**
     * 已读状态(1=已读,0=未读)
     */
    @ExcelProperty(value = "已读状态(1=已读,0=未读)")
    private String readStatus;

    /**
     * 通知的内容
     */
    private String context;


    private AppDynamicVo appDynamicVo;

    /**
     * 点赞的用户信息
     */
    private UserFilterVo userVo;
}
