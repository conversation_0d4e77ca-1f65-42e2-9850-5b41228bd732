package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * AP用户等级对象 app_level
 *
 * <AUTHOR>
 * @date 2024-09-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_level")
public class AppLevel extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 等级名称
     */
    private String name;
    /**
     * 积分小值
     */
    private Integer activeMin;
    /**
     * 积分大值
     */
    private Integer activeMax;
    /**
     * 备注
     */
    private String remark;

}
