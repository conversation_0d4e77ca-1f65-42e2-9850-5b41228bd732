package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import java.math.BigDecimal;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP用户信息设置对象 app_user_setting
 *
 * <AUTHOR>
 * @date 2023-02-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_user_setting")
public class AppUserSetting extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 年龄最小岁
     */
    private Integer ageMin;
    /**
     * 年龄最大岁
     */
    private Integer ageMax;
    /**
     * 用户性别筛选（0=男 1=女 2=未知）
     */
    private String sex;
    /**
     * 搜索距离
     */
    private Integer distance;
    /**
     * 只看同城(0=是,1=否)
     */
    private String city;
    /**
     * 纬度
     */
    private Double latitude;
    /**
     * 经度
     */
    private Double longitude;
    /**
     * 删除标志（0=代表存在 2=代表删除）
     */
    @TableLogic
    private String delFlag;

}
