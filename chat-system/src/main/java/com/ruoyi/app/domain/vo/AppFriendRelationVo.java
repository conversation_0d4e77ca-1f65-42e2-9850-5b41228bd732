package com.ruoyi.app.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ruoyi.common.annotation.ExcelDictFormat;
import com.ruoyi.common.convert.ExcelDictConvert;
import lombok.Data;
import java.util.Date;



/**
 * APP用户关系视图对象 app_friend_relation
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Data
@ExcelIgnoreUnannotated
public class AppFriendRelationVo {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long relationId;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 关注用户ID
     */
    @ExcelProperty(value = "关注用户ID")
    private Long toUserId;

    /**
     * 关注来源(0=收藏，1=足迹，2=点赞)
     */
    @ExcelProperty(value = "关注来源(0=收藏，1=足迹，2=点赞)")
    private String type;

    /**
     * 已读状态(1=已读,0=未读)
     */
    @ExcelProperty(value = "已读状态(1=已读,0=未读)")
    private String status;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


}
