package com.ruoyi.app.domain.bo;

import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * APP用户关注业务对象 app_friend
 *
 * <AUTHOR>
 * @date 2025-01-04
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class AppFriendBo extends BaseEntity {

    /**
     * ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 关注用户ID
     */
    @NotNull(message = "关注用户ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long friendId;

    /**
     * 来源状态（0=关注）
     */
    private String fromStatus = "0";

    /**
     * 已读状态(1=已读,0=未读)
     */
    private String status;

    /**
     * 是否置顶(0=不置顶，1=置顶)
     */
    private String topping;

    /**
     * 别名
     */
    private String remark;


    /**
     * 搜索的名字
     */
    private String name;


    private List<Long> friendIds;

}
