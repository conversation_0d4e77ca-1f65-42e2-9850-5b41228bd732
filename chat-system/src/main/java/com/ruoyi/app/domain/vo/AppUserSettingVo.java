package com.ruoyi.app.domain.vo;

import java.math.BigDecimal;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ruoyi.common.annotation.ExcelDictFormat;
import com.ruoyi.common.convert.ExcelDictConvert;
import lombok.Data;
import java.util.Date;



/**
 * APP用户信息设置视图对象 app_user_setting
 *
 * <AUTHOR>
 * @date 2025-02-02
 */
@Data
@ExcelIgnoreUnannotated
public class AppUserSettingVo {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 年龄最小岁
     */
    @ExcelProperty(value = "年龄最小岁")
    private Integer ageMin;

    /**
     * 年龄最大岁
     */
    @ExcelProperty(value = "年龄最大岁")
    private Integer ageMax;

    /**
     * 用户性别筛选（0=男 1=女 2=不限）
     */
    @ExcelProperty(value = "用户性别筛选", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=男,1=女,2=未知")
    private String sex;

    /**
     * 搜索距离
     */
    @ExcelProperty(value = "搜索距离")
    private Integer distance;

    /**
     * 只看同城(0=是,1=否)
     */
    @ExcelProperty(value = "只看同城(0=是,1=否)")
    private String city;

    /**
     * 纬度
     */
    @ExcelProperty(value = "纬度")
    private Double latitude;

    /**
     * 经度
     */
    @ExcelProperty(value = "经度")
    private Double longitude;


}
