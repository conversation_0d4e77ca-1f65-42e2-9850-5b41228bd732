package com.ruoyi.app.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ruoyi.common.annotation.ExcelDictFormat;
import com.ruoyi.common.convert.ExcelDictConvert;
import lombok.Data;

import java.util.Date;
import java.util.List;


/**
 * APP用户信息视图对象 app_user
 *
 * <AUTHOR>
 * @date 2025-12-08
 */
@Data
@ExcelIgnoreUnannotated
public class UserResultVo {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private Long id;

    /**
     * 腾讯uuid
     */
    @ExcelProperty(value = "环信uuid")
    private String uuid;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 用户性别（0=男 1=女 2=未知）
     */
    @ExcelProperty(value = "用户性别", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0==男,1==女,2==未知")
    private String sex;

    /**
     * 头像地址
     */
    @ExcelProperty(value = "头像地址")
    private String avatar;

    /**
     * 背景图片
     */
    @ExcelProperty(value = "背景图片")
    private String photo;


    /**
     * 出生年月
     */
    @ExcelProperty(value = "出生年月")
    private Long birthday;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 星座
     */
    private String constellation;

    /**
     * 身高(cm)
     */
    @ExcelProperty(value = "身高(cm)")
    private Integer height;

    /**
     * 个性签名
     */
    private String signature;


    private String status;

    /**
     * 帐号状态（0=不在线 1=在线 ）
     */
    private String onlineStatus;


    /**
     * 家乡
     */
    private String home;

    /**
     * 居住地省份
     */
    private String province;

    /**
     * 居住地城市
     */
    private String city;

    /**
     * 居住地
     */
    private String habitation;

    /**
     * 注册时间
     */
    private Date createTime;

    /**
     * 最后登录时间
     */
    private Date loginDate;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 喜欢我的粉丝数量
     */
    private Integer fans;

    /**
     * 我喜欢的关注数量
     */
    private Integer likes;

    /**
     * 我的动态图片
     */
    private List<AppDynamicVideoVo> dynamicVos;


    /**
     * 用户喜欢标签信息
     */
    private List<AppLabelLikeVo> likeLabels;


    /**
     * 群组身份信息
     */
    private AppGroupUserVo appGroupUserVo;

    /**
     * 我和对方用户的关系("0"="未关注"，"1"=已关注，"2"=好友)
     */
    private String isRelation;


    /**
     * 距离附近用户的距离
     */
    private Integer distance;

    /**
     * 距离附近用户的距离文本
     */
    private String distanceText;

    /**
     * 居住地省份名称
     */
    private String provinceName;

    /**
     * 居住地城市名称
     */
    private String cityName;

    /**
     * 纬度
     */
    private Double latitude;
    /**
     * 经度
     */
    private Double longitude;

    /**
     * 排序时间
     */
    private Date sortTime;

    /**
     * 用户的积分
     */
    private Long integral;

    /**
     * 用户VIP信息
     */
    private AppUserVipVo vipVo;
}
