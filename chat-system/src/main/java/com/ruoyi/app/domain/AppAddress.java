package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * APP用户地址信息对象 app_address
 *
 * <AUTHOR>
 * @date 2022-12-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_address")
public class AppAddress extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 收货姓名
     */
    private String nickName;
    /**
     * 收货电话
     */
    private String mobile;
    /**
     * 收货城市(省市区)
     */
    private String city;
    /**
     * 城市代码
     */
    private String cityCode;
    /**
     * 详细地址
     */
    private String cityDetails;

    /**
     * 地址是否默认(1=否，0=是)
     */
    private String status;

}
