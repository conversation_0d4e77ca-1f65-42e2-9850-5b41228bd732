package com.ruoyi.app.domain.bo;

import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;

import java.util.Date;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP用户匹配配置和记录业务对象 app_mate
 *
 * <AUTHOR>
 * @date 2023-05-16
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class AppMateBo extends BaseEntity {

    /**
     * ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 是否匹配在线
     */
    @NotBlank(message = "是否匹配在线不能为空", groups = {AddGroup.class, EditGroup.class})
    private String onLine;

    /**
     * 是否使用匹配功能
     */
    @NotBlank(message = "是否使用匹配功能不能为空", groups = {AddGroup.class, EditGroup.class})
    private String useMate;

    /**
     * 是否距离最近
     */
    @NotBlank(message = "是否距离最近不能为空", groups = {AddGroup.class, EditGroup.class})
    private String distance;

    /**
     * 是否信息完善
     */
    @NotBlank(message = "是否信息完善不能为空", groups = {AddGroup.class, EditGroup.class})
    private String infoComplete;

    /**
     * 是否提示
     */
    @NotBlank(message = "是否提示用户功能不能为空", groups = {AddGroup.class, EditGroup.class})
    private String remind;

    /**
     * 操作类型(1=匹配)
     */
    private String type;

    /**
     * 操作时间
     */
    private Long recordTime;


}
