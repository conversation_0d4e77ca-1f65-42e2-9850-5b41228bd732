package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * APP用户信息对象 app_user
 *
 * <AUTHOR>
 * @date 2025-12-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_user")
public class AppUser extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 用户账号
     */
    private String userName;
    /**
     * 用户昵称
     */
    private String nickName;
    /**
     * 用户邮箱
     */
    private String email;
    /**
     * 手机号码
     */
    private String phone;

    /**
     * 用户签名
     */
    private String signature;

    /**
     * 身份证号码
     */
    private String code;

    /**
     * 身份证姓名
     */
    private String codeName;

    /**
     * 环信UUID
     */
    private String uuid;

    /**
     * 用户性别（0=男 1=女 2=未知）
     */
    private String sex;
    /**
     * 邀请码
     */
    private String inviteCode;
    /**
     * 父级ID
     */
    private Long parentId;
    /**
     * 头像地址
     */
    private String avatar;
    /**
     * 生活照地址
     */
    private String photo;
    /**
     * 密码
     */
    private String password;
    /**
     * 职业
     */
    private String occupation;
    /**
     * 学校
     */
    private String school;
    /**
     * 出生年月
     */
    private Long birthday;
    /**
     * 身高(cm)
     */
    private Integer height;
    /**
     * 纬度
     */
    private Double latitude;
    /**
     * 经度
     */
    private Double longitude;

    /**
     * 帐号类型(0=用户注册，1=自动注册)
     */
    private String register;

    /**
     * 帐号状态（0=正常 1=冻结 ）
     */
    private String status;

    /**
     * 帐号状态（0=不在线 1=在线 ）
     */
    private String onlineStatus;

    /**
     * 删除标志（0=代表存在 2=代表删除）
     */
    @TableLogic
    private String delFlag;
    /**
     * 最后登录IP
     */
    private String loginIp;
    /**
     * 最后登录时间
     */
    private Date loginDate;
    /**
     * 备注
     */
    private String remark;

    /**
     * 用户语言
     */
    private String userLanguage;

    /**
     * 国家
     */
    private String userCountry;

    /**
     * 区号
     */
    private String areaCode;
}
