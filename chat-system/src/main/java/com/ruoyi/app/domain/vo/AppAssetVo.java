package com.ruoyi.app.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;


/**
 * APP用户资产信息视图对象 app_asset
 *
 * <AUTHOR>
 * @date 2022-12-09
 */
@Data
@ExcelIgnoreUnannotated
public class AppAssetVo {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 用户资产
     */
    @ExcelProperty(value = "用户资产")
    private BigDecimal balance;

    /**
     * 用户积分
     */
    @ExcelProperty(value = "用户积分")
    private BigDecimal integral;

    /**
     * 帐号状态(0=正常,1=停用)
     */
    @ExcelProperty(value = "帐号状态(0=正常,1=停用)")
    private String status;

    /**
     * 用户信息
     */
    private UserVo userVo;

}
