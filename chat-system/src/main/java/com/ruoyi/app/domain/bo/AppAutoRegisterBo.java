package com.ruoyi.app.domain.bo;

import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * APP用户信息业务对象 app_user
 *
 * <AUTHOR>
 * @date 2025-12-08
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class AppAutoRegisterBo extends BaseEntity {

    /**
     * 注册账号数量
     */
    private Integer number;

    /**
     * 年级最小值
     */
    private Integer ageMin;

    /**
     * 年级最大值
     */
    private Integer ageMax;

    /**
     * 用户性别
     */
    private String sex;

    /**
     * 身高最小值
     */
    private Integer heightMin;

    /**
     * 身高最大值
     */
    private Integer heightMax;

    /**
     * 纬度
     */
    private Double latitude;

    /**
     * 经度
     */
    private Double longitude;

    /**
     * 图片类型
     */
    private String imageType;


}
