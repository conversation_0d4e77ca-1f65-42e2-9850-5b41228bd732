package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * APP用户标签喜欢对象 app_label_like
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_label_like")
public class AppLabelLike extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 标签ID
     */
    private Long labelId;
    /**
     * 标签类型ID
     */
    private Long typeId;
    /**
     * 超级喜欢(0=是，1=否)
     */
    private String likeStatus;

    /**
     * 超级喜欢理由
     */
    private String cause;

    /**
     * 状态(0=生效，1=不生效)
     */
    private String status;
    /**
     * 备注
     */
    private String remark;
    /**
     * 删除标志（0=存在,2=删除）
     */
    @TableLogic
    private String delFlag;

}
