package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * APP群组对象 app_group_user
 *
 * <AUTHOR>
 * @date 2023-01-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_group_user")
public class AppGroupUser extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 群组记录ID
     */
    private Long groupId;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 群内用户昵称
     */
    private String nikeName;
    /**
     * 群组描述
     */
    private String groupName;
    /**
     * 是否显示群昵称
     */
    private String groupShow;
    /**
     * 群组身份(1=群主，2=管理员，3=群成员)
     */
    private String place;
    /**
     * 状态(0=正常，1=禁言)
     */
    private String status;
    /**
     * 对讲状态(0=正常，1=勿扰)
     */
    private String talkStatus;
    /**
     * 删除标志（0=存在,2=删除）
     */
    @TableLogic
    private String delFlag;
    /**
     * 备注
     */
    private String remark;

}
