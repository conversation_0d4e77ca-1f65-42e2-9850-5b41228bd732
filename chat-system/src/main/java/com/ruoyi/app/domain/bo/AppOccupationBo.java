package com.ruoyi.app.domain.bo;

import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP职业信息业务对象 app_occupation
 *
 * <AUTHOR>
 * @date 2022-12-12
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class AppOccupationBo extends BaseEntity {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 职业名称
     */
    @NotBlank(message = "职业名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 记录状态
     */
    @NotBlank(message = "记录状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;

    /**
     * 排序字段
     */
    @NotNull(message = "排序字段不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer sort;


}
