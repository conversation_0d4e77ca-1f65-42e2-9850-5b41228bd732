package com.ruoyi.app.domain.bo;

import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

/**
 * APP用户动态业务对象 app_dynamic
 *
 * <AUTHOR>
 * @date 2025-12-22
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class AppDynamicBo extends BaseEntity {

    /**
     * ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 动态内容
     */
    @NotBlank(message = "动态内容不能为空", groups = {AddGroup.class, EditGroup.class})
    private String context;

    /**
     * 动态图片，多个图片分隔
     */
    @NotBlank(message = "动态图片，多个图片逗号分隔", groups = {AddGroup.class, EditGroup.class})
    private String images;

    /**
     * 选择位置
     */
    @NotBlank(message = "选择位置不能为空", groups = {AddGroup.class, EditGroup.class})
    private String address;

    /**
     * 经度
     */
    @NotNull(message = "经度不能为空", groups = {AddGroup.class, EditGroup.class})
    private Double longitude;

    /**
     * 纬度
     */
    @NotNull(message = "纬度不能为空", groups = {AddGroup.class, EditGroup.class})
    private Double latitude;

    /**
     * 动态类型(1=图片文字，1=视频)
     */
    @NotBlank(message = "动态类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private String type;

    /**
     * 状态(0=展示，1=不展示，2=待审核)
     */
    @NotBlank(message = "状态(0=展示，1=不展示，2=待审核)不能为空", groups = {AddGroup.class, EditGroup.class})
    private String status;

    /**
     * 动态范围(1=所有人，2=自己，3=部分人,4=不给谁看)
     */
    @NotBlank(message = "动态范围(1=所有人，2=自己，3=部分人,4=不给谁看)不能为空", groups = {AddGroup.class, EditGroup.class})
    private String scope;

    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空", groups = {AddGroup.class, EditGroup.class})
    private String remark;

    /*******************视频信息*********************/

    /**
     * 视频地址
     */
    private String url;

    /**
     * 图片高度
     */
    private String height;

    /**
     * 图片宽度
     */
    private String width;

    /**
     * 切割后的图片数组
     */
    private List<String> imagesList;


    private String statusTop;

}
