package com.ruoyi.app.domain;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP群组对象 app_group
 *
 * <AUTHOR>
 * @date 2025-01-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_group")
public class AppGroup extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 群组ID
     */
    private String groupId;
    /**
     * 群名称
     */
    private String name;

    /**
     * 群频道名称
     */
    private String channelName;

    /**
     * 群公告
     */
    private String description;
    /**
     * 群类型(0=公开群，1=私有群)
     */
    private String type;
    /**
     * 是否允许群成员邀请别人加入此群(0=允许，1=不允许)只对私有群有效
     */
    private String allowinvites;
    /**
     * 用户申请入群是否需要群主或者群管理员审批(0=不需要，1=需要)
     */
    private String membersonly;
    /**
     * 邀请用户入群时是否需要被邀用户同意(0=不需要，1=需要)
     */
    private String inviteNeedConfirm;
    /**
     * 群组状态(0=正常，1=封禁)
     */
    private String disabled;
    /**
     * 群扩展信息
     */
    private String custom;

    /**
     * 全局禁言(0=未禁言，1=禁言)
     */
    private String muteAll;

    /**
     * 删除标志（0=存在,2=删除）
     */
    @TableLogic
    private String delFlag;
    /**
     * 备注
     */
    private String remark;

}
