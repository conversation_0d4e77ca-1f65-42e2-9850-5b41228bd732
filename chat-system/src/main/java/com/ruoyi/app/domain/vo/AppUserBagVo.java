package com.ruoyi.app.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ruoyi.common.annotation.ExcelDictFormat;
import com.ruoyi.common.convert.ExcelDictConvert;
import lombok.Data;

import java.util.Date;


/**
 * App用户签到领取奖励视图对象 app_user_bag
 *
 * <AUTHOR>
 * @date 2023-03-29
 */
@Data
@ExcelIgnoreUnannotated
public class AppUserBagVo {

    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 背包类型
     */
    @ExcelProperty(value = "背包类型")
    private String type;

    /**
     * 背包数量
     */
    @ExcelProperty(value = "背包数量")
    private Integer number;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;
}
