package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP商城的轮播图对象 app_banner
 *
 * <AUTHOR>
 * @date 2025-04-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_banner")
public class AppBanner extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 图片
     */
    private String image;
    /**
     * 类型
     */
    private String type;
    /**
     * 状态(1=不显示,0=显示)
     */
    private String status;

    /**
     * 跳转的商品ID
     */
    private String goodsId;

    /**
     * 备注
     */
    private String remark;
    /**
     * 删除标志（0=存在,2=删除）
     */
    @TableLogic
    private String delFlag;

}
