package com.ruoyi.app.domain.bo;

import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP用户VIP业务对象 app_user_vip
 *
 * <AUTHOR>
 * @date 2025-03-16
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class AppUserVipBo extends BaseEntity {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * VIP结束时间
     */
    private Long endTime;

    /**
     * 当前等级
     */
    private Integer level;

    /**
     * 当前经验
     */
    private Integer experience;

    /**
     * VIP的类型
     */
    private String type;

    /**
     * VIP的ID
     */
    private Long vipId;

    /**
     * 备注
     */
    private String remark;


}
