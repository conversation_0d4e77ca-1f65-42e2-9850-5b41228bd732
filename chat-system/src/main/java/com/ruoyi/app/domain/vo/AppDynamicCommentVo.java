package com.ruoyi.app.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;


/**
 * APP用户动态评论视图对象 app_dynamic_comment
 *
 * <AUTHOR>
 * @date 2025-12-23
 */
@Data
@ExcelIgnoreUnannotated
public class AppDynamicCommentVo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 动态ID
     */
    @ExcelProperty(value = "动态ID")
    private Long dynamicId;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 指定用户的评论
     */
    @ExcelProperty(value = "指定用户的评论")
    private Long toUserId;

    /**
     * 评论内容
     */
    @ExcelProperty(value = "评论内容")
    private String context;


    /**
     * 评论用户昵称
     */
    private String nickName;

    /**
     * 回复用户的昵称
     */
    private String toNickName;

    /**
     * 评论用户的信息
     */
    private AppUserVo userVo;

    /**
     * 当前用户是否点赞评论
     * true 已点赞
     * false 未点赞
     */
    private Boolean userCommentLike = false;

    /**
     * 评论点赞数量
     */
    private Integer commentLikeVosNumber = 0;

    /**
     * 动态发布的时间格式化
     */
    private String createTimeFormat;

}
