package com.ruoyi.app.domain.bo;

import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 动态表情业务对象 app_dynamic_emoji
 *
 * <AUTHOR>
 * @date 2025-07-18
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class AppDynamicEmojiBo extends BaseEntity {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 表情名称
     */
    @NotBlank(message = "表情名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String emojiName;

    /**
     * 表情图片
     */
    @NotBlank(message = "表情图片不能为空", groups = { AddGroup.class, EditGroup.class })
    private String emojiImage;

    /**
     * 表情动态
     */
    private String emojiSvgImage;


    private Integer sort;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态(0=生效，1=不生效)
     */
    private String status;
}
