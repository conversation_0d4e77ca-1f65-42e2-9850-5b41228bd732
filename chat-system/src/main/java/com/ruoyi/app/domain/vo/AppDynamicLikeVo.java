package com.ruoyi.app.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ruoyi.common.annotation.ExcelDictFormat;
import com.ruoyi.common.convert.ExcelDictConvert;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.Date;


/**
 * APP动态点赞视图对象 app_dynamic_like
 *
 * <AUTHOR>
 * @date 2023-01-16
 */
@Data
@ExcelIgnoreUnannotated
public class AppDynamicLikeVo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 动态或者评论ID
     */
    @ExcelProperty(value = "动态或者评论ID")
    private Long likeId;

    /**
     * 点赞用户ID
     */
    @ExcelProperty(value = "点赞用户ID")
    private Long userId;

    /**
     * 点赞类型(0=动态点赞,1=评论点赞,2=商品点赞)
     */
    @ExcelProperty(value = "点赞类型(0=动态点赞,1=评论点赞,2=商品点赞)")
    private String type;

    /**
     * 点赞用户信息
     */
    private UserVo userVo;


}
