package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * APP用户动态评论对象 app_dynamic_comment
 *
 * <AUTHOR>
 * @date 2025-12-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_dynamic_comment")
public class AppDynamicComment extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 动态ID
     */
    private Long dynamicId;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 指定用户的评论
     */
    private Long toUserId;
    /**
     * 评论内容
     */
    private String context;
    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

}
