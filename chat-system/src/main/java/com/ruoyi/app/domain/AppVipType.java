package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APPVIP类型对象 app_vip_type
 *
 * <AUTHOR>
 * @date 2025-03-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_vip_type")
public class AppVipType extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 套餐名称
     */
    private String name;
    /**
     * VIP时间
     */
    private Integer day;
    /**
     * 安卓价格
     */
    private Double androidPrice;
    /**
     * 苹果价格
     */
    private Double iosPrice;

    /**
     * 苹果指定套餐名称
     */
    private String iosName;

    /**
     * 折扣
     */
    private Integer discount;

    /**
     * 划线价格
     */
    private Double listPrice;

    /**
     * 删除标志（0=存在,2=删除）
     */
    @TableLogic
    private String delFlag;
    /**
     * 备注
     */
    private String remark;

    /**
     * 会员每日赠送的字符串
     */
    private Integer giveNumber;

    /**
     * 会员的礼物折扣
     */
    private BigDecimal giveDiscount;


    private String type;


}
