package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP用户匹配配置和记录对象 app_mate
 *
 * <AUTHOR>
 * @date 2023-05-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_mate")
public class AppMate extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 是否匹配在线
     */
    private String onLine;
    /**
     * 是否使用匹配功能
     */
    private String useMate;
    /**
     * 是否距离最近
     */
    private String distance;
    /**
     * 是否信息完善
     */
    private String infoComplete;
    /**
     * 是否提示
     */
    private String remind;
    /**
     * 操作类型(1=匹配)
     */
    private String type;
    /**
     * 操作时间
     */
    private Long recordTime;
    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

}
