package com.ruoyi.app.domain.bo;

import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP商城的轮播图业务对象 app_banner
 *
 * <AUTHOR>
 * @date 2023-04-20
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class AppBannerBo extends BaseEntity {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 图片
     */
    @NotBlank(message = "图片不能为空", groups = { AddGroup.class, EditGroup.class })
    private String image;

    /**
     * 类型
     */
    @NotBlank(message = "类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String type;

    /**
     * 状态(1=不显示,0=显示)
     */
    @NotBlank(message = "状态(1=不显示,0=显示)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;

    /**
     * 跳转的商品ID
     */
    private String goodsId;

    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remark;


}
