package com.ruoyi.app.domain.bo;

import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 活动展馆管理业务对象 app_show
 *
 * <AUTHOR>
 * @date 2025-04-23
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class AppShowBo extends BaseEntity {

    /**
     * ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 活动标题
     */
    @NotBlank(message = "活动标题不能为空", groups = { AddGroup.class, EditGroup.class })
    private String title;

    /**
     * 活动内容
     */
    private String context;

    /**
     * 活动图片
     */
    private String images;

    /**
     * 描述
     */
    private String remark;


}
