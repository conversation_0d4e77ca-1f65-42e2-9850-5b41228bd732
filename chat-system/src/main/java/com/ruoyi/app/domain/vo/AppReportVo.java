package com.ruoyi.app.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;



/**
 * APP举报类型视图对象 app_report
 *
 * <AUTHOR>
 * @date 2023-01-03
 */
@Data
@ExcelIgnoreUnannotated
public class AppReportVo {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 举报内容
     */
    @ExcelProperty(value = "举报内容")
    private String context;

    /**
     * 举报类型(0=群组,1=个人)
     */
    @ExcelProperty(value = "举报类型(0=群组,1=个人)")
    private String type;

    /**
     * 帐号状态(0=正常,1=停用)
     */
    @ExcelProperty(value = "帐号状态(0=正常,1=停用)")
    private String status;


}
