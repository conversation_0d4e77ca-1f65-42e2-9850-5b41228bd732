package com.ruoyi.app.domain.bo;

import com.ruoyi.app.domain.vo.AppLabelVo;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import com.ruoyi.common.core.validate.QueryGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.awt.*;

/**
 * APP用户标签喜欢业务对象 app_label_like
 *
 * <AUTHOR>
 * @date 2023-01-13
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class AppLabelLikeBo extends BaseEntity {

    /**
     * ID
     */
    private Long id;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long userId;

    /**
     * 标签ID
     */
    @NotNull(message = "标签ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long labelId;

    /**
     * 标签类型ID
     */
    @NotNull(message = "标签类型ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long typeId;

    /**
     * 超级喜欢(0=是，1=否)
     */
    @NotBlank(message = "超级喜欢(0=是，1=否)不能为空", groups = {AddGroup.class, EditGroup.class})
    private String likeStatus;

    /**
     * 超级喜欢理由
     */
    @Length(min = 2, max = 100, message = "描述字符超过限制2-100个字符", groups = QueryGroup.class)
    @NotBlank(message = "超级喜欢理由不能为空", groups = {AddGroup.class, EditGroup.class, QueryGroup.class})
    private String cause;

    /**
     * 状态(0=生效，1=不生效)
     */
    @NotBlank(message = "状态(0=生效，1=不生效)不能为空", groups = {AddGroup.class, EditGroup.class})
    private String status;

    /**
     * 标签信息
     */
    private AppLabelVo label;

    /**
     * 备注
     */
    private String remark;


}
