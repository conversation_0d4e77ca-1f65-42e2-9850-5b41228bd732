package com.ruoyi.app.domain.bo;

import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP用户静态资源业务对象 app_images
 *
 * <AUTHOR>
 * @date 2023-04-04
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class AppImagesBo extends BaseEntity {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 图片地址
     */
    @NotBlank(message = "图片地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String url;

    /**
     * 图片类型(0=用户1=匹配)
     */
    @NotBlank(message = "图片类型(0=用户1=匹配)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String type;

    /**
     * 帐号状态(0=正常,1=停用)
     */
    @NotBlank(message = "帐号状态(0=正常,1=停用)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;


}
