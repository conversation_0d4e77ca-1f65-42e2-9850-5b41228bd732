package com.ruoyi.app.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ruoyi.app.domain.AppDynamic;
import com.ruoyi.common.annotation.ExcelDictFormat;
import com.ruoyi.common.convert.ExcelDictConvert;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.Date;


/**
 * APP用户喜欢记录视图对象 app_like_notes
 *
 * <AUTHOR>
 * @date 2023-02-14
 */
@Data
@ExcelIgnoreUnannotated
public class AppLikeNotesVo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 操作的用户ID
     */
    @ExcelProperty(value = "操作的用户ID")
    private Long sideId;

    /**
     * 状态(0=喜欢，1=不喜欢)
     */
    @ExcelProperty(value = "状态(0=喜欢，2=不喜欢)")
    private String status;


    /**
     * 喜欢状态(0=超级喜欢，1=喜欢)
     */
    @ExcelProperty(value = "状态(0=超级喜欢，1=喜欢)")
    private String likeStatus;

    /**
     * 已读状态(1=已读,0=未读)
     */
    private String readStatus;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 用户信息
     */
    private UserVo userVo;

}
