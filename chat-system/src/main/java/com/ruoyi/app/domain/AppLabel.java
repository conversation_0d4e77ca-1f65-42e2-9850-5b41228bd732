package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * APP标签对象 app_label
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_label")
public class AppLabel extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 标签名称
     */
    private String name;
    /**
     * 标签图标
     */
    private String image;
    /**
     * 排序字段
     */
    private Long sort;
    /**
     * 标签类型ID
     */
    private Long typeId;

    /**
     * 推荐状态(0=推荐，1=不推荐)
     */
    private String hotStatus;

    /**
     * 状态(0=生效，1=不生效)
     */
    private String status;
    /**
     * 备注
     */
    private String remark;

}
