package com.ruoyi.app.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;



/**
 * APP用户第三方登录视图对象 app_third_user
 *
 * <AUTHOR>
 * @date 2023-01-07
 */
@Data
@ExcelIgnoreUnannotated
public class AppThirdUserVo {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 第三方Token
     */
    @ExcelProperty(value = "第三方Token")
    private String token;

    /**
     * 第三方类型
     */
    @ExcelProperty(value = "第三方类型")
    private String type;

    /**
     * 头像地址
     */
    @ExcelProperty(value = "头像地址")
    private String image;


}
