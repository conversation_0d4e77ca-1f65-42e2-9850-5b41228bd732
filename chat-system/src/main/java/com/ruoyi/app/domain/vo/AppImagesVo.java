package com.ruoyi.app.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ruoyi.common.annotation.ExcelDictFormat;
import com.ruoyi.common.convert.ExcelDictConvert;
import lombok.Data;
import java.util.Date;



/**
 * APP用户静态资源视图对象 app_images
 *
 * <AUTHOR>
 * @date 2025-04-04
 */
@Data
@ExcelIgnoreUnannotated
public class AppImagesVo {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 图片地址
     */
    @ExcelProperty(value = "图片地址")
    private String url;

    /**
     * 图片类型(0=用户1=匹配)
     */
    @ExcelProperty(value = "图片类型(0=用户1=匹配)")
    private String type;

    /**
     * 帐号状态(0=正常,1=停用)
     */
    @ExcelProperty(value = "帐号状态(0=正常,1=停用)")
    private String status;


}
