package com.ruoyi.app.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.amazonaws.services.s3.transfer.Download;
import com.ruoyi.common.annotation.ExcelDictFormat;
import com.ruoyi.common.convert.ExcelDictConvert;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.Date;


/**
 * APP版本更新记录视图对象 app_version
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
@Data
@ExcelIgnoreUnannotated
public class AppVersionVo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 标题
     */
    @ExcelProperty(value = "标题")
    private String title;

    /**
     * 更新内容
     */
    @ExcelProperty(value = "更新内容")
    private String context;

    /**
     * 苹果版本
     */
    @ExcelProperty(value = "苹果版本")
    private String iosVersion;

    /**
     * 安卓版本名称
     */
    @ExcelProperty(value = "安卓版本名称")
    private String androidVersion;

    /**
     * 安卓版本号
     */
    @ExcelProperty(value = "安卓版本号")
    private Integer versionCord;

    /**
     * 更新状态(0=不强制，1=强制)
     */
    @ExcelProperty(value = "更新状态(0=不强制，1=强制)")
    private String forceUpdate;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


    /**
     * 苹果下载地址
     */
    private String iosDownload;

    /**
     * 安卓下载地址
     */
    private String androidDownload;

}
