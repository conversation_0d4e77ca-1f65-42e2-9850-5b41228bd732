package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP用户关系对象 app_friend_relation
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_friend_relation")
public class AppFriendRelation extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * ID
     */
    @TableId(value = "relation_id")
    private Long relationId;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 关注用户ID
     */
    private Long toUserId;
    /**
     * 关注来源(0=收藏，1=足迹，2=点赞)
     */
    private String type;
    /**
     * 已读状态(1=已读,0=未读)
     */
    private String status;
    /**
     * 备注
     */
    private String remark;
    /**
     * 删除标志（0=存在,2=删除）
     */
    @TableLogic
    private String delFlag;

}
