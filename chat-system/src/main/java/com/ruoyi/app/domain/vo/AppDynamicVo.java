package com.ruoyi.app.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * APP用户动态视图对象 app_dynamic
 *
 * <AUTHOR>
 * @date 2025-12-22
 */
@Data
@ExcelIgnoreUnannotated
public class AppDynamicVo {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 动态内容
     */
    @ExcelProperty(value = "动态内容")
    private String context;

    /**
     * 动态图片，多个逗号分隔
     */
    @ExcelProperty(value = "动态图片，多个逗号分隔")
    private String images;

    /**
     * 选择位置
     */
    @ExcelProperty(value = "选择位置")
    private String address;

    /**
     * 经度
     */
    @ExcelProperty(value = "经度")
    private Double longitude;

    /**
     * 纬度
     */
    @ExcelProperty(value = "纬度")
    private Double latitude;

    /**
     * 动态类型(1=图片文字，2=视频)
     */
    @ExcelProperty(value = "动态类型(1=图片文字，2=视频)")
    private String type;

    /**
     * 状态(0=展示，1=不展示，2=待审核)
     */
    @ExcelProperty(value = "状态(0=展示，1=不展示，2=待审核)")
    private String status;

    /**
     * 动态范围(1=所有人，2=自己，3=部分人,4=不给谁看)
     */
    @ExcelProperty(value = "动态范围(1=所有人，2=自己，3=部分人,4=不给谁看)")
    private String scope;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 用户信息
     */
    private UserResultVo userVo;

    /**
     * 当前用户是否点赞
     * true 已点赞
     * false 未点赞
     */
    private Boolean userLike = false;

    /**
     * 当前用户是否收藏
     * true 已收藏
     * false 未收藏
     */
    private Boolean collectLike = false;

    /**
     * 评论列表
     */
    private List<AppDynamicCommentVo> commentVos;

    /**
     * 视频/图片信息
     */
    private List<AppDynamicVideoVo> videoVos;

    /**
     * 评论数量
     */
    private Integer commentVosNumber = 0;

    /**
     * 点赞列表
     */
    private List<AppDynamicLikeVo> likeVos;

    /**
     * 点赞数量
     */
    private Integer likeVosNumber = 0;


    /**
     * 动态发布的时间格式化
     */
    private String createTimeFormat;

    /**
     * 动态发布年份
     */
    private Integer year;

    /**
     * 分享地址
     */
    private String shareUrl;

    /**
     *
     * 收藏时间/点赞时间
     */
    private Date likeDate;

    /**
     * 距离附近用户的距离
     */
    private Integer distance;

    /**
     * 距离附近用户的距离文本
     */
    private String distanceText;


    private String statusTop;

    /**
     * 动态表情列表
     */
    private List<AppDynamicEmojiVo> emojiVos;

}
