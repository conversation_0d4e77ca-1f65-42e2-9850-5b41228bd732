package com.ruoyi.app.domain.bo;

import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;

import java.util.Date;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP用户通知记录业务对象 app_record
 *
 * <AUTHOR>
 * @date 2023-04-27
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class AppRecordBo extends BaseEntity {

    /**
     * ID
     */
    private Long id;

    /**
     * 记录ID
     */
    private Long recordId;

    /**
     * 评论点赞用户ID
     */
    private Long userId;

    /**
     * 记录发布人的用户ID
     */
    private Long recordUserId;

    /**
     * 评论ID
     */
    private Long commentId;

    /**
     * 类型1=点赞2=评论
     */
    @NotBlank(message = "类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private String type;

    /**
     * 已读状态(1=已读,0=未读)
     */
    @NotBlank(message = "已读状态(1=已读,0=未读)不能为空", groups = {AddGroup.class, EditGroup.class})
    private String readStatus;


}
