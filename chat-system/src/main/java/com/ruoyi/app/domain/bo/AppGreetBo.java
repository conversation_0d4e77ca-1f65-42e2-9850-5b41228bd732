package com.ruoyi.app.domain.bo;

import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;

import java.util.Date;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP打招呼记录
 * 业务对象 app_greet
 *
 * <AUTHOR>
 * @date 2023-03-23
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class AppGreetBo extends BaseEntity {

    /**
     * ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 对方ID
     */
    @NotNull(message = "ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long fromId;

    /**
     * 发送数量
     */
    private Integer number;


    /**
     * 发送内容
     */
    @NotBlank(message = "打招呼内容不能为空", groups = {AddGroup.class, EditGroup.class})
    private String context;

    /**
     * 记录状态
     */
    private String status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 获取列表
     * 1=发送者用户列表
     * 2=接收方用户列表
     */
    private String type;


}
