package com.ruoyi.app.domain.bo;

import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;

import java.util.Date;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP城市列业务对象 app_city_dist
 *
 * <AUTHOR>
 * @date 2023-03-01
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class AppCityDistBo extends BaseEntity {

    /**
     *
     */
    @NotNull(message = "不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 省直辖市
     */
    @NotBlank(message = "省直辖市不能为空", groups = {AddGroup.class, EditGroup.class})
    private String province;

    /**
     * 类型
     */
    @NotBlank(message = "类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private String type;

    /**
     * 城市
     */
    @NotBlank(message = "城市不能为空", groups = {AddGroup.class, EditGroup.class})
    private String city;

    /**
     * 上级ID
     */
    @NotNull(message = "上级ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long provinceId;

    /**
     * 县城
     */
    @NotBlank(message = "县城不能为空", groups = {AddGroup.class, EditGroup.class})
    private String district;

    /**
     * 城市代码
     */
    @NotNull(message = "城市代码不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long cityId;


}
