package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * APP动态视频信息对象 app_dynamic_video
 *
 * <AUTHOR>
 * @date 2025-01-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_dynamic_video")
public class AppDynamicVideo extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 动态ID
     */
    private Long dynamicId;

    /**
     * 视频地址
     */
    private String url;
    /**
     * 视频/动态图片
     */
    private String image;

    /**
     * 视频动态图片缩略图
     */
    private String smallImage;
    /**
     * 封面高度
     */
    private Integer height;
    /**
     * 封面宽度
     */
    private Integer width;
    /**
     * 删除标志（0=存在,2=删除）
     */
    @TableLogic
    private String delFlag;
    /**
     * 备注
     */
    private String remark;

}
