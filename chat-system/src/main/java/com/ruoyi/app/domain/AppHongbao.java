package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP红包对象 app_hongbao
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_hongbao")
public class AppHongbao extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * ID
     */
    @TableId(value = "hongbao_id")
    private Long hongbaoId;
    /**
     * 红包名称
     */
    private String hongbaoName;
    /**
     * 红包数量
     */
    private Long hongbaoNumber;
    /**
     * 红包金额
     */
    private BigDecimal hongbaoMoney;
    /**
     * 发送红包者
     */
    private Long userId;
    /**
     * 接收红包者
     */
    private Long sendUser;
    /**
     * 接收时间
     */
    private Date receiptTime;
    /**
     * 状态(0=待领取，1=已领取，2=未领退回)
     */
    private String status;
    /**
     * 删除标志（0=存在,2=删除）
     */
    @TableLogic
    private String delFlag;
    /**
     * 备注
     */
    private String remark;

}
