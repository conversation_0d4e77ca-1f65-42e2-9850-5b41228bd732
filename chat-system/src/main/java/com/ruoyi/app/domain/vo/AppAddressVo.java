package com.ruoyi.app.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;


/**
 * APP用户地址信息视图对象 app_address
 *
 * <AUTHOR>
 * @date 2025-12-21
 */
@Data
@ExcelIgnoreUnannotated
public class AppAddressVo {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 收货姓名
     */
    @ExcelProperty(value = "收货姓名")
    private String nickName;

    /**
     * 收货电话
     */
    @ExcelProperty(value = "收货电话")
    private String mobile;

    /**
     * 收货城市(省市区)
     */
    @ExcelProperty(value = "收货城市(省市区)")
    private String city;

    /**
     * 城市代码
     */
    @ExcelProperty(value = "城市代码")
    private String cityCode;

    /**
     * 详细地址
     */
    @ExcelProperty(value = "详细地址")
    private String cityDetails;

    /**
     * 地址是否默认
     */
    private String status;

    /**
     * 用户信息
     */
    private AppUserVo userVo;

}
