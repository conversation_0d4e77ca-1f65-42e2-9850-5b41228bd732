package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APPv会员VIP订单对象 app_vip_order
 *
 * <AUTHOR>
 * @date 2025-03-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_vip_order")
public class AppVipOrder extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 充值VIP类型
     */
    private Long vipId;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 支付状态(0=未支付  1=已支付)
     */
    private String status;
    /**
     * 支付方式
     */
    private String payType;
    /**
     * 会员类型 1 正常购买 2 1元体验 3 积分兑换
     */
    private String type;
    /**
     * 备注
     */
    private String remark;
    /**
     * 删除标志（0=代表存在 2=代表删除）
     */
    @TableLogic
    private String delFlag;

}
