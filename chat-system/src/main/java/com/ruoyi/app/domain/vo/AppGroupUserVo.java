package com.ruoyi.app.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ruoyi.common.annotation.ExcelDictFormat;
import com.ruoyi.common.convert.ExcelDictConvert;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;


/**
 * APP群组视图对象 app_group_user
 *
 * <AUTHOR>
 * @date 2025-01-29
 */
@Data
@ExcelIgnoreUnannotated
public class AppGroupUserVo {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 群组记录ID
     */
    @ExcelProperty(value = "群组记录ID")
    private Long groupId;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 群内用户昵称
     */
    @ExcelProperty(value = "群内用户昵称")
    private String nikeName;

    /**
     * 群组描述
     */
    @ExcelProperty(value = "群组描述")
    private String groupName;

    /**
     * 是否显示群昵称
     */
    @ExcelProperty(value = "是否显示群昵称")
    private String groupShow;

    /**
     * 群组身份(1=群主，2=管理员，3=群成员)
     */
    @ExcelProperty(value = "群组身份(1=群主，2=管理员，3=群成员)")
    private String place;

    /**
     * 状态(0=正常，1=禁言)
     */
    @ExcelProperty(value = "状态(0=正常，1=禁言)")
    private String status;

    /**
     * 对讲状态(0=正常，1=勿扰)
     */
    @ExcelProperty(value = "对讲状态")
    private String talkStatus;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 用户信息
     */
    private UserVo userVo;
}
