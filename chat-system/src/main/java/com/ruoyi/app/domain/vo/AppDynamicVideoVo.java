package com.ruoyi.app.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;


/**
 * APP动态视频信息视图对象 app_dynamic_video
 *
 * <AUTHOR>
 * @date 2023-01-11
 */
@Data
@ExcelIgnoreUnannotated
public class AppDynamicVideoVo {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 动态ID
     */
    @ExcelProperty(value = "动态ID")
    private Long dynamicId;

    /**
     * 视频地址
     */
    @ExcelProperty(value = "视频地址")
    private String url;

    /**
     * 视频/动态图片
     */
    @ExcelProperty(value = "视频/动态图片")
    private String image;

    /**
     * 视频/动态缩略图
     */
    @ExcelProperty(value = "视频/动态缩略图")
    private String smallImage;

    /**
     * 封面高度
     */
    @ExcelProperty(value = "封面高度")
    private Integer height;

    /**
     * 封面宽度
     */
    @ExcelProperty(value = "封面宽度")
    private Integer width;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


}
