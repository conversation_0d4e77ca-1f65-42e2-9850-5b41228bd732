package com.ruoyi.app.domain.bo;

import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;

import java.util.Date;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP用户动态话题类型业务对象 app_dynamic_type
 *
 * <AUTHOR>
 * @date 2025-05-18
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class AppDynamicTypeBo extends BaseEntity {

    /**
     * ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 动态标签名称
     */
    @NotBlank(message = "动态标签名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String name;

    /**
     * 动态标签描述
     */
    private String introduce;

    /**
     * 动态标签图片
     */
    private String image;

    /**
     * 动态标签类型(1=用户话题,0=系统话题,2=活动话题)
     */
    private String type;

    /**
     * 话题热度
     */
    private Integer heat;

    /**
     * 备注
     */
    private String remark;



    private String status;

}
