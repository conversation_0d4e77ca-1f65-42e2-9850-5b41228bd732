package com.ruoyi.app.domain.vo;

import java.math.BigDecimal;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ruoyi.common.annotation.ExcelDictFormat;
import com.ruoyi.common.convert.ExcelDictConvert;
import lombok.Data;
import java.util.Date;



/**
 * APP用户账单明细视图对象 app_detail
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Data
@ExcelIgnoreUnannotated
public class AppDetailVo {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 积分数量
     */
    @ExcelProperty(value = "积分数量")
    private BigDecimal balance;

    /**
     * 积分手续费
     */
    @ExcelProperty(value = "积分手续费")
    private BigDecimal freeBalance;

    /**
     * 账单类型[]
     */
    @ExcelProperty(value = "账单类型[]")
    private String type;

    /**
     * 类型(1=收入，2=支出)
     */
    @ExcelProperty(value = "类型(1=收入，2=支出)")
    private String flow;

    /**
     * 金币类型(1=余额，2=积分)
     */
    @ExcelProperty(value = "金币类型(1=余额，2=积分)")
    private String coinTyp;

    /**
     * 来源用户ID
     */
    @ExcelProperty(value = "来源用户ID")
    private Long fromId;

    /**
     * 创建时间戳
     */
    @ExcelProperty(value = "创建时间戳")
    private Integer insertTime;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


}
