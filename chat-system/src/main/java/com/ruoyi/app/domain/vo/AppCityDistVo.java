package com.ruoyi.app.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ruoyi.common.annotation.ExcelDictFormat;
import com.ruoyi.common.convert.ExcelDictConvert;
import lombok.Data;
import java.util.Date;



/**
 * APP城市列视图对象 app_city_dist
 *
 * <AUTHOR>
 * @date 2025-03-01
 */
@Data
@ExcelIgnoreUnannotated
public class AppCityDistVo {

    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Long id;

    /**
     * 省直辖市
     */
    @ExcelProperty(value = "省直辖市")
    private String province;

    /**
     * 类型
     */
    @ExcelProperty(value = "类型")
    private String type;

    /**
     * 城市
     */
    @ExcelProperty(value = "城市")
    private String city;

    /**
     * 上级ID
     */
    @ExcelProperty(value = "上级ID")
    private Long provinceId;

    /**
     * 县城
     */
    @ExcelProperty(value = "县城")
    private String district;

    /**
     * 城市代码
     */
    @ExcelProperty(value = "城市代码")
    private Long cityId;


}
