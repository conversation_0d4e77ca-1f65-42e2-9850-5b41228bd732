package com.ruoyi.app.domain.bo;

import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * APP举报类型业务对象 app_report
 *
 * <AUTHOR>
 * @date 2025-01-03
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class AppReportBo extends BaseEntity {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 举报内容
     */
    @NotBlank(message = "举报内容不能为空", groups = { AddGroup.class, EditGroup.class })
    private String context;

    /**
     * 举报类型(0=群组,1=个人)
     */
    @NotBlank(message = "举报类型(0=群组,1=个人)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String type;

    /**
     * 帐号状态(0=正常,1=停用)
     */
    @NotBlank(message = "帐号状态(0=正常,1=停用)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;


}
