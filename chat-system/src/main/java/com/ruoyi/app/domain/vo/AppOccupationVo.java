package com.ruoyi.app.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ruoyi.common.annotation.ExcelDictFormat;
import com.ruoyi.common.convert.ExcelDictConvert;
import lombok.Data;
import java.util.Date;



/**
 * APP职业信息视图对象 app_occupation
 *
 * <AUTHOR>
 * @date 2022-12-12
 */
@Data
@ExcelIgnoreUnannotated
public class AppOccupationVo {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 职业名称
     */
    @ExcelProperty(value = "职业名称")
    private String name;

    /**
     * 记录状态
     */
    @ExcelProperty(value = "记录状态")
    private String status;

    /**
     * 排序字段
     */
    @ExcelProperty(value = "排序字段")
    private Integer sort;


}
