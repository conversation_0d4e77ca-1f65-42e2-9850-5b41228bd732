package com.ruoyi.app.domain.bo;

import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP超级喜欢的标记业务对象 app_like_sign
 *
 * <AUTHOR>
 * @date 2025-02-16
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class AppLikeSignBo extends BaseEntity {

    /**
     * ID
     */
    private Long id;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 超级喜欢的用户ID
     */
    @NotNull(message = "超级喜欢的用户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long sideId;

    /**
     * 已读状态(1=已读,0=未读)
     */
    @NotBlank(message = "已读状态(1=已读,0=未读)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;

    /**
     * 备注
     */
    private String remark;


}
