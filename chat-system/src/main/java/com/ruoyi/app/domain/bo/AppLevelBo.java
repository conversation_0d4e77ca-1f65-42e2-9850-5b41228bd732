package com.ruoyi.app.domain.bo;

import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * AP用户等级业务对象 app_level
 *
 * <AUTHOR>
 * @date 2024-09-26
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class AppLevelBo extends BaseEntity {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 等级名称
     */
    @NotBlank(message = "等级名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 积分小值
     */
    private Integer activeMin;

    /**
     * 积分大值
     */
    private Integer activeMax;

    /**
     * 备注
     */
    private String remark;


}
