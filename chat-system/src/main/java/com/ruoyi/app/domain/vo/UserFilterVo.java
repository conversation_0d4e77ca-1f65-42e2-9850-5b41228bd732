package com.ruoyi.app.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ruoyi.common.annotation.ExcelDictFormat;
import com.ruoyi.common.convert.ExcelDictConvert;
import lombok.Data;

import java.util.Date;
import java.util.List;


/**
 * APP用户信息视图对象 app_user
 *
 * <AUTHOR>
 * @date 2025-12-08
 */
@Data
@ExcelIgnoreUnannotated
public class UserFilterVo {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private Long id;

    /**
     * 环信uuid
     */
    @ExcelProperty(value = "环信uuid")
    private String uuid;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 聊天用户名
     */
    private String chatName;

    /**
     * 头像地址
     */
    private String avatar;
}
