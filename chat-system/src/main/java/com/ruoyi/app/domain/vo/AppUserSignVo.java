package com.ruoyi.app.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ruoyi.common.annotation.ExcelDictFormat;
import com.ruoyi.common.convert.ExcelDictConvert;
import lombok.Data;
import java.util.Date;



/**
 * App用户签到记录视图对象 app_user_sign
 *
 * <AUTHOR>
 * @date 2025-03-28
 */
@Data
@ExcelIgnoreUnannotated
public class AppUserSignVo {

    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 签到天数
     */
    @ExcelProperty(value = "签到天数")
    private Integer day;

    /**
     * 签到时间
     */
    @ExcelProperty(value = "签到时间")
    private Long signTime;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


}
