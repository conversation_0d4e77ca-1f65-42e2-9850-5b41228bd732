package com.ruoyi.app.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;


/**
 * APP举报信息视图对象 app_user_report
 *
 * <AUTHOR>
 * @date 2023-01-03
 */
@Data
@ExcelIgnoreUnannotated
public class AppUserReportVo {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 举报用户和群组的ID
     */
    @ExcelProperty(value = "举报用户和群组的ID")
    private Long reportId;

    /**
     * 举报内容
     */
    @ExcelProperty(value = "举报内容")
    private String context;

    /**
     * 描述信息
     */
    @ExcelProperty(value = "描述信息")
    private String info;

    /**
     * 举报图片
     */
    @ExcelProperty(value = "举报图片")
    private String images;

    /**
     * 举报类型(0=群组,1=个人)
     */
    @ExcelProperty(value = "举报类型(0=群组,1=个人)")
    private String type;

    /**
     * 帐号状态(0=已处理,1=未处理)
     */
    @ExcelProperty(value = "帐号状态(0=已处理,1=未处理)")
    private String status;


}
