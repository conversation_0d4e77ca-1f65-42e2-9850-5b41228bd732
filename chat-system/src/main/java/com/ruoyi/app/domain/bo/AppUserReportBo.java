package com.ruoyi.app.domain.bo;

import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * APP举报信息业务对象 app_user_report
 *
 * <AUTHOR>
 * @date 2023-01-03
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class AppUserReportBo extends BaseEntity {

    /**
     * ID
     */
    private Long id;

    /**
     * 举报用户和群组的ID
     */
    @NotNull(message = "举报用户或者群组ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long reportId;

    /**
     * 举报内容
     */
    @NotBlank(message = "举报理由不能为空", groups = {AddGroup.class, EditGroup.class})
    private String context;

    /**
     * 描述信息
     */
    @Length(max = 100, message = "最多100个字符")
    private String info;

    /**
     * 举报图片
     */
    private String images;

    /**
     * 举报图片
     */
    private String image;

    /**
     * 举报类型(0=群组,1=个人)
     */
    @NotBlank(message = "举报类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private String type;

    /**
     * 帐号状态(0=已处理,1=未处理)
     */
    private String status;


}
