package com.ruoyi.app.domain.bo;

import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * APP举报信息业务对象 app_user_report
 *
 * <AUTHOR>
 * @date 2025-01-03
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class AppUserReportBo extends BaseEntity {

    /**
     * ID
     */
    private Long id;

    /**
     * 举报用户和群组的ID
     */
    private Long reportId;

    /**
     * 举报内容
     */
    @NotBlank(message = "{report.not.type.blank}", groups = {AddGroup.class, EditGroup.class})
    private String context;

    /**
     * 描述信息
     */
    @Length(max = 200, message = "{report.not.info.blank}")
    private String info;

    /**
     * 举报图片
     */
    private String images;

    /**
     * 举报图片
     */
    private String image;

    /**
     * 举报类型(0=群组,1=个人)
     */
    private String type;

    /**
     * 帐号状态(0=已处理,1=未处理)
     */
    private String status;
}
