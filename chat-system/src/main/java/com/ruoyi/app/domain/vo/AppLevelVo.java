package com.ruoyi.app.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ruoyi.common.annotation.ExcelDictFormat;
import com.ruoyi.common.convert.ExcelDictConvert;
import lombok.Data;
import java.util.Date;



/**
 * AP用户等级视图对象 app_level
 *
 * <AUTHOR>
 * @date 2025-09-26
 */
@Data
@ExcelIgnoreUnannotated
public class AppLevelVo {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 等级名称
     */
    @ExcelProperty(value = "等级名称")
    private String name;

    /**
     * 积分小值
     */
    @ExcelProperty(value = "积分小值")
    private Integer activeMin;

    /**
     * 积分大值
     */
    @ExcelProperty(value = "积分大值")
    private Integer activeMax;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


}
