package com.ruoyi.app.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ruoyi.common.annotation.ExcelDictFormat;
import com.ruoyi.common.convert.ExcelDictConvert;
import lombok.Data;

import java.util.Date;


/**
 * APP瓶盖码视图对象 app_code
 *
 * <AUTHOR>
 * @date 2023-04-03
 */
@Data
@ExcelIgnoreUnannotated
public class AppCodeVo {

    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "ID")
    private Long id;


    private String code;

    /**
     *
     */
    @ExcelProperty(value = "desCode")
    private String desCode;

    /**
     * 状态(0=未使用 1=已使用)
     */
    private Integer status;

    /**
     *
     */
    private Long userId;


}
