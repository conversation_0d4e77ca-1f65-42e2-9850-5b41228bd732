package com.ruoyi.app.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ruoyi.common.annotation.ExcelDictFormat;
import com.ruoyi.common.convert.ExcelDictConvert;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * APP用户信息视图对象 app_user
 *
 * <AUTHOR>
 * @date 2022-12-08
 */
@Data
@ExcelIgnoreUnannotated
public class AppUserVo {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long id;

    /**
     * 用户账号
     */
    @ExcelProperty(value = "用户账号")
    private String userName;

    /**
     * 用户昵称
     */
    @ExcelProperty(value = "用户昵称")
    private String nickName;

    /**
     * 用户邮箱
     */
    @ExcelProperty(value = "用户邮箱")
    private String email;

    /**
     * 手机号码
     */
    @ExcelProperty(value = "手机号码")
    private String phone;

    /**
     * 环信uuid
     */
    @ExcelProperty(value = "环信uuid")
    private String uuid;

    /**
     * 用户性别（0=男 1=女 2=未知）
     */
    @ExcelProperty(value = "用户性别", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0==男,1==女,2==未知")
    private String sex;

    /**
     * 身份证号码
     */
    @ExcelProperty(value = "身份证号码")
    private String code;

    /**
     * 身份证姓名
     */
    @ExcelProperty(value = "身份证姓名")
    private String codeName;

    /**
     * 邀请码
     */
    @ExcelProperty(value = "邀请码")
    private String inviteCode;

    /**
     * 父级ID
     */
    @ExcelProperty(value = "父级ID")
    private Long parentId;

    /**
     * 头像地址
     */
    @ExcelProperty(value = "头像地址")
    private String avatar;

    /**
     * 生活照片地址
     */
    @ExcelProperty(value = "生活照片地址")
    private String photo;

    /**
     * 多张生活照片信息
     */
    private List<AppUserImageVo> photos;

    /**
     * 个性签名
     */
    @ExcelProperty(value = "个性签名")
    private String signature;

    /**
     * 密码
     */
    @ExcelProperty(value = "密码")
    private String password;

    /**
     * 职业
     */
    @ExcelProperty(value = "职业")
    private String occupation;

    /**
     * 学校
     */
    @ExcelProperty(value = "学校")
    private String school;

    /**
     * 出生年月
     */
    @ExcelProperty(value = "出生年月")
    private Long birthday;

    /**
     * 身高(cm)
     */
    @ExcelProperty(value = "身高(cm)")
    private Integer height;

    /**
     * 纬度
     */
    private Double latitude;
    /**
     * 经度
     */
    private Double longitude;

    /**
     * 帐号类型(0=用户注册，1=自动注册)
     */
    private String register;

    /**
     * 帐号状态（0=正常 1=冻结）
     */
    @ExcelProperty(value = "帐号状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0==正常,1==冻结")
    private String status;

    /**
     * 帐号状态（0=不在线 1=在线 ）
     */
    private String onlineStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后登录IP
     */
    @ExcelProperty(value = "最后登录IP")
    private String loginIp;

    /**
     * 最后登录时间
     */
    @ExcelProperty(value = "最后登录时间")
    private Date loginDate;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 环信的用户名
     */
    private String chatName;

    /**
     * 用户是否vip
     */
    private Boolean vip;

    /**
     * vip的结束时间
     */
    private String vipTime;

    /**
     * 星座信息
     */
    private String constellation;

    /**
     * 排序时间
     */
    private Date sortTime;

    /**
     * 对方用户和我的关系("1" = 喜欢，"0" = 超级喜欢)
     */
    private String isLikeRelation;

    /**
     * 群组身份信息
     */
    private AppGroupUserVo appGroupUserVo;

    /**
     * 实名认证状态
     * false= 没有实名，ture = 实名用户
     */
    private Boolean isRealName;

    /**
     * 用户的积分
     */
    private BigDecimal integral;

    /**
     * vipId
     */
    private String vipId;

    /**
     * vip过期时间
     */
    private Long vipExpireTime;


    private Long bookId;

    /**
     * 用户语言
     */
    private String userLanguage;

    /**
     * 国家
     */
    private String userCountry;

    /**
     * 区号
     */
    private String areaCode;

    /**
     * 收藏状态
     */
    private Boolean collectStatus;

}
