package com.ruoyi.app.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ruoyi.common.annotation.ExcelDictFormat;
import com.ruoyi.common.convert.ExcelDictConvert;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;


/**
 * APP礼物记录视图对象 app_gift_order
 *
 * <AUTHOR>
 * @date 2025-12-21
 */
@Data
@ExcelIgnoreUnannotated
public class AppGiftOrderVo {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 赠送用户ID
     */
    @ExcelProperty(value = "赠送用户ID")
    private Long toUserId;

    /**
     * 礼物名称
     */
    @ExcelProperty(value = "礼物名称")
    private String name;

    /**
     * 礼物ID
     */
    @ExcelProperty(value = "礼物ID")
    private Long giftId;


    private BigDecimal amount;

    /**
     * 礼物数量
     */
    private Integer number;
    /**
     * 1=礼物
     */
    @ExcelProperty(value = "1=礼物")
    private Integer type;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    private String image;

    private String userName;

    private String toUserName;


}
