package com.ruoyi.app.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ruoyi.common.annotation.ExcelDictFormat;
import com.ruoyi.common.convert.ExcelDictConvert;
import lombok.Data;

import java.util.Date;
import java.util.HashMap;


/**
 * APP用户匹配配置和记录视图对象 app_mate
 *
 * <AUTHOR>
 * @date 2023-05-16
 */
@Data
@ExcelIgnoreUnannotated
public class AppMateVo {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 是否匹配在线
     */
    @ExcelProperty(value = "是否匹配在线")
    private String onLine;

    /**
     * 是否使用匹配功能
     */
    @ExcelProperty(value = "是否使用匹配功能")
    private String useMate;

    /**
     * 是否距离最近
     */
    @ExcelProperty(value = "是否距离最近")
    private String distance;

    /**
     * 是否信息完善
     */
    @ExcelProperty(value = "是否信息完善")
    private String infoComplete;

    /**
     * 是否提示
     */
    private String remind;

    /**
     * 操作类型(1=匹配)
     */
    @ExcelProperty(value = "操作类型(1=匹配)")
    private String type;

    /**
     * 操作时间
     */
    @ExcelProperty(value = "操作时间")
    private Long recordTime;

    /**
     * 文案信息
     */
    private HashMap<String, String> copywriter;


}
