package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP用户动态话题类型对象 app_dynamic_type
 *
 * <AUTHOR>
 * @date 2025-05-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_dynamic_type")
public class AppDynamicType extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 动态标签名称
     */
    private String name;
    /**
     * 动态标签描述
     */
    private String introduce;
    /**
     * 动态标签图片
     */
    private String image;
    /**
     * 动态标签类型(1=用户话题,0=系统话题,2=活动话题)
     */
    private String type;

    /**
     * 话题热度
     */
    private Integer heat;

    /**
     * 备注
     */
    private String remark;
    /**
     * 删除标志（0=存在,2=删除）
     */
    @TableLogic
    private String delFlag;


    private String status;

}
