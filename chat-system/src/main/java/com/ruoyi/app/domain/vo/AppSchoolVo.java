package com.ruoyi.app.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;



/**
 * APP学校信息视图对象 app_school
 *
 * <AUTHOR>
 * @date 2023-01-28
 */
@Data
@ExcelIgnoreUnannotated
public class AppSchoolVo {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 学校名称
     */
    @ExcelProperty(value = "学校名称")
    private String name;

    /**
     * 状态(0=生效，1=不生效)
     */
    @ExcelProperty(value = "状态(0=生效，1=不生效)")
    private String status;


}
