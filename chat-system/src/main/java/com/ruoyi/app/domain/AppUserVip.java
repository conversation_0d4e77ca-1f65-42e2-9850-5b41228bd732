package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP用户VIP对象 app_user_vip
 *
 * <AUTHOR>
 * @date 2023-03-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_user_vip")
public class AppUserVip extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * VIP结束时间
     */
    private Long endTime;

    /**
     * 当前等级
     */
    private Integer level;

    /**
     * 当前经验
     */
    private Integer experience;

    /**
     * VIP的类型
     */
    private String type;

    /**
     * 删除标志（0=存在,2=删除）
     */
    @TableLogic
    private String delFlag;
    /**
     * 备注
     */
    private String remark;

    /**
     * VIP的ID
     */
    private Long vipId;

}
