package com.ruoyi.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.app.domain.AppFriend;
import com.ruoyi.app.domain.bo.AppFriendBo;
import com.ruoyi.app.mapper.AppFriendMapper;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ruoyi.app.domain.bo.AppAddressBookBo;
import com.ruoyi.app.domain.vo.AppAddressBookVo;
import com.ruoyi.app.domain.AppAddressBook;
import com.ruoyi.app.mapper.AppAddressBookMapper;
import com.ruoyi.app.service.IAppAddressBookService;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Collection;
import java.util.Objects;

/**
 * APP用户的通讯录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-12-26
 */
@RequiredArgsConstructor
@Service
public class AppAddressBookServiceImpl implements IAppAddressBookService {

    private final AppAddressBookMapper baseMapper;
    private final AppFriendMapper appFriendMapper;

    /**
     * 查询APP用户的通讯录
     */
    @Override
    public AppAddressBookVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 根据用户ID
     *
     * @param userId 用户ID
     * @param bookId 通讯录好友ID
     * @return
     */
    @Override
    public AppAddressBookVo queryBook(Long userId, Long bookId) {
        LambdaQueryWrapper<AppAddressBook> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AppAddressBook::getUserId, userId).eq(AppAddressBook::getBookId, bookId);
        return baseMapper.selectVoOne(wrapper);
    }

    /**
     * 查询APP用户的通讯录列表
     */
    @Override
    public TableDataInfo<AppAddressBookVo> queryPageList(AppAddressBookBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AppAddressBook> lqw = buildQueryWrapper(bo);
        Page<AppAddressBookVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询APP用户的通讯录列表
     */
    @Override
    public List<AppAddressBookVo> queryList(AppAddressBookBo bo) {
        LambdaQueryWrapper<AppAddressBook> lqw = buildQueryWrapper(bo);
        lqw.orderByDesc(AppAddressBook::getCreateTime);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AppAddressBook> buildQueryWrapper(AppAddressBookBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AppAddressBook> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, AppAddressBook::getUserId, bo.getUserId());
        lqw.eq(bo.getBookId() != null, AppAddressBook::getBookId, bo.getBookId());
        lqw.like(StringUtils.isNotBlank(bo.getRemark()), AppAddressBook::getRemark, bo.getRemark());
        return lqw;
    }

    /**
     * 新增APP用户的通讯录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(AppAddressBookBo bo) {
        AppAddressBook add = BeanUtil.toBean(bo, AppAddressBook.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createFriend(Long userId, Long friendId) {
        //添加双方通讯录信息
        if (Objects.isNull(queryBook(userId, friendId))) {
            AppAddressBookBo userBook = new AppAddressBookBo();
            userBook.setUserId(userId);
            userBook.setBookId(friendId);
            insertByBo(userBook);
        }
        if (Objects.isNull(queryBook(friendId, userId))) {
            AppAddressBookBo friendBook = new AppAddressBookBo();
            friendBook.setUserId(friendId);
            friendBook.setBookId(userId);
            insertByBo(friendBook);
        }
        return true;
    }

    @Override
    public Boolean cancel(Long userId, Long friendId) {
        return null;
    }

    /**
     * 修改APP用户的通讯录
     */
    @Override
    public Boolean updateByBo(AppAddressBookBo bo) {
        AppAddressBook update = BeanUtil.toBean(bo, AppAddressBook.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    @Override
    public Boolean updateByUserIdAndRemark(Long id, Long userId, String remark) {
        LambdaUpdateWrapper<AppAddressBook> update = new LambdaUpdateWrapper<>();
        update.eq(AppAddressBook::getId, id).eq(AppAddressBook::getUserId, userId).set(AppAddressBook::getRemark, remark);
        return baseMapper.update(null, update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AppAddressBook entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除APP用户的通讯录
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
