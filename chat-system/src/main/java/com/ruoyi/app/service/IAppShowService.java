package com.ruoyi.app.service;

import com.ruoyi.app.domain.bo.AppShowBo;
import com.ruoyi.app.domain.vo.AppShowVo;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 活动展馆管理Service接口
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
public interface IAppShowService {

    /**
     * 查询活动展馆管理
     */
    AppShowVo queryById(Long id);

    /**
     * 查询活动展馆管理列表
     */
    TableDataInfo<AppShowVo> queryPageList(AppShowBo bo, PageQuery pageQuery);

    /**
     * 查询活动展馆管理列表
     */
    List<AppShowVo> queryList(AppShowBo bo);

    /**
     * 新增活动展馆管理
     */
    Boolean insertByBo(AppShowBo bo);

    /**
     * 修改活动展馆管理
     */
    Boolean updateByBo(AppShowBo bo);

    /**
     * 校验并批量删除活动展馆管理信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
