package com.ruoyi.app.service;

import com.ruoyi.app.domain.AppDynamicType;
import com.ruoyi.app.domain.vo.AppDynamicTypeVo;
import com.ruoyi.app.domain.bo.AppDynamicTypeBo;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * APP用户动态话题类型Service接口
 *
 * <AUTHOR>
 * @date 2025-05-18
 */
public interface IAppDynamicTypeService {

    /**
     * 查询APP用户动态话题类型
     */
    AppDynamicTypeVo queryById(Long id);

    /**
     * 查询APP用户动态话题类型
     */
    AppDynamicType queryByName(String name);

    /**
     * 查询APP用户动态话题类型列表
     */
    TableDataInfo<AppDynamicTypeVo> queryPageList(AppDynamicTypeBo bo, PageQuery pageQuery);

    /**
     * 查询APP用户动态话题类型列表
     */
    List<AppDynamicTypeVo> queryList(AppDynamicTypeBo bo);

    /**
     * 新增APP用户动态话题类型
     */
    Boolean insertByBo(AppDynamicTypeBo bo);


    /**
     * 新增APP用户动态话题类型
     */
    Boolean insert(AppDynamicType appDynamicType);

    /**
     * 修改APP用户动态话题类型
     */
    Boolean updateByBo(AppDynamicTypeBo bo);

    Boolean update(AppDynamicType dynamicType);

    /**
     * 校验并批量删除APP用户动态话题类型信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);


}
