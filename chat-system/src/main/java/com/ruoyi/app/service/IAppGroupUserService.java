package com.ruoyi.app.service;

import com.ruoyi.app.domain.AppGroupUser;
import com.ruoyi.app.domain.vo.AppGroupUserVo;
import com.ruoyi.app.domain.bo.AppGroupUserBo;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * APP群组Service接口
 *
 * <AUTHOR>
 * @date 2025-01-29
 */
public interface IAppGroupUserService {

    /**
     * 查询APP群组
     */
    AppGroupUserVo queryById(Long id);

    /**
     * 根据群组ID和身份查询用户
     *
     * @param groupId 群组ID
     * @param place   身份[群主1个，管理员和成员多个]
     * @return
     */
    List<AppGroupUserVo> queryByGroupAndPlace(Long groupId, String place);


    /**
     * 根据群组ID查询群主信息
     *
     * @param groupId 群组ID
     * @return
     */
    AppGroupUserVo queryByGroupMaster(Long groupId);

    /**
     * 查询用户在群组的身份
     *
     * @param groupId 群组ID
     * @param groupId 用户ID
     * @return
     */
    AppGroupUserVo queryByGroupAndUserId(Long groupId, Long userId);

    /**
     * 查询APP群组列表
     */
    TableDataInfo<AppGroupUserVo> queryPageList(AppGroupUserBo bo, PageQuery pageQuery);

    /**
     * 查询APP群组列表
     */
    List<AppGroupUserVo> queryList(AppGroupUserBo bo);


    /**
     * 获取群主外的用户
     */
    AppGroupUserVo queryUser(Long groupId);

    /**
     * 查询APP群组成员列表
     */
    List<AppGroupUserVo> queryList(Long groupId);

    /**
     * 新增APP群组
     */
    Boolean insertByBo(AppGroupUserBo bo);

    /**
     * 修改APP群组
     */
    Boolean updateByBo(AppGroupUserBo bo);

    /**
     * 修改APP群组成员信息
     */
    Boolean updateByVo(AppGroupUserVo vo);


    Boolean updateMaster(AppGroupUserVo vo, AppGroupUserVo master);


    /**
     * 校验并批量删除APP群组信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 删除群组的用户
     *
     * @param groupId 群组ID
     * @param userId  用户ID
     * @return
     */
    int removeUser(Long groupId, Long userId, Integer sign, Long nextId);

    /**
     * 删除群组的用户
     *
     * @param groupId 群组ID
     * @param userId  用户ID
     * @return
     */
    int removeUser(Long groupId, Long userId);

    /**
     * 删除群组的用户
     *
     * @param groupId   群组ID
     * @param usernames 用户IDS
     * @return
     */
    Boolean removeUser(Long groupId, List<String> usernames);
}
