package com.ruoyi.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.constant.MessageConstants;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ruoyi.app.domain.bo.AppNoticeBo;
import com.ruoyi.app.domain.vo.AppNoticeVo;
import com.ruoyi.app.domain.AppNotice;
import com.ruoyi.app.mapper.AppNoticeMapper;
import com.ruoyi.app.service.IAppNoticeService;

import java.awt.*;
import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * APP用户通知信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-12-27
 */
@RequiredArgsConstructor
@Service
public class AppNoticeServiceImpl implements IAppNoticeService {

    private final AppNoticeMapper baseMapper;

    /**
     * 查询APP用户通知信息
     */
    @Override
    public AppNoticeVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询APP用户通知信息列表
     */
    @Override
    public TableDataInfo<AppNoticeVo> queryPageList(AppNoticeBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AppNotice> lqw = buildQueryWrapper(bo);
        Page<AppNoticeVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    @Override
    public TableDataInfo<AppNoticeVo> queryPageList(Long userId, List<String> types, PageQuery pageQuery) {
        LambdaUpdateWrapper<AppNotice> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(AppNotice::getUserId, userId);
        wrapper.in(AppNotice::getType, types);
        wrapper.orderByDesc(AppNotice::getCreateTime);
        Page<AppNoticeVo> result = baseMapper.selectVoPage(pageQuery.build(), wrapper);

        List<AppNoticeVo> appNoticeVos = baseMapper.selectVoList(wrapper);
        appNoticeVos.forEach(e -> updateByStatus(e.getId()));
        return TableDataInfo.build(result);
    }

    @Override
    public List<AppNoticeVo> queryList(Long userId, List<String> types, String read) {
        LambdaUpdateWrapper<AppNotice> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(AppNotice::getUserId, userId);
        wrapper.in(AppNotice::getType, types);
        wrapper.eq(StringUtils.isNotBlank(read), AppNotice::getStatusRead, read);
        return baseMapper.selectVoList(wrapper);
    }


    /**
     * 查询APP用户通知信息列表
     */
    @Override
    public List<AppNoticeVo> queryList(AppNoticeBo bo) {
        LambdaQueryWrapper<AppNotice> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AppNotice> buildQueryWrapper(AppNoticeBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AppNotice> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getTitle()), AppNotice::getTitle, bo.getTitle());
        lqw.eq(StringUtils.isNotBlank(bo.getContent()), AppNotice::getContent, bo.getContent());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), AppNotice::getType, bo.getType());
        lqw.eq(bo.getUserId() != null, AppNotice::getUserId, bo.getUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getStatusRead()), AppNotice::getStatusRead, bo.getStatusRead());
        return lqw;
    }

    /**
     * 新增APP用户通知信息
     */
    @Override
    public Boolean insertByBo(AppNoticeBo bo) {
        AppNotice add = BeanUtil.toBean(bo, AppNotice.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 新增APP用户通知信息
     */
    @Override
    public Boolean inserts(List<AppNotice> appNotices) {
        return baseMapper.insertBatch(appNotices);
    }

    /**
     * 修改APP用户通知信息
     */
    @Override
    public Boolean updateByBo(AppNoticeBo bo) {
        AppNotice update = BeanUtil.toBean(bo, AppNotice.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    @Override
    public Boolean updateByStatus(Long id) {
        return baseMapper.update(null,
            new LambdaUpdateWrapper<AppNotice>()
                .set(AppNotice::getStatusRead, "1")
                .eq(AppNotice::getId, id)) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AppNotice entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除APP用户通知信息
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public Boolean delete(Long id) {
        return baseMapper.deleteById(id) > 0;
    }
}
