package com.ruoyi.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ruoyi.app.domain.bo.AppGiftOrderBo;
import com.ruoyi.app.domain.vo.AppGiftOrderVo;
import com.ruoyi.app.domain.AppGiftOrder;
import com.ruoyi.app.mapper.AppGiftOrderMapper;
import com.ruoyi.app.service.IAppGiftOrderService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * APP礼物记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-12-21
 */
@RequiredArgsConstructor
@Service
public class AppGiftOrderServiceImpl implements IAppGiftOrderService {

    private final AppGiftOrderMapper baseMapper;

    /**
     * 查询APP礼物记录
     */
    @Override
    public AppGiftOrderVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询APP礼物记录列表
     */
    @Override
    public TableDataInfo<AppGiftOrderVo> queryPageList(AppGiftOrderBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AppGiftOrder> lqw = buildQueryWrapper(bo);
        Page<AppGiftOrderVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询APP礼物记录列表
     */
    @Override
    public List<AppGiftOrderVo> queryList(AppGiftOrderBo bo) {
        LambdaQueryWrapper<AppGiftOrder> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AppGiftOrder> buildQueryWrapper(AppGiftOrderBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AppGiftOrder> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, AppGiftOrder::getUserId, bo.getUserId());
        lqw.eq(bo.getToUserId() != null, AppGiftOrder::getToUserId, bo.getToUserId());
        lqw.like(StringUtils.isNotBlank(bo.getName()), AppGiftOrder::getName, bo.getName());
        lqw.eq(bo.getGiftId() != null, AppGiftOrder::getGiftId, bo.getGiftId());
        lqw.eq(bo.getAmount() != null, AppGiftOrder::getAmount, bo.getAmount());
        lqw.eq(bo.getType() != null, AppGiftOrder::getType, bo.getType());
        return lqw;
    }

    /**
     * 新增APP礼物记录
     */
    @Override
    public Boolean insertByBo(AppGiftOrderBo bo) {
        AppGiftOrder add = BeanUtil.toBean(bo, AppGiftOrder.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改APP礼物记录
     */
    @Override
    public Boolean updateByBo(AppGiftOrderBo bo) {
        AppGiftOrder update = BeanUtil.toBean(bo, AppGiftOrder.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AppGiftOrder entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除APP礼物记录
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
