package com.ruoyi.app.service;

import com.ruoyi.app.domain.bo.AppLabelBo;
import com.ruoyi.app.domain.vo.AppLabelVo;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;

/**
 * APP标签Service接口
 *
 * <AUTHOR>
 * @date 2023-01-09
 */
public interface IAppLabelService {

    /**
     * 查询APP标签
     */
    AppLabelVo queryById(Long id);

    /**
     * 查询APP标签
     */
    AppLabelVo queryByName(String name);

    /**
     * 查询APP标签列表
     */
    TableDataInfo<AppLabelVo> queryPageList(AppLabelBo bo, PageQuery pageQuery);

    /**
     * 查询APP标签列表
     */
    List<AppLabelVo> queryList(AppLabelBo bo);

    /**
     * 新增APP标签
     */
    Boolean insertByBo(AppLabelBo bo);

    /**
     * 新增标签，同时用户加入标签
     *
     * @param bo 标签对象
     * @return
     */
    Boolean createLabel(AppLabelBo bo);

    /**
     * 修改APP标签
     */
    Boolean updateByBo(AppLabelBo bo);

    /**
     * 校验并批量删除APP标签信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据类型查询标签
     */
    List<AppLabelVo> queryListType(AppLabelBo bo,Long userId);
}
