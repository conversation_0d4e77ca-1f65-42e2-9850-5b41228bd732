package com.ruoyi.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ruoyi.app.domain.bo.AppVersionBo;
import com.ruoyi.app.domain.vo.AppVersionVo;
import com.ruoyi.app.domain.AppVersion;
import com.ruoyi.app.mapper.AppVersionMapper;
import com.ruoyi.app.service.IAppVersionService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * APP版本更新记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-04-11
 */
@RequiredArgsConstructor
@Service
public class AppVersionServiceImpl implements IAppVersionService {

    private final AppVersionMapper baseMapper;

    /**
     * 查询APP版本更新记录
     */
    @Override
    public AppVersionVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询APP版本更新记录列表
     */
    @Override
    public TableDataInfo<AppVersionVo> queryPageList(AppVersionBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AppVersion> lqw = buildQueryWrapper(bo);
        Page<AppVersionVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询APP版本更新记录列表
     */
    @Override
    public List<AppVersionVo> queryList(AppVersionBo bo) {
        LambdaQueryWrapper<AppVersion> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 查询APP版本更新记录列表
     */
    @Override
    public AppVersionVo lastOne(AppVersionBo bo) {
        LambdaQueryWrapper<AppVersion> lqw = buildQueryWrapper(bo);
        lqw.last("limit 1");
        return baseMapper.selectVoOne(lqw);
    }

    private LambdaQueryWrapper<AppVersion> buildQueryWrapper(AppVersionBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AppVersion> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getTitle()), AppVersion::getTitle, bo.getTitle());
        lqw.eq(StringUtils.isNotBlank(bo.getContext()), AppVersion::getContext, bo.getContext());
        lqw.eq(StringUtils.isNotBlank(bo.getIosVersion()), AppVersion::getIosVersion, bo.getIosVersion());
        lqw.eq(StringUtils.isNotBlank(bo.getAndroidVersion()), AppVersion::getAndroidVersion, bo.getAndroidVersion());
        lqw.eq(StringUtils.isNotBlank(bo.getForceUpdate()), AppVersion::getForceUpdate, bo.getForceUpdate());
        lqw.orderByDesc(AppVersion::getCreateTime);
        return lqw;
    }

    /**
     * 新增APP版本更新记录
     */
    @Override
    public Boolean insertByBo(AppVersionBo bo) {
        AppVersion add = BeanUtil.toBean(bo, AppVersion.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改APP版本更新记录
     */
    @Override
    public Boolean updateByBo(AppVersionBo bo) {
        AppVersion update = BeanUtil.toBean(bo, AppVersion.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AppVersion entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除APP版本更新记录
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
