package com.ruoyi.app.service;

import com.ruoyi.app.domain.bo.AppSchoolBo;
import com.ruoyi.app.domain.vo.AppSchoolVo;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * APP学校信息Service接口
 *
 * <AUTHOR>
 * @date 2023-01-28
 */
public interface IAppSchoolService {

    /**
     * 查询APP学校信息
     */
    AppSchoolVo queryById(Long id);

    /**
     * 查询APP学校信息列表
     */
    TableDataInfo<AppSchoolVo> queryPageList(AppSchoolBo bo, PageQuery pageQuery);

    /**
     * 查询APP学校信息列表
     */
    List<AppSchoolVo> queryList(AppSchoolBo bo);

    /**
     * 新增APP学校信息
     */
    Boolean insertByBo(AppSchoolBo bo);

    /**
     * 修改APP学校信息
     */
    Boolean updateByBo(AppSchoolBo bo);

    /**
     * 校验并批量删除APP学校信息信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
