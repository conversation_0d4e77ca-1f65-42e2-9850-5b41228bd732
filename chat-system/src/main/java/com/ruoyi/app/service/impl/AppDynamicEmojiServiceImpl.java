package com.ruoyi.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ruoyi.app.domain.bo.AppDynamicEmojiBo;
import com.ruoyi.app.domain.vo.AppDynamicEmojiVo;
import com.ruoyi.app.domain.AppDynamicEmoji;
import com.ruoyi.app.mapper.AppDynamicEmojiMapper;
import com.ruoyi.app.service.IAppDynamicEmojiService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 动态表情Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@RequiredArgsConstructor
@Service
public class AppDynamicEmojiServiceImpl implements IAppDynamicEmojiService {

    private final AppDynamicEmojiMapper baseMapper;

    /**
     * 查询动态表情
     */
    @Override
    public AppDynamicEmojiVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询动态表情列表
     */
    @Override
    public TableDataInfo<AppDynamicEmojiVo> queryPageList(AppDynamicEmojiBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AppDynamicEmoji> lqw = buildQueryWrapper(bo);
        Page<AppDynamicEmojiVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询动态表情列表
     */
    @Override
    public List<AppDynamicEmojiVo> queryList(AppDynamicEmojiBo bo) {
        LambdaQueryWrapper<AppDynamicEmoji> lqw = buildQueryWrapper(bo);
        lqw.orderByAsc(AppDynamicEmoji::getSort);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AppDynamicEmoji> buildQueryWrapper(AppDynamicEmojiBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AppDynamicEmoji> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getEmojiName()), AppDynamicEmoji::getEmojiName, bo.getEmojiName());
        return lqw;
    }

    /**
     * 新增动态表情
     */
    @Override
    public Boolean insertByBo(AppDynamicEmojiBo bo) {
        AppDynamicEmoji add = BeanUtil.toBean(bo, AppDynamicEmoji.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改动态表情
     */
    @Override
    public Boolean updateByBo(AppDynamicEmojiBo bo) {
        AppDynamicEmoji update = BeanUtil.toBean(bo, AppDynamicEmoji.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AppDynamicEmoji entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除动态表情
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
