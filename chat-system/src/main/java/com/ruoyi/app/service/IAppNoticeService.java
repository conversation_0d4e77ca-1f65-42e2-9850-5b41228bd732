package com.ruoyi.app.service;

import com.ruoyi.app.domain.AppNotice;
import com.ruoyi.app.domain.bo.AppNoticeBo;
import com.ruoyi.app.domain.vo.AppNoticeVo;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * APP用户通知信息Service接口
 *
 * <AUTHOR>
 * @date 2025-12-27
 */
public interface IAppNoticeService {

    /**
     * 查询APP用户通知信息
     */
    AppNoticeVo queryById(Long id);

    /**
     * 查询APP用户通知信息列表
     */
    TableDataInfo<AppNoticeVo> queryPageList(AppNoticeBo bo, PageQuery pageQuery);
    TableDataInfo<AppNoticeVo> queryPageList(Long userId,List<String> types,PageQuery pageQuery);
    List<AppNoticeVo> queryList(Long userId,List<String> types,String read);

    /**
     * 查询APP用户通知信息列表
     */
    List<AppNoticeVo> queryList(AppNoticeBo bo);

    /**
     * 新增APP用户通知信息
     */
    Boolean insertByBo(AppNoticeBo bo);


    /**
     * 匹配新增
     * @param appNotices
     * @return
     */
    Boolean inserts(List<AppNotice> appNotices);

    /**
     * 修改APP用户通知信息
     */
    Boolean updateByBo(AppNoticeBo bo);

    /**
     * 修改APP用户通知信息
     */
    Boolean updateByStatus(Long id);

    /**
     * 校验并批量删除APP用户通知信息信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 删除通知记录
     * @param id 记录ID
     * @return
     */
    Boolean delete(Long id);
}
