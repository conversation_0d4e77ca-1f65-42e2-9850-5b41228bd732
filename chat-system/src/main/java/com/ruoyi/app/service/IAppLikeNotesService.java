package com.ruoyi.app.service;

import com.ruoyi.app.domain.AppLikeNotes;
import com.ruoyi.app.domain.vo.AppLikeNotesVo;
import com.ruoyi.app.domain.bo.AppLikeNotesBo;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * APP用户喜欢记录Service接口
 *
 * <AUTHOR>
 * @date 2025-02-14
 */
public interface IAppLikeNotesService {

    /**
     * 查询APP用户喜欢记录
     */
    AppLikeNotesVo queryById(Long id);

    /**
     * 查询APP用户喜欢记录列表
     */
    TableDataInfo<AppLikeNotesVo> queryPageList(AppLikeNotesBo bo, PageQuery pageQuery);

    /**
     * 查询APP用户喜欢记录列表
     */
    List<AppLikeNotesVo> queryList(AppLikeNotesBo bo);

    /**
     * 查询APP用户喜欢记录列表
     */
    List<AppLikeNotes> list(AppLikeNotesBo bo);

    /**
     * 查询两个用户的关系
     */
    AppLikeNotesVo queryUser(Long userId, Long sideId, String status);


    /**
     * 查询两个用户的关系
     */
    Boolean queryBlock(Long userId, Long sideId);



    AppLikeNotesVo queryDynamic(Long userId, Long dynamicId);

    /**
     * 查询两个用户的关系
     */
    AppLikeNotesVo queryUser(Long userId, Long sideId, List<String> status);


    /**
     * 查询两个用户的记录
     */
    String queryRelation(Long userId, Long sideId);

    /**
     * 新增APP用户喜欢记录
     */
    Boolean insertByBo(AppLikeNotesBo bo);

    /**
     * 修改APP用户喜欢记录
     */
    Boolean updateByBo(AppLikeNotesBo bo);


    Boolean updateByVo(AppLikeNotesVo vo);

    /**
     * 校验并批量删除APP用户喜欢记录信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);


    /**
     * 根据ID删除
     */
    Boolean deleteById(Long id);

    /**
     * 根据用户ID和类型删除用户的历史话题
     */
    Boolean deleteByType(Long userId,String type);

    Boolean updates(List<AppLikeNotes> readList);
}
