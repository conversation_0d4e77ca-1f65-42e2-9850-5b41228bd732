package com.ruoyi.app.service;

import com.ruoyi.app.domain.bo.AppUserNameBo;
import com.ruoyi.app.domain.vo.AppUserNameVo;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 【请填写功能名称】Service接口
 *
 * <AUTHOR>
 * @date 2023-03-09
 */
public interface IAppUserNameService {

    /**
     * 查询【请填写功能名称】
     */
    AppUserNameVo queryById(Long id);

    /**
     * 查询【请填写功能名称】列表
     */
    TableDataInfo<AppUserNameVo> queryPageList(AppUserNameBo bo, PageQuery pageQuery);

    /**
     * 查询【请填写功能名称】列表
     */
    List<AppUserNameVo> queryList(AppUserNameBo bo);

    /**
     * 新增【请填写功能名称】
     */
    Boolean insertByBo(AppUserNameBo bo);

    /**
     * 修改【请填写功能名称】
     */
    Boolean updateByBo(AppUserNameBo bo);

    /**
     * 校验并批量删除【请填写功能名称】信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
