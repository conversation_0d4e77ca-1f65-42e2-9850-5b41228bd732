package com.ruoyi.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.app.domain.AppHongbao;
import com.ruoyi.app.domain.bo.AppHongbaoBo;
import com.ruoyi.app.domain.vo.AppHongbaoVo;
import com.ruoyi.app.mapper.AppHongbaoMapper;
import com.ruoyi.app.service.IAppAssetService;
import com.ruoyi.app.service.IAppDetailService;
import com.ruoyi.app.service.IAppHongbaoService;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BillType;
import com.ruoyi.common.enums.CoinType;
import com.ruoyi.common.enums.DetailType;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * APP红包Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@RequiredArgsConstructor
@Service
public class AppHongbaoServiceImpl implements IAppHongbaoService {

    private final AppHongbaoMapper baseMapper;

    /**
     * 查询APP红包
     */
    @Override
    public AppHongbaoVo queryById(Long hongbaoId) {
        return baseMapper.selectVoById(hongbaoId);
    }

    /**
     * 查询APP红包列表
     */
    @Override
    public TableDataInfo<AppHongbaoVo> queryPageList(AppHongbaoBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AppHongbao> lqw = buildQueryWrapper(bo);
        Page<AppHongbaoVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询APP红包列表
     */
    @Override
    public List<AppHongbaoVo> queryList(AppHongbaoBo bo) {
        LambdaQueryWrapper<AppHongbao> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AppHongbao> buildQueryWrapper(AppHongbaoBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AppHongbao> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getHongbaoName()), AppHongbao::getHongbaoName, bo.getHongbaoName());
        lqw.eq(bo.getUserId() != null, AppHongbao::getUserId, bo.getUserId());
        lqw.eq(bo.getSendUser() != null, AppHongbao::getSendUser, bo.getSendUser());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), AppHongbao::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增APP红包
     */
    @Override
    public Boolean insertByBo(AppHongbaoBo bo) {
        AppHongbao add = BeanUtil.toBean(bo, AppHongbao.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setHongbaoId(add.getHongbaoId());
        }
        return flag;
    }

    /**
     * 修改APP红包
     */
    @Override
    public Boolean updateByBo(AppHongbaoBo bo) {
        AppHongbao update = BeanUtil.toBean(bo, AppHongbao.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }


    /**
     * 修改APP红包
     */
    @Override
    public Boolean updateByVo(AppHongbaoVo bo) {
        AppHongbao update = BeanUtil.toBean(bo, AppHongbao.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }


    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AppHongbao entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除APP红包
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    private final IAppAssetService appAssetService;
    private final IAppDetailService appDetailService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public AppHongbaoBo sendHongbao(AppHongbaoBo bo, BigDecimal amount) {
        Boolean result = appAssetService.subtractBalance(bo.getUserId(), amount);
        if (result && insertByBo(bo)) {
            appDetailService.insert(bo.getUserId(), amount, BigDecimal.ZERO, BillType.TO.getCode(), CoinType.GOLD.getCode(), DetailType.HONGBAO.getCode(), "发红包", null);
            return bo;
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AppHongbaoVo receiveHongbao(Long id, AppHongbaoVo hongbaoVo) {
        hongbaoVo.setStatus(Constants.FAIL);
        hongbaoVo.setReceiptTime(DateUtils.getNowDate());
        if (updateByVo(hongbaoVo)) {
            BigDecimal amount = hongbaoVo.getHongbaoMoney().multiply(BigDecimal.valueOf(hongbaoVo.getHongbaoNumber()));
            appAssetService.addBalance(id, amount);
            appDetailService.insert(id, amount, BigDecimal.ZERO, BillType.IN.getCode(), CoinType.GOLD.getCode(), DetailType.RECEIVE_HONGBAO.getCode(), "收红包", hongbaoVo.getUserId());
            return hongbaoVo;
        }
        return null;
    }
}
