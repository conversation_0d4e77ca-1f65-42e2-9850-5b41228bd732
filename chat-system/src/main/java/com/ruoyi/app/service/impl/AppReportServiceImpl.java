package com.ruoyi.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ruoyi.app.domain.bo.AppReportBo;
import com.ruoyi.app.domain.vo.AppReportVo;
import com.ruoyi.app.domain.AppReport;
import com.ruoyi.app.mapper.AppReportMapper;
import com.ruoyi.app.service.IAppReportService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * APP举报类型Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-03
 */
@RequiredArgsConstructor
@Service
public class AppReportServiceImpl implements IAppReportService {

    private final AppReportMapper baseMapper;

    /**
     * 查询APP举报类型
     */
    @Override
    public AppReportVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询APP举报类型列表
     */
    @Override
    public TableDataInfo<AppReportVo> queryPageList(AppReportBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AppReport> lqw = buildQueryWrapper(bo);
        Page<AppReportVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询APP举报类型列表
     */
    @Override
    public List<AppReportVo> queryList(AppReportBo bo) {
        LambdaQueryWrapper<AppReport> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AppReport> buildQueryWrapper(AppReportBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AppReport> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getContext()), AppReport::getContext, bo.getContext());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), AppReport::getType, bo.getType());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), AppReport::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增APP举报类型
     */
    @Override
    public Boolean insertByBo(AppReportBo bo) {
        AppReport add = BeanUtil.toBean(bo, AppReport.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改APP举报类型
     */
    @Override
    public Boolean updateByBo(AppReportBo bo) {
        AppReport update = BeanUtil.toBean(bo, AppReport.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AppReport entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除APP举报类型
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
