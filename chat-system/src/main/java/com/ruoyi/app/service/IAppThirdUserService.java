package com.ruoyi.app.service;

import com.ruoyi.app.domain.bo.AppThirdUserBo;
import com.ruoyi.app.domain.vo.AppThirdUserVo;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * APP用户第三方登录Service接口
 *
 * <AUTHOR>
 * @date 2023-01-07
 */
public interface IAppThirdUserService {

    /**
     * 查询APP用户第三方登录
     */
    AppThirdUserVo queryById(Long id);

    /**
     * 查询APP用户第三方登录列表
     */
    TableDataInfo<AppThirdUserVo> queryPageList(AppThirdUserBo bo, PageQuery pageQuery);

    /**
     * 查询APP用户第三方登录列表
     */
    List<AppThirdUserVo> queryList(AppThirdUserBo bo);

    /**
     * 查询APP用户第三方登录
     */
    AppThirdUserVo queryByTypeAndToken(String type,String token);

    /**
     * 查询APP用户第三方登录
     */
    AppThirdUserVo queryByTypeAndUserId(String type,Long userId);

    /**
     * 新增APP用户第三方登录
     */
    Boolean insertByBo(AppThirdUserBo bo);

    /**
     * 修改APP用户第三方登录
     */
    Boolean updateByBo(AppThirdUserBo bo);

    /**
     * 校验并批量删除APP用户第三方登录信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
