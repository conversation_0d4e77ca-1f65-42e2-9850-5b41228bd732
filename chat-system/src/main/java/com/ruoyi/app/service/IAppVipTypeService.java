package com.ruoyi.app.service;

import com.ruoyi.app.domain.AppVipType;
import com.ruoyi.app.domain.vo.AppVipTypeVo;
import com.ruoyi.app.domain.bo.AppVipTypeBo;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * APPVIP类型Service接口
 *
 * <AUTHOR>
 * @date 2023-03-16
 */
public interface IAppVipTypeService {

    /**
     * 查询APPVIP类型
     */
    AppVipTypeVo queryById(Long id);

    /**
     * 查询APPVIP类型列表
     */
    TableDataInfo<AppVipTypeVo> queryPageList(AppVipTypeBo bo, PageQuery pageQuery);

    /**
     * 查询APPVIP类型列表
     */
    List<AppVipTypeVo> queryList(AppVipTypeBo bo);

    /**
     * 新增APPVIP类型
     */
    Boolean insertByBo(AppVipTypeBo bo);

    /**
     * 修改APPVIP类型
     */
    Boolean updateByBo(AppVipTypeBo bo);

    /**
     * 校验并批量删除APPVIP类型信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
