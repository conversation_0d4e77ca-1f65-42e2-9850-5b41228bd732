package com.ruoyi.app.service;

import com.ruoyi.app.domain.AppLikeSign;
import com.ruoyi.app.domain.bo.AppLikeSignBo;
import com.ruoyi.app.domain.vo.AppLikeSignVo;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * APP超级喜欢的标记Service接口
 *
 * <AUTHOR>
 * @date 2025-02-22
 */
public interface IAppLikeSignService {

    /**
     * 查询APP超级喜欢的标记
     */
    AppLikeSignVo queryById(Long id);

    /**
     * 查询APP超级喜欢的标记列表
     */
    TableDataInfo<AppLikeSignVo> queryPageList(AppLikeSignBo bo, PageQuery pageQuery);

    /**
     * 查询APP超级喜欢的标记列表
     */
    List<AppLikeSignVo> queryList(AppLikeSignBo bo);


    AppLikeSign queryOne(Long userId, Long sideId);


    AppLikeSign queryLike(Long userId, Long sideId);

    /**
     * 查询APP超级喜欢的标记列表
     */
    List<AppLikeSign> list(AppLikeSignBo bo);

    /**
     * 新增APP超级喜欢的标记
     */
    Boolean insertByBo(AppLikeSignBo bo);

    /**
     * 修改APP超级喜欢的标记
     */
    Boolean updateByBo(AppLikeSignBo bo);

    /**
     * 修改APP超级喜欢的标记
     */
    Boolean update(AppLikeSign appLikeSign);

    /**
     * 修改APP超级喜欢的标记
     */
    Boolean update(List<AppLikeSign> appLikeSigns);


    /**
     * 校验并批量删除APP超级喜欢的标记信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
