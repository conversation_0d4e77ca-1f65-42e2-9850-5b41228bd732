package com.ruoyi.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ruoyi.app.domain.bo.AppBannerBo;
import com.ruoyi.app.domain.vo.AppBannerVo;
import com.ruoyi.app.domain.AppBanner;
import com.ruoyi.app.mapper.AppBannerMapper;
import com.ruoyi.app.service.IAppBannerService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * APP商城的轮播图Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-20
 */
@RequiredArgsConstructor
@Service
public class AppBannerServiceImpl implements IAppBannerService {

    private final AppBannerMapper baseMapper;

    /**
     * 查询APP商城的轮播图
     */
    @Override
    public AppBannerVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询APP商城的轮播图列表
     */
    @Override
    public TableDataInfo<AppBannerVo> queryPageList(AppBannerBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AppBanner> lqw = buildQueryWrapper(bo);
        Page<AppBannerVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询APP商城的轮播图列表
     */
    @Override
    public List<AppBannerVo> queryList(AppBannerBo bo) {
        LambdaQueryWrapper<AppBanner> lqw = buildQueryWrapper(bo);
        lqw.orderByDesc(AppBanner::getId);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AppBanner> buildQueryWrapper(AppBannerBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AppBanner> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getImage()), AppBanner::getImage, bo.getImage());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), AppBanner::getType, bo.getType());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), AppBanner::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增APP商城的轮播图
     */
    @Override
    public Boolean insertByBo(AppBannerBo bo) {
        AppBanner add = BeanUtil.toBean(bo, AppBanner.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改APP商城的轮播图
     */
    @Override
    public Boolean updateByBo(AppBannerBo bo) {
        AppBanner update = BeanUtil.toBean(bo, AppBanner.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AppBanner entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除APP商城的轮播图
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
