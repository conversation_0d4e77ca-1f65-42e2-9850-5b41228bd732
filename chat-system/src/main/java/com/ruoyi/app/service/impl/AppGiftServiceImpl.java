package com.ruoyi.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.app.domain.AppGift;
import com.ruoyi.app.domain.bo.AppGiftBo;
import com.ruoyi.app.domain.bo.AppGiftOrderBo;
import com.ruoyi.app.domain.vo.AppGiftVo;
import com.ruoyi.app.domain.vo.AppUserVipVo;
import com.ruoyi.app.domain.vo.AppVipTypeVo;
import com.ruoyi.app.mapper.AppGiftMapper;
import com.ruoyi.app.service.IAppAssetService;
import com.ruoyi.app.service.IAppGiftOrderService;
import com.ruoyi.app.service.IAppGiftService;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * APP礼物Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-12-14
 */
@RequiredArgsConstructor
@Service
public class AppGiftServiceImpl implements IAppGiftService {

    private final AppGiftMapper baseMapper;
    private final IAppAssetService appAssetService;
    private final IAppGiftOrderService appGiftOrderService;

    /**
     * 查询APP礼物
     */
    @Override
    public AppGiftVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询APP礼物列表
     */
    @Override
    public TableDataInfo<AppGiftVo> queryPageList(AppGiftBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AppGift> lqw = buildQueryWrapper(bo);
        Page<AppGiftVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询APP礼物列表
     */
    @Override
    public List<AppGiftVo> queryList(AppGiftBo bo) {
        LambdaQueryWrapper<AppGift> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AppGift> buildQueryWrapper(AppGiftBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AppGift> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getName()), AppGift::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getImage()), AppGift::getImage, bo.getImage());
        lqw.eq(StringUtils.isNotBlank(bo.getImageSvg()), AppGift::getImageSvg, bo.getImageSvg());
        lqw.eq(bo.getAmount() != null, AppGift::getAmount, bo.getAmount());
        lqw.eq(bo.getSort() != null, AppGift::getSort, bo.getSort());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), AppGift::getStatus, bo.getStatus());
        lqw.orderByAsc(AppGift::getSort);
        return lqw;
    }

    /**
     * 新增APP礼物
     */
    @Override
    public Boolean insertByBo(AppGiftBo bo) {
        AppGift add = BeanUtil.toBean(bo, AppGift.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改APP礼物
     */
    @Override
    public Boolean updateByBo(AppGiftBo bo) {
        AppGift update = BeanUtil.toBean(bo, AppGift.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AppGift entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除APP礼物
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean send(Long fromId, Long userId, AppGiftVo appGift, Integer number,BigDecimal price) {
        BigDecimal amount = price.multiply(BigDecimal.valueOf(number));
        if (appAssetService.subtractBalance(fromId, amount)) {
            AppGiftOrderBo orderBo = new AppGiftOrderBo();
            orderBo.setName(appGift.getName());
            orderBo.setType(1);
            orderBo.setAmount(price);
            orderBo.setNumber(number);
            orderBo.setUserId(fromId);
            orderBo.setToUserId(userId);
            orderBo.setGiftId(appGift.getId());
            return appGiftOrderService.insertByBo(orderBo);
        }
        return false;
    }
}
