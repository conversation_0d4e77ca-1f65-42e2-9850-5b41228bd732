package com.ruoyi.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.enums.LikeStatus;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ruoyi.app.domain.bo.AppLikeNotesBo;
import com.ruoyi.app.domain.vo.AppLikeNotesVo;
import com.ruoyi.app.domain.AppLikeNotes;
import com.ruoyi.app.mapper.AppLikeNotesMapper;
import com.ruoyi.app.service.IAppLikeNotesService;

import java.util.*;

/**
 * APP用户喜欢记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-02-14
 */
@RequiredArgsConstructor
@Service
public class AppLikeNotesServiceImpl implements IAppLikeNotesService {

    private final AppLikeNotesMapper baseMapper;

    /**
     * 查询APP用户喜欢记录
     */
    @Override
    public AppLikeNotesVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询APP用户喜欢记录列表
     */
    @Override
    public TableDataInfo<AppLikeNotesVo> queryPageList(AppLikeNotesBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AppLikeNotes> lqw = buildQueryWrapper(bo);
        Page<AppLikeNotesVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询APP用户喜欢记录列表
     */
    @Override
    public List<AppLikeNotesVo> queryList(AppLikeNotesBo bo) {
        LambdaQueryWrapper<AppLikeNotes> lqw = buildQueryWrapper(bo);
        lqw.orderByDesc(AppLikeNotes::getCreateTime);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 查询APP用户喜欢记录列表
     */
    @Override
    public List<AppLikeNotes> list(AppLikeNotesBo bo) {
        LambdaQueryWrapper<AppLikeNotes> lqw = buildQueryWrapper(bo);
        return baseMapper.selectList(lqw);
    }


    @Override
    public AppLikeNotesVo queryUser(Long userId, Long sideId, String status) {
        LambdaQueryWrapper<AppLikeNotes> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AppLikeNotes::getUserId, userId)
            .eq(AppLikeNotes::getSideId, sideId)
            .eq(AppLikeNotes::getStatus, status);
        return baseMapper.selectVoOne(wrapper);
    }


    @Override
    public Boolean queryBlock(Long userId, Long sideId) {
        LambdaQueryWrapper<AppLikeNotes> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AppLikeNotes::getUserId, userId)
            .eq(AppLikeNotes::getSideId, sideId)
            .eq(AppLikeNotes::getStatus, LikeStatus.BLOCK.getCode());
        return Objects.nonNull(baseMapper.selectVoOne(wrapper));
    }

    @Override
    public AppLikeNotesVo queryDynamic(Long userId, Long sideId) {
        LambdaQueryWrapper<AppLikeNotes> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AppLikeNotes::getUserId, userId)
            .eq(AppLikeNotes::getSideId, sideId)
            .eq(AppLikeNotes::getStatus, Constants.SUCCESS);
        return baseMapper.selectVoOne(wrapper);
    }


    @Override
    public AppLikeNotesVo queryUser(Long userId, Long sideId, List<String> status) {
        LambdaQueryWrapper<AppLikeNotes> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AppLikeNotes::getUserId, userId)
            .eq(AppLikeNotes::getSideId, sideId)
            .in(AppLikeNotes::getStatus, status);
        return baseMapper.selectVoOne(wrapper);
    }


    @Override
    public String queryRelation(Long userId, Long sideId) {
        AppLikeNotesVo appLikeNotesVo = queryUser(userId, sideId, Constants.SUCCESS);
        if (Objects.nonNull(appLikeNotesVo)) {
            if (Constants.SUCCESS.equals(appLikeNotesVo.getLikeStatus())) {
                return LikeStatus.SUPER_LIKE.getCode();
            }
            return LikeStatus.LIKE.getCode();
        }
        return null;
    }

    private LambdaQueryWrapper<AppLikeNotes> buildQueryWrapper(AppLikeNotesBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AppLikeNotes> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, AppLikeNotes::getUserId, bo.getUserId());
        lqw.eq(bo.getSideId() != null, AppLikeNotes::getSideId, bo.getSideId());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), AppLikeNotes::getStatus, bo.getStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getLikeStatus()), AppLikeNotes::getLikeStatus, bo.getLikeStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getReadStatus()), AppLikeNotes::getReadStatus, bo.getReadStatus());
        return lqw;
    }

    /**
     * 新增APP用户喜欢记录
     */
    @Override
    public Boolean insertByBo(AppLikeNotesBo bo) {
        AppLikeNotes add = BeanUtil.toBean(bo, AppLikeNotes.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改APP用户喜欢记录
     */
    @Override
    public Boolean updateByBo(AppLikeNotesBo bo) {
        AppLikeNotes update = BeanUtil.toBean(bo, AppLikeNotes.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 修改APP用户喜欢记录
     */
    @Override
    public Boolean updateByVo(AppLikeNotesVo vo) {
        AppLikeNotes update = BeanUtil.toBean(vo, AppLikeNotes.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AppLikeNotes entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除APP用户喜欢记录
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public Boolean deleteById(Long id) {
        return baseMapper.deleteById(id) > 0;
    }

    @Override
    public Boolean deleteByType(Long userId, String status) {
        return baseMapper.delete(new LambdaQueryWrapper<AppLikeNotes>()
            .eq(AppLikeNotes::getUserId, userId)
            .eq(AppLikeNotes::getStatus, status)) > 0;
    }

    @Override
    public Boolean updates(List<AppLikeNotes> readList) {
        return baseMapper.updateBatchById(readList);
    }
}
