package com.ruoyi.app.service;

import com.ruoyi.app.domain.bo.AppGiftBo;
import com.ruoyi.app.domain.vo.AppGiftVo;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

/**
 * APP礼物Service接口
 *
 * <AUTHOR>
 * @date 2025-12-14
 */
public interface IAppGiftService {

    /**
     * 查询APP礼物
     */
    AppGiftVo queryById(Long id);

    /**
     * 查询APP礼物列表
     */
    TableDataInfo<AppGiftVo> queryPageList(AppGiftBo bo, PageQuery pageQuery);

    /**
     * 查询APP礼物列表
     */
    List<AppGiftVo> queryList(AppGiftBo bo);

    /**
     * 新增APP礼物
     */
    Boolean insertByBo(AppGiftBo bo);

    /**
     * 修改APP礼物
     */
    Boolean updateByBo(AppGiftBo bo);

    /**
     * 校验并批量删除APP礼物信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 赠送礼物
     * @param fromId 赠送者
     * @param userId 赠送用户的ID
     * @param appGift 礼物对象
     * @param number 礼物数量
     * @return
     */
    Boolean send(Long fromId, Long userId, AppGiftVo appGift, Integer number, BigDecimal price);
}
