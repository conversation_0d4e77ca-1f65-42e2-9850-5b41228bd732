package com.ruoyi.app.service;

import com.ruoyi.app.domain.bo.AppReportBo;
import com.ruoyi.app.domain.vo.AppReportVo;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * APP举报类型Service接口
 *
 * <AUTHOR>
 * @date 2025-01-03
 */
public interface IAppReportService {

    /**
     * 查询APP举报类型
     */
    AppReportVo queryById(Long id);

    /**
     * 查询APP举报类型列表
     */
    TableDataInfo<AppReportVo> queryPageList(AppReportBo bo, PageQuery pageQuery);

    /**
     * 查询APP举报类型列表
     */
    List<AppReportVo> queryList(AppReportBo bo);

    /**
     * 新增APP举报类型
     */
    Boolean insertByBo(AppReportBo bo);

    /**
     * 修改APP举报类型
     */
    Boolean updateByBo(AppReportBo bo);

    /**
     * 校验并批量删除APP举报类型信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
