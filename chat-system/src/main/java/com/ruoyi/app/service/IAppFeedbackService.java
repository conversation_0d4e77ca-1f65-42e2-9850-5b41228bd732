package com.ruoyi.app.service;

import com.ruoyi.app.domain.AppFeedback;
import com.ruoyi.app.domain.vo.AppFeedbackVo;
import com.ruoyi.app.domain.bo.AppFeedbackBo;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * APP用户意见反馈Service接口
 *
 * <AUTHOR>
 * @date 2023-02-09
 */
public interface IAppFeedbackService {

    /**
     * 查询APP用户意见反馈
     */
    AppFeedbackVo queryById(Long id);


    /**
     * 查询APP用户意见反馈列表
     */
    TableDataInfo<AppFeedbackVo> queryPageList(AppFeedbackBo bo, PageQuery pageQuery);

    /**
     * 查询APP用户意见反馈列表
     */
    List<AppFeedbackVo> queryList(AppFeedbackBo bo);

    /**
     * 新增APP用户意见反馈
     */
    Boolean insertByBo(AppFeedbackBo bo);

    /**
     * 修改APP用户意见反馈
     */
    Boolean updateByBo(AppFeedbackBo bo);

    /**
     * 校验并批量删除APP用户意见反馈信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
