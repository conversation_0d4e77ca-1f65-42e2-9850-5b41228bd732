package com.ruoyi.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ruoyi.app.domain.bo.AppSchoolBo;
import com.ruoyi.app.domain.vo.AppSchoolVo;
import com.ruoyi.app.domain.AppSchool;
import com.ruoyi.app.mapper.AppSchoolMapper;
import com.ruoyi.app.service.IAppSchoolService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * APP学校信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@RequiredArgsConstructor
@Service
public class AppSchoolServiceImpl implements IAppSchoolService {

    private final AppSchoolMapper baseMapper;

    /**
     * 查询APP学校信息
     */
    @Override
    public AppSchoolVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询APP学校信息列表
     */
    @Override
    public TableDataInfo<AppSchoolVo> queryPageList(AppSchoolBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AppSchool> lqw = buildQueryWrapper(bo);
        Page<AppSchoolVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询APP学校信息列表
     */
    @Override
    public List<AppSchoolVo> queryList(AppSchoolBo bo) {
        LambdaQueryWrapper<AppSchool> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AppSchool> buildQueryWrapper(AppSchoolBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AppSchool> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getName()), AppSchool::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), AppSchool::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增APP学校信息
     */
    @Override
    public Boolean insertByBo(AppSchoolBo bo) {
        AppSchool add = BeanUtil.toBean(bo, AppSchool.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改APP学校信息
     */
    @Override
    public Boolean updateByBo(AppSchoolBo bo) {
        AppSchool update = BeanUtil.toBean(bo, AppSchool.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AppSchool entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除APP学校信息
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
