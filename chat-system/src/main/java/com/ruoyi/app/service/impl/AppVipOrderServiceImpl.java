package com.ruoyi.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.app.domain.vo.AppVipTypeVo;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.utils.OrderNumUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ruoyi.app.domain.bo.AppVipOrderBo;
import com.ruoyi.app.domain.vo.AppVipOrderVo;
import com.ruoyi.app.domain.AppVipOrder;
import com.ruoyi.app.mapper.AppVipOrderMapper;
import com.ruoyi.app.service.IAppVipOrderService;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * APPv会员VIP订单Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-17
 */
@RequiredArgsConstructor
@Service
public class AppVipOrderServiceImpl implements IAppVipOrderService {

    private final AppVipOrderMapper baseMapper;

    /**
     * 查询APPv会员VIP订单
     */
    @Override
    public AppVipOrderVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    @Override
    public AppVipOrderVo queryOrderNo(String orderNo) {
        LambdaQueryWrapper<AppVipOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AppVipOrder::getOrderNo, orderNo);
        return baseMapper.selectVoOne(wrapper);
    }

    /**
     * 查询APPv会员VIP订单列表
     */
    @Override
    public TableDataInfo<AppVipOrderVo> queryPageList(AppVipOrderBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AppVipOrder> lqw = buildQueryWrapper(bo);
        Page<AppVipOrderVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询APPv会员VIP订单列表
     */
    @Override
    public List<AppVipOrderVo> queryList(AppVipOrderBo bo) {
        LambdaQueryWrapper<AppVipOrder> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AppVipOrder> buildQueryWrapper(AppVipOrderBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AppVipOrder> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, AppVipOrder::getUserId, bo.getUserId());
        lqw.eq(bo.getVipId() != null, AppVipOrder::getVipId, bo.getVipId());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderNo()), AppVipOrder::getOrderNo, bo.getOrderNo());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), AppVipOrder::getStatus, bo.getStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getPayType()), AppVipOrder::getPayType, bo.getPayType());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), AppVipOrder::getType, bo.getType());
        return lqw;
    }

    /**
     * 新增APPv会员VIP订单
     */
    @Override
    public Boolean insertByBo(AppVipOrderBo bo) {
        AppVipOrder add = BeanUtil.toBean(bo, AppVipOrder.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改APPv会员VIP订单
     */
    @Override
    public Boolean updateByBo(AppVipOrderBo bo) {
        AppVipOrder update = BeanUtil.toBean(bo, AppVipOrder.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }


    /**
     * 修改APPv会员VIP订单
     */
    @Override
    public Boolean updateByVo(AppVipOrderVo vo) {
        AppVipOrder update = BeanUtil.toBean(vo, AppVipOrder.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AppVipOrder entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除APPv会员VIP订单
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AppVipOrder createOrder(AppVipTypeVo appVipTypeVo, Long userId) {
        String orderNo = OrderNumUtils.getInstance().generateOrderNo();
        AppVipOrder orderBo = new AppVipOrder();
        orderBo.setOrderNo(orderNo);
        orderBo.setStatus(Constants.SUCCESS);
        orderBo.setType("1");
        orderBo.setVipId(appVipTypeVo.getId());
        orderBo.setUserId(userId);
        baseMapper.insert(orderBo);
        return orderBo;
    }
}
