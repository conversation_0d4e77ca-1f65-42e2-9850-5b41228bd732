package com.ruoyi.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ruoyi.app.domain.bo.AppLikeSignBo;
import com.ruoyi.app.domain.vo.AppLikeSignVo;
import com.ruoyi.app.domain.AppLikeSign;
import com.ruoyi.app.mapper.AppLikeSignMapper;
import com.ruoyi.app.service.IAppLikeSignService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * APP超级喜欢的标记Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-02-22
 */
@RequiredArgsConstructor
@Service
public class AppLikeSignServiceImpl implements IAppLikeSignService {

    private final AppLikeSignMapper baseMapper;

    /**
     * 查询APP超级喜欢的标记
     */
    @Override
    public AppLikeSignVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询APP超级喜欢的标记列表
     */
    @Override
    public TableDataInfo<AppLikeSignVo> queryPageList(AppLikeSignBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AppLikeSign> lqw = buildQueryWrapper(bo);
        Page<AppLikeSignVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询APP超级喜欢的标记列表
     */
    @Override
    public List<AppLikeSignVo> queryList(AppLikeSignBo bo) {
        LambdaQueryWrapper<AppLikeSign> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    @Override
    public AppLikeSign queryOne(Long userId, Long sideId) {
        LambdaQueryWrapper<AppLikeSign> wrapper = new LambdaQueryWrapper<>();
        wrapper
            .eq(AppLikeSign::getUserId, userId)
            .eq(AppLikeSign::getSideId, sideId)
            .eq(AppLikeSign::getStatus, Constants.MSG_UNREAD);
        return baseMapper.selectOne(wrapper);
    }

    @Override
    public AppLikeSign queryLike(Long userId, Long sideId) {
        LambdaQueryWrapper<AppLikeSign> wrapper = new LambdaQueryWrapper<>();
        wrapper
            .eq(AppLikeSign::getUserId, userId)
            .eq(AppLikeSign::getSideId, sideId);
        return baseMapper.selectOne(wrapper);
    }

    /**
     * 查询APP超级喜欢的标记列表
     */
    @Override
    public List<AppLikeSign> list(AppLikeSignBo bo) {
        LambdaQueryWrapper<AppLikeSign> lqw = buildQueryWrapper(bo);
        return baseMapper.selectList(lqw);
    }

    private LambdaQueryWrapper<AppLikeSign> buildQueryWrapper(AppLikeSignBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AppLikeSign> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, AppLikeSign::getUserId, bo.getUserId());
        lqw.eq(bo.getSideId() != null, AppLikeSign::getSideId, bo.getSideId());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), AppLikeSign::getStatus, bo.getStatus());
        lqw.orderByAsc(AppLikeSign::getCreateTime);
        return lqw;
    }

    /**
     * 新增APP超级喜欢的标记
     */
    @Override
    public Boolean insertByBo(AppLikeSignBo bo) {
        AppLikeSign add = BeanUtil.toBean(bo, AppLikeSign.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改APP超级喜欢的标记
     */
    @Override
    public Boolean updateByBo(AppLikeSignBo bo) {
        AppLikeSign update = BeanUtil.toBean(bo, AppLikeSign.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    @Override
    public Boolean update(AppLikeSign appLikeSign) {
        return baseMapper.updateById(appLikeSign) > 0;
    }

    @Override
    public Boolean update(List<AppLikeSign> appLikeSigns) {
        return baseMapper.updateBatchById(appLikeSigns);
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AppLikeSign entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除APP超级喜欢的标记
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
