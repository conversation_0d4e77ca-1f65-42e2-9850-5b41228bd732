package com.ruoyi.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.app.mapper.AppCityDistMapper;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ruoyi.app.domain.bo.AppCityDistBo;
import com.ruoyi.app.domain.vo.AppCityDistVo;
import com.ruoyi.app.domain.AppCityDist;
import com.ruoyi.app.service.IAppCityDistService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * APP城市列Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-01
 */
@RequiredArgsConstructor
@Service
public class AppCityDistServiceImpl implements IAppCityDistService {

    private final AppCityDistMapper baseMapper;

    /**
     * 查询APP城市列
     */
    @Override
    public AppCityDistVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询APP城市列列表
     */
    @Override
    public TableDataInfo<AppCityDistVo> queryPageList(AppCityDistBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AppCityDist> lqw = buildQueryWrapper(bo);
        Page<AppCityDistVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询APP城市列列表
     */
    @Override
    public List<AppCityDistVo> queryList(AppCityDistBo bo) {
        LambdaQueryWrapper<AppCityDist> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }


    /**
     * 查询APP城市列列表
     */
    @Override
    public List<AppCityDistVo> queryList() {
        LambdaQueryWrapper<AppCityDist> wrapper = new LambdaQueryWrapper<>();
        wrapper.isNotNull(AppCityDist::getCity);
        return baseMapper.selectVoList(wrapper);
    }

    private LambdaQueryWrapper<AppCityDist> buildQueryWrapper(AppCityDistBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AppCityDist> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getProvince()), AppCityDist::getProvince, bo.getProvince());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), AppCityDist::getType, bo.getType());
        lqw.eq(StringUtils.isNotBlank(bo.getCity()), AppCityDist::getCity, bo.getCity());
        lqw.eq(bo.getProvinceId() != null, AppCityDist::getProvinceId, bo.getProvinceId());
        lqw.eq(StringUtils.isNotBlank(bo.getDistrict()), AppCityDist::getDistrict, bo.getDistrict());
        lqw.eq(bo.getCityId() != null, AppCityDist::getCityId, bo.getCityId());
        return lqw;
    }

    /**
     * 新增APP城市列
     */
    @Override
    public Boolean insertByBo(AppCityDistBo bo) {
        AppCityDist add = BeanUtil.toBean(bo, AppCityDist.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改APP城市列
     */
    @Override
    public Boolean updateByBo(AppCityDistBo bo) {
        AppCityDist update = BeanUtil.toBean(bo, AppCityDist.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AppCityDist entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除APP城市列
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
