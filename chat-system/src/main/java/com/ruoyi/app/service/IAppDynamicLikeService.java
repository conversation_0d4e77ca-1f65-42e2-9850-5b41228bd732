package com.ruoyi.app.service;

import com.ruoyi.app.domain.AppDynamicLike;
import com.ruoyi.app.domain.bo.AppDynamicBo;
import com.ruoyi.app.domain.vo.AppDynamicLikeVo;
import com.ruoyi.app.domain.bo.AppDynamicLikeBo;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;

/**
 * APP动态点赞Service接口
 *
 * <AUTHOR>
 * @date 2025-01-16
 */
public interface IAppDynamicLikeService {

    /**
     * 查询APP动态点赞
     */
    AppDynamicLikeVo queryById(Long id);

    /**
     * 查询APP动态点赞列表
     */
    TableDataInfo<AppDynamicLikeVo> queryPageList(AppDynamicLikeBo bo, PageQuery pageQuery);

    /**
     * 查询APP动态点赞列表
     */
    List<AppDynamicLikeVo> queryList(AppDynamicLikeBo bo);

    /**
     * 新增APP动态点赞
     */
    Boolean insertByBo(AppDynamicLikeBo bo);

    /**
     * 修改APP动态点赞
     */
    Boolean updateByBo(AppDynamicLikeBo bo);

    /**
     * 校验并批量删除APP动态点赞信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);


    /**
     * 根据记录ID，用户ID，类型查询点赞信息
     *
     * @param did  动态或者评论ID
     * @param uid  用户ID
     * @param type 类型
     * @return
     */
    AppDynamicLikeVo queryByDynamicIdAndUserId(Long did, Long uid, String type);

    /**
     * 根据记录ID，用户ID，类型查询点赞信息是否存在
     *
     * @param did  动态或者评论ID
     * @param uid  用户ID
     * @param type 类型
     * @return
     */
    Boolean queryByUser(Long did, Long uid, String type);

    /**
     * 查询动态的点赞记录
     *
     * @param id 动态ID
     * @return
     */
    List<AppDynamicLikeVo> queryByLike(Long id, String type);


    /**
     * 查询动态的点赞数量
     *
     * @param id 动态ID
     * @return
     */
    Long queryCount(Long id, String type);

    /**
     * 动态的点赞和取消点赞
     *
     * @param id     动态ID
     * @param userId 用户ID
     * @return
     */
    HashMap<String, String> like(Long id, Long userId, String type,Long emojiId);

}
