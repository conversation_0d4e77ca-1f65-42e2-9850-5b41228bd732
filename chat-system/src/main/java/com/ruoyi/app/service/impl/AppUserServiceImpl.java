package com.ruoyi.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.crypto.digest.BCrypt;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.app.domain.AppUser;
import com.ruoyi.app.domain.bo.AppAddressBookBo;
import com.ruoyi.app.domain.bo.AppUserBo;
import com.ruoyi.app.domain.vo.*;
import com.ruoyi.app.mapper.AppFriendMapper;
import com.ruoyi.app.mapper.AppMateMapper;
import com.ruoyi.app.mapper.AppUserMapper;
import com.ruoyi.app.service.*;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BillType;
import com.ruoyi.common.enums.CoinType;
import com.ruoyi.common.enums.DetailType;
import com.ruoyi.common.enums.SexType;
import com.ruoyi.common.utils.*;
import com.ruoyi.system.service.ISysConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * APP用户信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-11-30
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class AppUserServiceImpl implements IAppUserService {


    private final AppUserMapper baseMapper;
    private final AppMateMapper appMateMapper;
    private final AppFriendMapper appFriendMapper;
    private final IAppAssetService appAssetService;
    private final IAppDetailService appDetailService;
    private final ISysConfigService iSysConfigService;
    private final IAppVipTypeService appVipTypeService;
    private final IAppUserVipService appUserVipService;
    private final IAppUserImageService appUserImageService;
    private final IAppAddressBookService appAddressBookService;
    private final IAppFriendRelationService appFriendRelationService;

    // 当日用户已使用免费额度
    private Map<Long, Integer> free_used = new HashMap<>();
    public static final LinkedHashMap<String, String> LANGUAGE = new LinkedHashMap<>();


    @PostConstruct
    public void init() {
        LANGUAGE.put("en", "en-Us");
        LANGUAGE.put("zh", "zh-CN");
        LANGUAGE.put("cht", "zh-TW");
        LANGUAGE.put("kor", "ko-KR");
        LANGUAGE.put("ru", "ru-RU");
        LANGUAGE.put("de", "de-DE");
        LANGUAGE.put("spa", "es-AR");
        LANGUAGE.put("fra", "fr-FR");
        LANGUAGE.put("jp", "ja-JP");
    }


    @Override
    public List<AppLanguageVo> languages() {
        List<AppLanguageVo> list = new ArrayList<>();
        int i = 0;
        for (String key : LANGUAGE.keySet()) {
            i= i +1;
            AppLanguageVo languageVo = new AppLanguageVo();
            languageVo.setId(String.valueOf(i));
            languageVo.setName(key);
            languageVo.setValue(LANGUAGE.get(key));
            list.add(languageVo);
        }
        return list;
    }



    /**
     * 查询APP用户信息
     */
    @Override
    public AppUserVo selectVoById(Long id) {
        AppUserVo userVo = baseMapper.selectVoById(id);
        if (Objects.nonNull(userVo)) {
            userVo.setChatName(Constants.USER + userVo.getId());
        }
        return userVo;
    }


    /**
     * 查询APP用户信息
     */
    @Override
    public AppUserVo queryById(Long id) {
        AppUserVo userVo = baseMapper.selectVoById(id);
        if (ObjectUtils.isEmpty(userVo)) {
            return null;
        }
        userVo.setPassword(null);
        if (userVo.getBirthday() != null) {
            userVo.setAge(InfoUtils.getAge(userVo.getBirthday()));
        }
        userVo.setChatName(Constants.USER + userVo.getId());
        return userVo;
    }

    @Override
    public List<AppUserVo> queryByPid(Long id) {
        LambdaQueryWrapper<AppUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AppUser::getParentId, id);
        return baseMapper.selectVoList(wrapper);
    }

    /**
     * 查询APP详细用户信息
     */
    @Override
    public UserVo queryByIdUserVo(Long id) {
        AppUserVo user = baseMapper.selectVoById(id);
        user.setIsRealName(StringUtils.isNotBlank(user.getCode()));
        user.setCode(null);
        user.setCodeName(null);
        user.setPassword(null);
        UserVo userVo = new UserVo();
        EntityCopyUtils.copyPropertiesIgnoreNull(user, userVo);
        if (userVo.getBirthday() != null) {
            userVo.setAge(InfoUtils.getAge(userVo.getBirthday()));
            userVo.setConstellation(InfoUtils.getConstellation(userVo.getBirthday()));
        }
        if (StringUtils.isNotBlank(user.getUserLanguage())) {
            userVo.setAppLanguage(LANGUAGE.get(user.getUserLanguage()));
        }
        userVo.setVipVo(appUserVipService.userVipId(id));
        userVo.setChatName(Constants.USER + user.getId());
        return userVo;
    }

    /**
     * 过滤用户信息
     */
    @Override
    public UserFilterVo userFilterVo(Long id) {
        AppUserVo user = baseMapper.selectVoById(id);
        user.setCode(null);
        user.setCodeName(null);
        user.setPassword(null);
        UserFilterVo userVo = new UserFilterVo();
        EntityCopyUtils.copyPropertiesIgnoreNull(user, userVo);
        return userVo;
    }

    /**
     * 过滤用户信息
     */
    @Override
    public UserResultVo filterateUserVo(Long id) {
        AppUserVo user = baseMapper.selectVoById(id);
        user.setCode(null);
        user.setPassword(null);
        user.setCodeName(null);
        UserResultVo userVo = new UserResultVo();
        EntityCopyUtils.copyPropertiesIgnoreNull(user, userVo);
        if (userVo.getBirthday() != null) {
            userVo.setAge(InfoUtils.getAge(userVo.getBirthday()));
            userVo.setConstellation(InfoUtils.getConstellation(userVo.getBirthday()));
        }
        userVo.setVipVo(appUserVipService.userVipId(id));
        return userVo;
    }


    /**
     * 查询APP详细用户信息
     */
    @Override
    public UserVo userVo(Long id) {
        AppUserVo user = baseMapper.selectVoById(id);
        UserVo userVo = new UserVo();
        EntityCopyUtils.copyPropertiesIgnoreNull(user, userVo);
        if (userVo.getBirthday() != null) {
            userVo.setAge(InfoUtils.getAge(userVo.getBirthday()));
            userVo.setConstellation(InfoUtils.getConstellation(userVo.getBirthday()));
        }
        userVo.setPhotos(appUserImageService.queryList(user.getId()));
        userVo.setVipVo(appUserVipService.userVipId(id));
        return userVo;
    }


    @Override
    public AppUserVo queryByPhone(String phone) {
        LambdaQueryWrapper<AppUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AppUser::getPhone, phone);
        return baseMapper.selectVoOne(wrapper);
    }

    @Override
    public List<AppUserVo> queryByPhone(String phone, Long id) {
        LambdaQueryWrapper<AppUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AppUser::getPhone, phone);
        wrapper.ne(AppUser::getId, id);
        return baseMapper.selectVoList(wrapper);
    }

    @Override
    public AppUserVo queryByUserName(String userName) {
        LambdaQueryWrapper<AppUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AppUser::getUserName, userName);
        return baseMapper.selectVoOne(wrapper);
    }

    @Override
    public AppUserVo queryByEmail(String email) {
        LambdaQueryWrapper<AppUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AppUser::getEmail, email);
        return baseMapper.selectVoOne(wrapper);
    }

    @Override
    public List<AppUserVo> queryByEmail(String email, Long id) {
        LambdaQueryWrapper<AppUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.ne(AppUser::getId, id);
        wrapper.eq(AppUser::getEmail, email);
        return baseMapper.selectVoList(wrapper);
    }

    @Override
    public AppUserVo queryByEmailOrPhone(String userName) {
        LambdaQueryWrapper<AppUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AppUser::getEmail, userName).or().eq(AppUser::getPhone, userName);
        return baseMapper.selectVoOne(wrapper);
    }

    @Override
    public AppUserVo queryByUserNameOrPhone(String userName) {
        LambdaQueryWrapper<AppUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AppUser::getUserName, userName).or().eq(AppUser::getEmail, userName).or().eq(AppUser::getPhone, userName);
        return baseMapper.selectVoOne(wrapper);
    }

    @Override
    public AppUserVo queryNickName(String name) {
        LambdaQueryWrapper<AppUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AppUser::getNickName, name);
        return baseMapper.selectVoOne(wrapper);
    }

    @Override
    public AppUserVo queryByInviteCode(String code) {
        LambdaQueryWrapper<AppUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AppUser::getInviteCode, code);
        return baseMapper.selectVoOne(wrapper);
    }

    @Override
    public List<AppUserVo> selectList(AppUserBo bo) {
        LambdaQueryWrapper<AppUser> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }


    @Override
    public List<AppUserVo> selectList(LambdaQueryWrapper<AppUser> lqw) {
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 查询APP用户信息列表
     */
    @Override
    public TableDataInfo<AppUserVo> queryPageList(AppUserBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AppUser> lqw = buildQueryWrapper(bo);
        Page<AppUserVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询APP用户信息列表
     */
    @Override
    public TableDataInfo<AppUser> queryPage(AppUserBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AppUser> lqw = buildQueryWrapper(bo);
        Page<AppUser> result = baseMapper.selectPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }


    /**
     * 查询APP用户信息列表
     */
    @Override
    public List<AppUserVo> queryList(AppUserBo bo) {
        LambdaQueryWrapper<AppUser> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 查询APP用户信息列表
     */
    @Override
    public List<AppUser> queryListUser(AppUserBo appUserBo) {
        LambdaQueryWrapper<AppUser> lqw = buildQueryWrapper(appUserBo);
        return baseMapper.selectList(lqw);
    }


    /**
     * 计算用户有几个共同爱好的标签
     *
     * @param labelIDs
     * @param labelLikeVos
     * @return
     */
    public long number(Set<Long> labelIDs, List<AppLabelLikeVo> labelLikeVos) {
        return labelLikeVos.stream().filter(i -> labelIDs.contains(i.getLabelId())).count();
    }

    /**
     * 获取用户的信息
     *
     * @param ids 用户ID
     * @return
     */
    public List<AppUserVo> info(HashSet<Long> ids) {
        ArrayList<AppUserVo> userVos = new ArrayList<>();
        ids.forEach(e -> {
            AppUserVo userVo = queryById(e);
            userVo.setChatName(Constants.USER + e);
            userVos.add(userVo);
        });
        return userVos;
    }


    @Override
    public List<AppUserVo> queryListOrder(AppUserBo bo) {
        LambdaQueryWrapper<AppUser> lqw = buildQueryWrapper(bo);
        lqw.orderByDesc(AppUser::getId);
        return baseMapper.selectVoList(lqw);
    }

    @Override
    public List<AppUserVo> queryList(AppUserSettingVo setting, Set<Long> noLikeIds) {
        noLikeIds.add(setting.getUserId());
        LambdaQueryWrapper<AppUser> lqw = Wrappers.lambdaQuery();
        lqw.notIn(AppUser::getId, noLikeIds);
        if (!SexType.UN.getCode().equals(setting.getSex())) {
            lqw.eq(AppUser::getSex, setting.getSex());
        }
        if (setting.getDistance() != null && setting.getDistance() > 0 && setting.getDistance() < 100) {
            if (setting.getLatitude() != null && setting.getLongitude() != null) {
                Map<String, Double> doubleMap = DistanceUtils.getPeopleNearby(setting.getLongitude(), setting.getLatitude(), setting.getDistance());
                lqw.le(AppUser::getLatitude, doubleMap.get("maxLatitude"));
                lqw.ge(AppUser::getLatitude, doubleMap.get("minLatitude"));
                lqw.le(AppUser::getLongitude, doubleMap.get("maxLongitude"));
                lqw.ge(AppUser::getLongitude, doubleMap.get("minLongitude"));
            }
        }
        //年龄越小，时间戳越大
        if (setting.getAgeMin().equals(setting.getAgeMax())) {
            String minString = newDateUtil.subYearNew(new Date(), setting.getAgeMin()) + "-01-01 00:00:00";
            long minLong = newDateUtil.toMillSeconds(minString);
            String maxString = newDateUtil.subYearNew(new Date(), setting.getAgeMax()) + "-12-31 23:59:59";
            long maxLong = newDateUtil.toMillSeconds(maxString);
            lqw.ge(AppUser::getBirthday, minLong).le(AppUser::getBirthday, maxLong);
        } else {
            String minString = newDateUtil.subYearNew(new Date(), setting.getAgeMin()) + "-12-31 23:59:59";
            long minLong = newDateUtil.toMillSeconds(minString);
            String maxString = newDateUtil.subYearNew(new Date(), setting.getAgeMax()) + "-01-01 00:00:00";
            long maxLong = newDateUtil.toMillSeconds(maxString);
            lqw.le(AppUser::getBirthday, minLong).ge(AppUser::getBirthday, maxLong);
        }
        return baseMapper.selectVoList(lqw);
    }


    @Override
    public List<AppUserVo> findList(AppUserBo bo) {
        LambdaQueryWrapper<AppUser> lqw = wrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    @Override
    public List<AppUserVo> findNameList(AppUserBo bo) {
        LambdaQueryWrapper<AppUser> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getUserName()), AppUser::getUserName, bo.getUserName()).or().like(StringUtils.isNotBlank(bo.getNickName()), AppUser::getNickName, bo.getNickName());
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AppUser> buildQueryWrapper(AppUserBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AppUser> lqw = Wrappers.lambdaQuery();
        lqw.like(bo.getId() != null, AppUser::getId, bo.getId());
        lqw.like(StringUtils.isNotBlank(bo.getUserName()), AppUser::getUserName, bo.getUserName());
        lqw.like(StringUtils.isNotBlank(bo.getNickName()), AppUser::getNickName, bo.getNickName());
        lqw.eq(StringUtils.isNotBlank(bo.getEmail()), AppUser::getEmail, bo.getEmail());
        lqw.like(StringUtils.isNotBlank(bo.getPhone()), AppUser::getPhone, bo.getPhone());
        lqw.eq(StringUtils.isNotBlank(bo.getSex()), AppUser::getSex, bo.getSex());
        lqw.eq(StringUtils.isNotBlank(bo.getInviteCode()), AppUser::getInviteCode, bo.getInviteCode());
        lqw.eq(bo.getParentId() != null, AppUser::getParentId, bo.getParentId());
        lqw.eq(StringUtils.isNotBlank(bo.getOccupation()), AppUser::getOccupation, bo.getOccupation());
        lqw.eq(StringUtils.isNotBlank(bo.getSchool()), AppUser::getSchool, bo.getSchool());
        lqw.eq(bo.getBirthday() != null, AppUser::getBirthday, bo.getBirthday());
        lqw.eq(bo.getHeight() != null, AppUser::getHeight, bo.getHeight());
        lqw.eq(StringUtils.isNotBlank(bo.getRegister()), AppUser::getRegister, bo.getRegister());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), AppUser::getStatus, bo.getStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getLoginIp()), AppUser::getLoginIp, bo.getLoginIp());
        lqw.eq(bo.getLoginDate() != null, AppUser::getLoginDate, bo.getLoginDate());
        lqw.in(bo.getIds() != null && !bo.getIds().isEmpty(), AppUser::getId, bo.getIds());
        return lqw;
    }

    private LambdaQueryWrapper<AppUser> wrapper(AppUserBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AppUser> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getNickName()), AppUser::getNickName, bo.getNickName()).or().eq(StringUtils.isNotBlank(bo.getPhone()), AppUser::getPhone, bo.getPhone());
        return lqw;
    }

    /**
     * 新增APP用户信息
     */
    @Override
    public Boolean insertByBo(AppUserBo bo) {
        AppUser add = BeanUtil.toBean(bo, AppUser.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 新增APP用户信息
     */
    @Override
    public Boolean insert(AppUserVo userVo) {
        AppUser user = BeanUtil.toBean(userVo, AppUser.class);
        user.setCreateBy(userVo.getUserName());
        user.setCreateTime(DateUtils.getNowDate());
        user.setUpdateBy(userVo.getUserName());
        user.setUpdateTime(DateUtils.getNowDate());
        boolean flag = baseMapper.insert(user) > 0;
        if (flag) {
            userVo.setId(user.getId());
        }
        return flag;
    }

    /**
     * 修改APP用户信息
     */
    @Override
    public Boolean updateByBo(AppUserBo bo) {
        AppUser update = BeanUtil.toBean(bo, AppUser.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 修改APP用户信息
     */
    @Override
    public Boolean updateByVo(AppUserVo bo) {
        AppUser update = BeanUtil.toBean(bo, AppUser.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    @Override
    public Boolean updates(List<AppUserVo> appUserVos) {
        return null;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AppUser entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除APP用户信息
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public HashMap<String, Object> inviteCode(Long uid) {
        AppUserVo user = queryById(uid);
        if (StringUtils.isBlank(user.getInviteCode())) {
            user.setInviteCode(ShareCodeUtil.getSerialCode(user.getId().intValue()));
            AppUserBo userBo = BeanUtil.toBean(user, AppUserBo.class);
            updateByBo(userBo);
        }
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("inviteCode", user.getInviteCode());
        AppUserBo userBo = new AppUserBo();
        userBo.setParentId(user.getId());
        List<AppUserVo> inviteUser = selectList(userBo);
        ArrayList<UserVo> list = new ArrayList<>();
        inviteUser.forEach(e -> list.add(queryByIdUserVo(e.getId())));
        hashMap.put("isBind", user.getParentId() != null);
        hashMap.put("inviteUsers", list);
        hashMap.put("downloadUrL", iSysConfigService.selectConfigByKey("app.share.h5"));
        //邀请有效数量
        hashMap.put("inviteUsersNumber", inviteUser.size());
        //已邀请人数
        hashMap.put("inviteUsersNumberSum", inviteUser.size());
        hashMap.put("inviteInfo", "邀请好友下载，参与奖励");
        hashMap.put("inviteExplain", "1.可获得虚拟饮料\n" + "2.参与一瓶水认识一个人找到心动的另一半");
        return hashMap;
    }

    @Override
    public Boolean updatePassword(Long userId, String password) {
        return baseMapper.update(null, new LambdaUpdateWrapper<AppUser>().set(AppUser::getPassword, BCrypt.hashpw(password)).eq(AppUser::getId, userId)) > 0;
    }

    @Override
    public Boolean updateUuid(Long userId, String uuid) {
        return baseMapper.update(null, new LambdaUpdateWrapper<AppUser>().set(AppUser::getUuid, uuid).eq(AppUser::getId, userId)) > 0;
    }

    @Override
    public Boolean updateStatus(Long userId, String status) {
        AppUserVo userVo = queryById(userId);
        return baseMapper.update(null, new LambdaUpdateWrapper<AppUser>().set(AppUser::getStatus, status).set(AppUser::getUserName, userVo.getUserName() + "_off").set(AppUser::getPhone, userVo.getPhone() + "_off").eq(AppUser::getId, userId)) > 0;
    }


    @Override
    public Boolean updateInfo(AppUserBo bo) {
        return updateByBo(bo);
    }

    /**
     * 查询用户信息
     *
     * @param id     查询用户信息的ID
     * @param userId 当前用户ID
     * @return
     */
    @Override
    public UserVo setUserInfo(Long id, Long userId) {
        UserVo user = queryByIdUserVo(id);
        user.setUuid(null);
        if (!id.equals(userId)) {
            List<AppFriendRelationVo> userCollect = appFriendRelationService.queryList(userId, id, Constants.SUCCESS);
            user.setCollectStatus(!userCollect.isEmpty());
        }
        return user;
    }


    @Override
    public List<UserVo> randNumberUser(Long userId, Integer number) {
        AppAddressBookBo bookBo = new AppAddressBookBo();
        bookBo.setUserId(userId);
        List<AppAddressBookVo> bookVos = appAddressBookService.queryList(bookBo);
        List<Long> ids = bookVos.stream().map(AppAddressBookVo::getBookId).collect(Collectors.toList());
        ids.add(userId);
        AppUserBo userBo = new AppUserBo();
        userBo.setStatus(Constants.SUCCESS);
        List<AppUserVo> appUserVos = queryList(userBo);
        ArrayList<UserVo> userVos = new ArrayList<>();
        for (int i = 0; i < appUserVos.size(); i++) {
            AppUserVo userVo = appUserVos.get(RandomUtils.number(0, appUserVos.size()));
            if (!ids.contains(userVo.getId())) {
                UserVo user = new UserVo();
                EntityCopyUtils.copyPropertiesIgnoreNull(userVo, user);
                userVos.add(user);
            }
            if (userVos.size() >= number) {
                break;
            }
        }
        return userVos;
    }

    @Override
    public Boolean updateOnline(Long id) {
        LambdaUpdateWrapper<AppUser> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(AppUser::getId, id).set(AppUser::getOnlineStatus, Constants.SUCCESS);
        return baseMapper.update(null, wrapper) > 0;
    }

    @Override
    public Boolean update(ArrayList<AppUser> updates) {
        return baseMapper.updateBatchById(updates);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean translate(Long id, Integer messageLength) {
        AppAssetVo assetVo = appAssetService.queryByUid(id);
        int freeUsed = free_used.getOrDefault(id, 0);
        int purchased = assetVo.getIntegral().intValue();
        int todayFreeNumber = 0;
        AppUserVipVo userVipVo = appUserVipService.userVipId(id);
        if (userVipVo != null) {
            AppVipTypeVo vipTypeVo = appVipTypeService.queryById(userVipVo.getVipId());
            todayFreeNumber = vipTypeVo.getGiveNumber();
        }
        // 计算剩余免费额度
        int remainingFree = todayFreeNumber - freeUsed;
        if (remainingFree > 0) {
            // 先使用免费额度
            int freeToUse = Math.min(remainingFree, messageLength);
            free_used.put(id, freeUsed + freeToUse);
            messageLength -= freeToUse;
            log.info("每日用户免费字符：{}", free_used);
        }
        if (messageLength > 0) {
            // 使用购买的额度
            if (purchased >= messageLength) {
                BigDecimal bigDecimal = new BigDecimal(messageLength);
                appAssetService.subtractIntegral(id, bigDecimal);
                appDetailService.insert(id, bigDecimal, BigDecimal.ZERO, BillType.TO.getCode(), CoinType.INTE.getCode(), DetailType.TEXT.getCode(), "发送文字扣费", null);
            } else {
                return false;
            }
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<?> subtractionEmoji(Long id, BigDecimal price) {
        AppUserVipVo userVipVo = appUserVipService.userVipId(id);
        if (userVipVo != null) {
            AppVipTypeVo vipTypeVo = appVipTypeService.queryById(userVipVo.getVipId());
            if (StringUtils.equals(vipTypeVo.getType(), Constants.SUCCESS)) {
                return R.ok(MessageUtils.message("system.info.success"));
            }
        }
        if (appAssetService.subtractBalance(id, price)) {
            appDetailService.insert(id, price, BigDecimal.ZERO, BillType.TO.getCode(), CoinType.GOLD.getCode(), DetailType.EMOJI.getCode(), "发送表情扣费", null);
        }
        return R.ok(MessageUtils.message("system.info.success"));
    }


    /**
     * 重置每日免费额度(每天凌晨调用)
     */
    @Override
    public void resetDailyFreeQuota() {
        free_used.clear();
    }
}
