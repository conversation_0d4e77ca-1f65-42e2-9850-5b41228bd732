package com.ruoyi.app.service;

import com.ruoyi.app.domain.bo.AppHongbaoBo;
import com.ruoyi.app.domain.vo.AppHongbaoVo;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

/**
 * APP红包Service接口
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface IAppHongbaoService {

    /**
     * 查询APP红包
     */
    AppHongbaoVo queryById(Long hongbaoId);

    /**
     * 查询APP红包列表
     */
    TableDataInfo<AppHongbaoVo> queryPageList(AppHongbaoBo bo, PageQuery pageQuery);

    /**
     * 查询APP红包列表
     */
    List<AppHongbaoVo> queryList(AppHongbaoBo bo);

    /**
     * 新增APP红包
     */
    Boolean insertByBo(AppHongbaoBo bo);

    /**
     * 修改APP红包
     */
    Boolean updateByBo(AppHongbaoBo bo);

    /**
     * 修改红包数据
     * @param bo 红包数据
     * @return
     */
    Boolean updateByVo(AppHongbaoVo bo);

    /**
     * 校验并批量删除APP红包信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    AppHongbaoBo sendHongbao(AppHongbaoBo bo, BigDecimal amount);

    AppHongbaoVo receiveHongbao(Long id, AppHongbaoVo hongbaoVo);
}
