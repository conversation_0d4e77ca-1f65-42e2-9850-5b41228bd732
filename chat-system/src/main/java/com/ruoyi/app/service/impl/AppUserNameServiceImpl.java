package com.ruoyi.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ruoyi.app.domain.bo.AppUserNameBo;
import com.ruoyi.app.domain.vo.AppUserNameVo;
import com.ruoyi.app.domain.AppUserName;
import com.ruoyi.app.mapper.AppUserNameMapper;
import com.ruoyi.app.service.IAppUserNameService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-03-09
 */
@RequiredArgsConstructor
@Service
public class AppUserNameServiceImpl implements IAppUserNameService {

    private final AppUserNameMapper baseMapper;

    /**
     * 查询【请填写功能名称】
     */
    @Override
    public AppUserNameVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询【请填写功能名称】列表
     */
    @Override
    public TableDataInfo<AppUserNameVo> queryPageList(AppUserNameBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AppUserName> lqw = buildQueryWrapper(bo);
        Page<AppUserNameVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询【请填写功能名称】列表
     */
    @Override
    public List<AppUserNameVo> queryList(AppUserNameBo bo) {
        LambdaQueryWrapper<AppUserName> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AppUserName> buildQueryWrapper(AppUserNameBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AppUserName> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getName()), AppUserName::getName, bo.getName());
        return lqw;
    }

    /**
     * 新增【请填写功能名称】
     */
    @Override
    public Boolean insertByBo(AppUserNameBo bo) {
        AppUserName add = BeanUtil.toBean(bo, AppUserName.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改【请填写功能名称】
     */
    @Override
    public Boolean updateByBo(AppUserNameBo bo) {
        AppUserName update = BeanUtil.toBean(bo, AppUserName.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AppUserName entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除【请填写功能名称】
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
