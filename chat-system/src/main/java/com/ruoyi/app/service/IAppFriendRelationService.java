package com.ruoyi.app.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.app.domain.AppFriendRelation;
import com.ruoyi.app.domain.vo.AppFriendRelationVo;
import com.ruoyi.app.domain.bo.AppFriendRelationBo;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;
import com.sun.org.apache.xpath.internal.operations.Bool;

import java.util.Collection;
import java.util.List;

/**
 * APP用户关系Service接口
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface IAppFriendRelationService {

    /**
     * 查询APP用户关系
     */
    AppFriendRelationVo queryById(Long relationId);


    /**
     * 查询信息
     * @param id 用户
     * @param userId 操作用户
     * @param type 操作类型
     * @return
     */
    List<AppFriendRelationVo> queryList(Long id,Long userId,String type);


    /**
     * 查询一条信息
     * @param wrapper 查询条件
     * @return
     */
    AppFriendRelationVo queryByOne(LambdaQueryWrapper<AppFriendRelation> wrapper);

    /**
     * 查询APP用户关系列表
     */
    TableDataInfo<AppFriendRelationVo> queryPageList(AppFriendRelationBo bo, PageQuery pageQuery);

    /**
     * 查询APP用户关系列表
     */
    List<AppFriendRelationVo> queryList(AppFriendRelationBo bo);

    /**
     * 新增APP用户关系
     */
    Boolean insertByBo(AppFriendRelationBo bo);

    /**
     * 修改APP用户关系
     */
    Boolean updateByBo(AppFriendRelationBo bo);


    Boolean updateByVo(AppFriendRelationVo bo);

    /**
     * 校验并批量删除APP用户关系信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 删除数据
     * @param id 记录ID
     * @return
     */
    Boolean deleteWithValidById(Long id);
}
