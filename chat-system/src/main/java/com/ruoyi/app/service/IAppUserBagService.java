package com.ruoyi.app.service;

import com.ruoyi.app.domain.AppUserBag;
import com.ruoyi.app.domain.vo.AppUserBagVo;
import com.ruoyi.app.domain.bo.AppUserBagBo;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * App用户签到领取奖励Service接口
 *
 * <AUTHOR>
 * @date 2023-03-29
 */
public interface IAppUserBagService {

    /**
     * 查询App用户签到领取奖励
     */
    AppUserBagVo queryById(Long id);

    /**
     * 查询App用户签到领取奖励
     */
    AppUserBagVo queryByUserId(Long userId,String type);


    /**
     * 查询App用户签到领取奖励列表
     */
    TableDataInfo<AppUserBagVo> queryPageList(AppUserBagBo bo, PageQuery pageQuery);

    /**
     * 查询App用户签到领取奖励列表
     */
    List<AppUserBagVo> queryList(AppUserBagBo bo);

    /**
     * 新增App用户签到领取奖励
     */
    Boolean insertByBo(AppUserBagBo bo);

    /**
     * 修改App用户签到领取奖励
     */
    Boolean updateByBo(AppUserBagBo bo);

    /**
     * 修改App用户签到领取奖励
     */
    Boolean updateByVo(AppUserBagVo vo);


    /**
     * 校验并批量删除App用户签到领取奖励信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
