package com.ruoyi.app.service;

import com.ruoyi.app.domain.bo.AppRecordBo;
import com.ruoyi.app.domain.vo.AppRecordVo;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * APP用户通知记录Service接口
 *
 * <AUTHOR>
 * @date 2025-04-27
 */
public interface IAppRecordService {

    /**
     * 查询APP用户通知记录
     */
    AppRecordVo queryById(Long id);

    /**
     * 查询APP用户通知记录列表
     */
    TableDataInfo<AppRecordVo> queryPageList(AppRecordBo bo, PageQuery pageQuery);

    /**
     * 查询APP用户通知记录列表
     */
    List<AppRecordVo> queryList(AppRecordBo bo);

    /**
     * 新增APP用户通知记录
     */
    Boolean insertByBo(AppRecordBo bo);

    /**
     * 修改APP用户通知记录
     */
    Boolean updateByBo(AppRecordBo bo);

    /**
     * 修改APP用户通知记录
     */
    Boolean updateByBo(Long id,String status);


    /**
     * 校验并批量删除APP用户通知记录信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 删除动态和评论，同步删除通知
     * @param id
     * @param type
     */
    Boolean delete(Long id, String type);
}
