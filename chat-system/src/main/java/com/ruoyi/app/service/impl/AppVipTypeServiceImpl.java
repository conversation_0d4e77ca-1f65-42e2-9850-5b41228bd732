package com.ruoyi.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ruoyi.app.domain.bo.AppVipTypeBo;
import com.ruoyi.app.domain.vo.AppVipTypeVo;
import com.ruoyi.app.domain.AppVipType;
import com.ruoyi.app.mapper.AppVipTypeMapper;
import com.ruoyi.app.service.IAppVipTypeService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * APPVIP类型Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-16
 */
@RequiredArgsConstructor
@Service
public class AppVipTypeServiceImpl implements IAppVipTypeService {

    private final AppVipTypeMapper baseMapper;

    /**
     * 查询APPVIP类型
     */
    @Override
    public AppVipTypeVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询APPVIP类型列表
     */
    @Override
    public TableDataInfo<AppVipTypeVo> queryPageList(AppVipTypeBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AppVipType> lqw = buildQueryWrapper(bo);
        Page<AppVipTypeVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询APPVIP类型列表
     */
    @Override
    public List<AppVipTypeVo> queryList(AppVipTypeBo bo) {
        LambdaQueryWrapper<AppVipType> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AppVipType> buildQueryWrapper(AppVipTypeBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AppVipType> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getName()), AppVipType::getName, bo.getName());
        lqw.eq(bo.getDay() != null, AppVipType::getDay, bo.getDay());
        lqw.eq(bo.getAndroidPrice() != null, AppVipType::getAndroidPrice, bo.getAndroidPrice());
        lqw.eq(bo.getIosPrice() != null, AppVipType::getIosPrice, bo.getIosPrice());
        lqw.eq(bo.getIosName() != null, AppVipType::getIosName, bo.getIosName());
        lqw.eq(bo.getDiscount() != null, AppVipType::getDiscount, bo.getDiscount());
        return lqw;
    }

    /**
     * 新增APPVIP类型
     */
    @Override
    public Boolean insertByBo(AppVipTypeBo bo) {
        AppVipType add = BeanUtil.toBean(bo, AppVipType.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改APPVIP类型
     */
    @Override
    public Boolean updateByBo(AppVipTypeBo bo) {
        AppVipType update = BeanUtil.toBean(bo, AppVipType.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AppVipType entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除APPVIP类型
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
