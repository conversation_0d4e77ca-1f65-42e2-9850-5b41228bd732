package com.ruoyi.app.service;

import com.ruoyi.app.domain.AppMate;
import com.ruoyi.app.domain.vo.AppMateVo;
import com.ruoyi.app.domain.bo.AppMateBo;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * APP用户匹配配置和记录Service接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
public interface IAppMateService {

    /**
     * 查询APP用户匹配配置和记录
     */
    AppMateVo queryById(Long id);

    /**
     * 查询APP用户
     */
    AppMateVo queryByUserId(Long userId,String type);

    /**
     * 查询APP用户匹配配置和记录列表
     */
    TableDataInfo<AppMateVo> queryPageList(AppMateBo bo, PageQuery pageQuery);

    /**
     * 查询APP用户匹配配置和记录列表
     */
    List<AppMateVo> queryList(AppMateBo bo);

    /**
     * 新增APP用户匹配配置和记录
     */
    Boolean insertByBo(AppMateBo bo);

    /**
     * 修改APP用户匹配配置和记录
     */
    Boolean updateByBo(AppMateBo bo);

    /**
     * 修改APP用户匹配配置和记录
     */
    Boolean updateByVo(AppMateVo vo);

    /**
     * 校验并批量删除APP用户匹配配置和记录信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
