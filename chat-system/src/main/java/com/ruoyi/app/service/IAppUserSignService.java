package com.ruoyi.app.service;

import com.ruoyi.app.domain.AppUserSign;
import com.ruoyi.app.domain.bo.AppUserSignBo;
import com.ruoyi.app.domain.vo.AppUserSignVo;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * App用户签到记录Service接口
 *
 * <AUTHOR>
 * @date 2025-03-28
 */
public interface IAppUserSignService {

    /**
     * 查询App用户签到记录
     */
    AppUserSignVo queryById(Long id);

    /**
     * 查询App用户签到记录列表
     */
    TableDataInfo<AppUserSignVo> queryPageList(AppUserSignBo bo, PageQuery pageQuery);

    /**
     * 查询App用户签到记录列表
     */
    List<AppUserSignVo> queryList(AppUserSignBo bo);

    /**
     * 新增App用户签到记录
     */
    Boolean insertByBo(AppUserSignBo bo);

    /**
     * 新增App用户签到记录
     */
    Boolean insert(AppUserSign appUserSign);

    /**
     * 修改App用户签到记录
     */
    Boolean updateByBo(AppUserSignBo bo);

    /**
     * 修改App用户签到记录
     */
    Boolean update(AppUserSign appUserSign);

    /**
     * 校验并批量删除App用户签到记录信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    AppUserSign queryUser(Long userId);

    AppUserSign userSign(Long userId, long time, AppUserSign userSign, Map<Integer, Integer> list);
}
