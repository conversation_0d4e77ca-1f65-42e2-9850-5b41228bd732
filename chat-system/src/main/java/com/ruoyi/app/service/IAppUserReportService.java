package com.ruoyi.app.service;

import com.ruoyi.app.domain.bo.AppUserReportBo;
import com.ruoyi.app.domain.vo.AppUserReportVo;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * APP举报信息Service接口
 *
 * <AUTHOR>
 * @date 2025-01-03
 */
public interface IAppUserReportService {

    /**
     * 查询APP举报信息
     */
    AppUserReportVo queryById(Long id);

    /**
     * 查询APP举报信息列表
     */
    TableDataInfo<AppUserReportVo> queryPageList(AppUserReportBo bo, PageQuery pageQuery);

    /**
     * 查询APP举报信息列表
     */
    List<AppUserReportVo> queryList(AppUserReportBo bo);

    /**
     * 新增APP举报信息
     */
    Boolean insertByBo(AppUserReportBo bo);

    /**
     * 修改APP举报信息
     */
    Boolean updateByBo(AppUserReportBo bo);

    /**
     * 校验并批量删除APP举报信息信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
