package com.ruoyi.app.service;

import com.ruoyi.app.domain.bo.AppLabelTypeBo;
import com.ruoyi.app.domain.vo.AppLabelTypeVo;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * APP标签类型Service接口
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
public interface IAppLabelTypeService {

    /**
     * 查询APP标签类型
     */
    AppLabelTypeVo queryById(Long id);

    /**
     * 查询APP标签类型列表
     */
    TableDataInfo<AppLabelTypeVo> queryPageList(AppLabelTypeBo bo, PageQuery pageQuery);

    /**
     * 查询APP标签类型列表
     */
    List<AppLabelTypeVo> queryList(AppLabelTypeBo bo);

    /**
     * 新增APP标签类型
     */
    Boolean insertByBo(AppLabelTypeBo bo);

    /**
     * 修改APP标签类型
     */
    Boolean updateByBo(AppLabelTypeBo bo);

    /**
     * 校验并批量删除APP标签类型信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
