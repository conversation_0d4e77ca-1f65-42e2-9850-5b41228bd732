package com.ruoyi.app.service;

import com.ruoyi.app.domain.AppOccupation;
import com.ruoyi.app.domain.vo.AppOccupationVo;
import com.ruoyi.app.domain.bo.AppOccupationBo;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * APP职业信息Service接口
 *
 * <AUTHOR>
 * @date 2022-12-12
 */
public interface IAppOccupationService {

    /**
     * 查询APP职业信息
     */
    AppOccupationVo queryById(Long id);

    /**
     * 查询APP职业信息列表
     */
    TableDataInfo<AppOccupationVo> queryPageList(AppOccupationBo bo, PageQuery pageQuery);

    /**
     * 查询APP职业信息列表
     */
    List<AppOccupationVo> queryList(AppOccupationBo bo);

    /**
     * 新增APP职业信息
     */
    Boolean insertByBo(AppOccupationBo bo);

    /**
     * 修改APP职业信息
     */
    Boolean updateByBo(AppOccupationBo bo);

    /**
     * 校验并批量删除APP职业信息信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
