package com.ruoyi.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ruoyi.app.domain.bo.AppAgreeBo;
import com.ruoyi.app.domain.vo.AppAgreeVo;
import com.ruoyi.app.domain.AppAgree;
import com.ruoyi.app.mapper.AppAgreeMapper;
import com.ruoyi.app.service.IAppAgreeService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * APP时光约会Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-25
 */
@RequiredArgsConstructor
@Service
public class AppAgreeServiceImpl implements IAppAgreeService {

    private final AppAgreeMapper baseMapper;

    /**
     * 查询APP时光约会
     */
    @Override
    public AppAgreeVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询APP时光约会列表
     */
    @Override
    public TableDataInfo<AppAgreeVo> queryPageList(AppAgreeBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AppAgree> lqw = buildQueryWrapper(bo);
        Page<AppAgreeVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询APP时光约会列表
     */
    @Override
    public List<AppAgreeVo> queryList(AppAgreeBo bo) {
        LambdaQueryWrapper<AppAgree> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AppAgree> buildQueryWrapper(AppAgreeBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AppAgree> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, AppAgree::getUserId, bo.getUserId());
        lqw.eq(bo.getSideId() != null, AppAgree::getSideId, bo.getSideId());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), AppAgree::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增APP时光约会
     */
    @Override
    public Boolean insertByBo(AppAgreeBo bo) {
        AppAgree add = BeanUtil.toBean(bo, AppAgree.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改APP时光约会
     */
    @Override
    public Boolean updateByBo(AppAgreeBo bo) {
        AppAgree update = BeanUtil.toBean(bo, AppAgree.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }


    /**
     * 修改APP时光约会
     */
    @Override
    public Boolean updateByVo(AppAgreeVo vo) {
        AppAgree update = BeanUtil.toBean(vo, AppAgree.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }


    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AppAgree entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除APP时光约会
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
