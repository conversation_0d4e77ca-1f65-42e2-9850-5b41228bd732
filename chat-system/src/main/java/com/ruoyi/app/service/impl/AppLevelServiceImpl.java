package com.ruoyi.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ruoyi.app.domain.bo.AppLevelBo;
import com.ruoyi.app.domain.vo.AppLevelVo;
import com.ruoyi.app.domain.AppLevel;
import com.ruoyi.app.mapper.AppLevelMapper;
import com.ruoyi.app.service.IAppLevelService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * AP用户等级Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-09-26
 */
@RequiredArgsConstructor
@Service
public class AppLevelServiceImpl implements IAppLevelService {

    private final AppLevelMapper baseMapper;

    /**
     * 查询AP用户等级
     */
    @Override
    public AppLevelVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }


    /**
     * 根据积分获取等级
     * @param number 积分
     * @return
     */
    @Override
    public AppLevelVo queryLevel(Integer number) {
        LambdaQueryWrapper<AppLevel> lqw =new LambdaQueryWrapper<>();
        lqw.orderByDesc(AppLevel::getActiveMin);
        List<AppLevelVo> appLevelVos = baseMapper.selectVoList(lqw);
        for (AppLevelVo appLevelVo : appLevelVos){
            if (number >= appLevelVo.getActiveMin()){
                return appLevelVo;
            }
        }
        return null;
    }

    /**
     * 查询AP用户等级列表
     */
    @Override
    public TableDataInfo<AppLevelVo> queryPageList(AppLevelBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AppLevel> lqw = buildQueryWrapper(bo);
        Page<AppLevelVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询AP用户等级列表
     */
    @Override
    public List<AppLevelVo> queryList(AppLevelBo bo) {
        LambdaQueryWrapper<AppLevel> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AppLevel> buildQueryWrapper(AppLevelBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AppLevel> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getName()), AppLevel::getName, bo.getName());
        return lqw;
    }

    /**
     * 新增AP用户等级
     */
    @Override
    public Boolean insertByBo(AppLevelBo bo) {
        AppLevel add = BeanUtil.toBean(bo, AppLevel.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改AP用户等级
     */
    @Override
    public Boolean updateByBo(AppLevelBo bo) {
        AppLevel update = BeanUtil.toBean(bo, AppLevel.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AppLevel entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除AP用户等级
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
