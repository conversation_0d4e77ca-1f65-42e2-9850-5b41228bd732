package com.ruoyi.app.service;

import com.ruoyi.app.domain.bo.AppCityDistBo;
import com.ruoyi.app.domain.vo.AppCityDistVo;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * APP城市列Service接口
 *
 * <AUTHOR>
 * @date 2025-03-01
 */
public interface IAppCityDistService {

    /**
     * 查询APP城市列
     */
    AppCityDistVo queryById(Long id);

    /**
     * 查询APP城市列列表
     */
    TableDataInfo<AppCityDistVo> queryPageList(AppCityDistBo bo, PageQuery pageQuery);

    /**
     * 查询APP城市列列表
     */
    List<AppCityDistVo> queryList(AppCityDistBo bo);


    /**
     * 查询APP城市列列表
     */
    List<AppCityDistVo> queryList();

    /**
     * 新增APP城市列
     */
    Boolean insertByBo(AppCityDistBo bo);

    /**
     * 修改APP城市列
     */
    Boolean updateByBo(AppCityDistBo bo);

    /**
     * 校验并批量删除APP城市列信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
