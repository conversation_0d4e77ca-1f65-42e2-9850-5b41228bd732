package com.ruoyi.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ruoyi.app.domain.bo.AppShowBo;
import com.ruoyi.app.domain.vo.AppShowVo;
import com.ruoyi.app.domain.AppShow;
import com.ruoyi.app.mapper.AppShowMapper;
import com.ruoyi.app.service.IAppShowService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 活动展馆管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
@RequiredArgsConstructor
@Service
public class AppShowServiceImpl implements IAppShowService {

    private final AppShowMapper baseMapper;

    /**
     * 查询活动展馆管理
     */
    @Override
    public AppShowVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询活动展馆管理列表
     */
    @Override
    public TableDataInfo<AppShowVo> queryPageList(AppShowBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AppShow> lqw = buildQueryWrapper(bo);
        Page<AppShowVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询活动展馆管理列表
     */
    @Override
    public List<AppShowVo> queryList(AppShowBo bo) {
        LambdaQueryWrapper<AppShow> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AppShow> buildQueryWrapper(AppShowBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AppShow> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getTitle()), AppShow::getTitle, bo.getTitle());
        lqw.eq(bo.getUserId() != null, AppShow::getUserId, bo.getUserId());
        return lqw;
    }

    /**
     * 新增活动展馆管理
     */
    @Override
    public Boolean insertByBo(AppShowBo bo) {
        AppShow add = BeanUtil.toBean(bo, AppShow.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改活动展馆管理
     */
    @Override
    public Boolean updateByBo(AppShowBo bo) {
        AppShow update = BeanUtil.toBean(bo, AppShow.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AppShow entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除活动展馆管理
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
