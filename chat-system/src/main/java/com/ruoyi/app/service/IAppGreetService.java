package com.ruoyi.app.service;

import com.ruoyi.app.domain.AppGreet;
import com.ruoyi.app.domain.vo.AppGreetVo;
import com.ruoyi.app.domain.bo.AppGreetBo;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * APP打招呼记录
 * Service接口
 *
 * <AUTHOR>
 * @date 2025-03-23
 */
public interface IAppGreetService {

    /**
     * 查询APP打招呼记录
     */
    AppGreetVo queryById(Long id);

    /**
     * 查询APP打招呼记录
     * 列表
     */
    TableDataInfo<AppGreetVo> queryPageList(AppGreetBo bo, PageQuery pageQuery);

    /**
     * 查询APP打招呼记录列表
     */
    List<AppGreetVo> queryList(AppGreetBo bo);


    /**
     * 查询APP打招呼记录列表
     */
    List<AppGreet> queryListDesc(AppGreetBo bo);


    /**
     * 新增APP打招呼记录
     */
    Boolean insertByBo(AppGreetBo bo);

    /**
     * 修改APP打招呼记录
     */
    Boolean updateByBo(AppGreetBo bo);

    /**
     * 批量修改APP打招呼记录
     */
    Boolean updates(List<AppGreet> appGreet);

    /**
     * 校验并批量删除APP打招呼记录
     * 信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
