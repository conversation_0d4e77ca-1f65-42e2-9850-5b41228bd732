package com.ruoyi.app.service;

import com.ruoyi.app.domain.AppDynamicEmoji;
import com.ruoyi.app.domain.vo.AppDynamicEmojiVo;
import com.ruoyi.app.domain.bo.AppDynamicEmojiBo;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 动态表情Service接口
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
public interface IAppDynamicEmojiService {

    /**
     * 查询动态表情
     */
    AppDynamicEmojiVo queryById(Long id);

    /**
     * 查询动态表情列表
     */
    TableDataInfo<AppDynamicEmojiVo> queryPageList(AppDynamicEmojiBo bo, PageQuery pageQuery);

    /**
     * 查询动态表情列表
     */
    List<AppDynamicEmojiVo> queryList(AppDynamicEmojiBo bo);

    /**
     * 新增动态表情
     */
    Boolean insertByBo(AppDynamicEmojiBo bo);

    /**
     * 修改动态表情
     */
    Boolean updateByBo(AppDynamicEmojiBo bo);

    /**
     * 校验并批量删除动态表情信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
