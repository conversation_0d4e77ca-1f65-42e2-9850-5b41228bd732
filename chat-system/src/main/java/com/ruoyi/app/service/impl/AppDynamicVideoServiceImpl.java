package com.ruoyi.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.app.domain.AppDynamic;
import com.ruoyi.app.domain.AppDynamicVideo;
import com.ruoyi.app.domain.bo.AppDynamicVideoBo;
import com.ruoyi.app.domain.vo.AppDynamicVideoVo;
import com.ruoyi.app.domain.vo.AppDynamicVo;
import com.ruoyi.app.mapper.AppDynamicMapper;
import com.ruoyi.app.mapper.AppDynamicVideoMapper;
import com.ruoyi.app.service.IAppDynamicVideoService;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * APP动态视频信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-01-11
 */
@RequiredArgsConstructor
@Service
public class AppDynamicVideoServiceImpl implements IAppDynamicVideoService {

    private final AppDynamicVideoMapper baseMapper;
    private final AppDynamicMapper appDynamicMapper;

    /**
     * 查询APP动态视频信息
     */
    @Override
    public AppDynamicVideoVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询APP动态视频信息列表
     */
    @Override
    public TableDataInfo<AppDynamicVideoVo> queryPageList(AppDynamicVideoBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AppDynamicVideo> lqw = buildQueryWrapper(bo);
        Page<AppDynamicVideoVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询APP动态视频信息列表
     */
    @Override
    public List<AppDynamicVideoVo> queryList(AppDynamicVideoBo bo) {
        LambdaQueryWrapper<AppDynamicVideo> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }


    /**
     * 查询APP动态视频信息列表
     */
    public List<AppDynamicVideoVo> queryOrderList(AppDynamicVideoBo bo) {
        LambdaQueryWrapper<AppDynamicVideo> lqw = buildQueryWrapper(bo);
        lqw.orderByDesc(AppDynamicVideo::getCreateTime);
        return baseMapper.selectVoList(lqw);
    }

    @Override
    public AppDynamicVideoVo queryOne(Long dynamicId) {
        LambdaQueryWrapper<AppDynamicVideo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AppDynamicVideo::getDynamicId, dynamicId).
            orderByAsc(AppDynamicVideo::getId);
        List<AppDynamicVideoVo> appDynamicVideoVos = baseMapper.selectVoList(wrapper);
        if (appDynamicVideoVos.size() > 0) {
            return appDynamicVideoVos.get(0);
        }
        return null;
    }

    @Override
    public List<AppDynamicVideoVo> countList(Long userId, Long count) {
        ArrayList<AppDynamicVideoVo> data = new ArrayList<>();
        LambdaQueryWrapper<AppDynamic> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AppDynamic::getUserId, userId);
        wrapper.orderByDesc(AppDynamic::getCreateTime);
        List<AppDynamicVo> appDynamicVos = appDynamicMapper.selectVoList(wrapper);
        for (AppDynamicVo e : appDynamicVos) {
            AppDynamicVideoBo video = new AppDynamicVideoBo();
            video.setDynamicId(e.getId());
            List<AppDynamicVideoVo> videosVo = queryOrderList(video);
            data.addAll(videosVo);
            if (data.size() >= count) {
                break;
            }
        }
        return data;
    }

    private LambdaQueryWrapper<AppDynamicVideo> buildQueryWrapper(AppDynamicVideoBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AppDynamicVideo> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getUrl()), AppDynamicVideo::getUrl, bo.getUrl());
        lqw.eq(StringUtils.isNotBlank(bo.getImage()), AppDynamicVideo::getImage, bo.getImage());
        lqw.eq(bo.getDynamicId() != null, AppDynamicVideo::getDynamicId, bo.getDynamicId());
        lqw.eq(bo.getHeight() != null, AppDynamicVideo::getHeight, bo.getHeight());
        lqw.eq(bo.getWidth() != null, AppDynamicVideo::getWidth, bo.getWidth());
        return lqw;
    }

    /**
     * 新增APP动态视频信息
     */
    @Override
    public Boolean insertByBo(AppDynamicVideoBo bo) {
        AppDynamicVideo add = BeanUtil.toBean(bo, AppDynamicVideo.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改APP动态视频信息
     */
    @Override
    public Boolean updateByBo(AppDynamicVideoBo bo) {
        AppDynamicVideo update = BeanUtil.toBean(bo, AppDynamicVideo.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AppDynamicVideo entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除APP动态视频信息
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
