package com.ruoyi.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ruoyi.app.domain.bo.AppMateBo;
import com.ruoyi.app.domain.vo.AppMateVo;
import com.ruoyi.app.domain.AppMate;
import com.ruoyi.app.mapper.AppMateMapper;
import com.ruoyi.app.service.IAppMateService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * APP用户匹配配置和记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-05-16
 */
@RequiredArgsConstructor
@Service
public class AppMateServiceImpl implements IAppMateService {

    private final AppMateMapper baseMapper;

    /**
     * 查询APP用户匹配配置和记录
     */
    @Override
    public AppMateVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }


    /**
     * 查询APP用户匹配配置和记录
     */
    @Override
    public AppMateVo queryByUserId(Long userId, String type) {
        LambdaQueryWrapper<AppMate> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AppMate::getUserId, userId).eq(AppMate::getType, type);
        return baseMapper.selectVoOne(wrapper);
    }

    /**
     * 查询APP用户匹配配置和记录列表
     */
    @Override
    public TableDataInfo<AppMateVo> queryPageList(AppMateBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AppMate> lqw = buildQueryWrapper(bo);
        Page<AppMateVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询APP用户匹配配置和记录列表
     */
    @Override
    public List<AppMateVo> queryList(AppMateBo bo) {
        LambdaQueryWrapper<AppMate> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AppMate> buildQueryWrapper(AppMateBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AppMate> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, AppMate::getUserId, bo.getUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getOnLine()), AppMate::getOnLine, bo.getOnLine());
        lqw.eq(StringUtils.isNotBlank(bo.getUseMate()), AppMate::getUseMate, bo.getUseMate());
        lqw.eq(StringUtils.isNotBlank(bo.getDistance()), AppMate::getDistance, bo.getDistance());
        lqw.eq(StringUtils.isNotBlank(bo.getInfoComplete()), AppMate::getInfoComplete, bo.getInfoComplete());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), AppMate::getType, bo.getType());
        lqw.eq(bo.getRecordTime() != null, AppMate::getRecordTime, bo.getRecordTime());
        return lqw;
    }

    /**
     * 新增APP用户匹配配置和记录
     */
    @Override
    public Boolean insertByBo(AppMateBo bo) {
        AppMate add = BeanUtil.toBean(bo, AppMate.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改APP用户匹配配置和记录
     */
    @Override
    public Boolean updateByBo(AppMateBo bo) {
        AppMate update = BeanUtil.toBean(bo, AppMate.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }


    /**
     * 修改APP用户匹配配置和记录
     */
    @Override
    public Boolean updateByVo(AppMateVo vo) {
        AppMate update = BeanUtil.toBean(vo, AppMate.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AppMate entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除APP用户匹配配置和记录
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
