package com.ruoyi.app.service;

import com.ruoyi.app.domain.AppCode;
import com.ruoyi.app.domain.vo.AppCodeVo;
import com.ruoyi.app.domain.bo.AppCodeBo;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * APP瓶盖码Service接口
 *
 * <AUTHOR>
 * @date 2025-04-03
 */
public interface IAppCodeService {

    /**
     * 查询APP瓶盖码
     */
    AppCodeVo queryById(String id);


    AppCodeVo queryByCode(String code);

    AppCodeVo queryByDesCode(String desCode);

    /**
     * 查询APP瓶盖码列表
     */
    TableDataInfo<AppCodeVo> queryPageList(AppCodeBo bo, PageQuery pageQuery);

    /**
     * 查询APP瓶盖码列表
     */
    List<AppCodeVo> queryList(AppCodeBo bo);

    /**
     * 新增APP瓶盖码
     */
    Boolean insertByBo(AppCodeBo bo);

    /**
     * 新增APP瓶盖码
     */
    Boolean inserts(List<AppCode> list);

    /**
     * 修改APP瓶盖码
     */
    Boolean updateByBo(AppCodeBo bo);

    /**
     * 修改APP瓶盖码
     */
    Boolean updateByVo(AppCodeVo vo);

    /**
     * 校验并批量删除APP瓶盖码信息
     */
    Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);
}
