package com.ruoyi.app.service;

import com.ruoyi.app.domain.bo.AppDynamicVideoBo;
import com.ruoyi.app.domain.vo.AppDynamicVideoVo;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * APP动态视频信息Service接口
 *
 * <AUTHOR>
 * @date 2023-01-11
 */
public interface IAppDynamicVideoService {

    /**
     * 查询APP动态视频信息
     */
    AppDynamicVideoVo queryById(Long id);

    /**
     * 查询APP动态视频信息列表
     */
    TableDataInfo<AppDynamicVideoVo> queryPageList(AppDynamicVideoBo bo, PageQuery pageQuery);

    /**
     * 查询APP动态视频信息列表
     */
    List<AppDynamicVideoVo> queryList(AppDynamicVideoBo bo);

    /**
     * 查询APP动态视频信息列表
     */
    AppDynamicVideoVo queryOne(Long dynamicId);

    /**
     * 查询指定条数的数据
     */
    List<AppDynamicVideoVo> countList(Long userId,Long count);

    /**
     * 新增APP动态视频信息
     */
    Boolean insertByBo(AppDynamicVideoBo bo);

    /**
     * 修改APP动态视频信息
     */
    Boolean updateByBo(AppDynamicVideoBo bo);

    /**
     * 校验并批量删除APP动态视频信息信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
