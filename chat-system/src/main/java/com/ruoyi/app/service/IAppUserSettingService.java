package com.ruoyi.app.service;

import com.ruoyi.app.domain.AppUserSetting;
import com.ruoyi.app.domain.vo.AppUserSettingVo;
import com.ruoyi.app.domain.bo.AppUserSettingBo;
import com.ruoyi.app.domain.vo.AppUserVo;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * APP用户信息设置Service接口
 *
 * <AUTHOR>
 * @date 2025-02-02
 */
public interface IAppUserSettingService {

    /**
     * 查询APP用户信息设置
     */
    AppUserSettingVo queryById(Long id);

    /**
     * 查询APP用户信息设置
     */
    AppUserSettingVo queryByUserId(Long uid);

    /**
     * 查询APP用户信息设置列表
     */
    TableDataInfo<AppUserSettingVo> queryPageList(AppUserSettingBo bo, PageQuery pageQuery);

    /**
     * 查询APP用户信息设置列表
     */
    List<AppUserSettingVo> queryList(AppUserSettingBo bo);


    List<AppUserVo> queryList(Map<String, Double> doubleMap);

    /**
     * 新增APP用户信息设置
     */
    Boolean insertByBo(AppUserSettingBo bo);

    /**
     * 新增默认用户配置信息
     *
     * @return
     */
    Boolean insert(Long id);

    /**
     * 修改APP用户信息设置
     */
    Boolean updateByBo(AppUserSettingBo bo);
    Boolean updateByVo(AppUserSettingVo vo);

    /**
     * 校验并批量删除APP用户信息设置信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
