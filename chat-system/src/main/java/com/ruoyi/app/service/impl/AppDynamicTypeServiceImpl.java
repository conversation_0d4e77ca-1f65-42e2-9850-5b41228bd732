package com.ruoyi.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ruoyi.app.domain.bo.AppDynamicTypeBo;
import com.ruoyi.app.domain.vo.AppDynamicTypeVo;
import com.ruoyi.app.domain.AppDynamicType;
import com.ruoyi.app.mapper.AppDynamicTypeMapper;
import com.ruoyi.app.service.IAppDynamicTypeService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * APP用户动态话题类型Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-18
 */
@RequiredArgsConstructor
@Service
public class AppDynamicTypeServiceImpl implements IAppDynamicTypeService {

    private final AppDynamicTypeMapper baseMapper;

    /**
     * 查询APP用户动态话题类型
     */
    @Override
    public AppDynamicTypeVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }


    /**
     * 查询APP用户动态话题类型
     */
    @Override
    public AppDynamicType queryByName(String name) {
        LambdaQueryWrapper<AppDynamicType> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AppDynamicType::getName, name);
        return baseMapper.selectOne(wrapper);
    }


    /**
     * 查询APP用户动态话题类型列表
     */
    @Override
    public TableDataInfo<AppDynamicTypeVo> queryPageList(AppDynamicTypeBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AppDynamicType> lqw = buildQueryWrapper(bo);
        Page<AppDynamicTypeVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询APP用户动态话题类型列表
     */
    @Override
    public List<AppDynamicTypeVo> queryList(AppDynamicTypeBo bo) {
        LambdaQueryWrapper<AppDynamicType> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AppDynamicType> buildQueryWrapper(AppDynamicTypeBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AppDynamicType> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, AppDynamicType::getUserId, bo.getUserId());
        lqw.like(StringUtils.isNotBlank(bo.getName()), AppDynamicType::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getIntroduce()), AppDynamicType::getIntroduce, bo.getIntroduce());
        lqw.eq(StringUtils.isNotBlank(bo.getImage()), AppDynamicType::getImage, bo.getImage());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), AppDynamicType::getType, bo.getType());
        return lqw;
    }

    /**
     * 新增APP用户动态话题类型
     */
    @Override
    public Boolean insertByBo(AppDynamicTypeBo bo) {
        AppDynamicType add = BeanUtil.toBean(bo, AppDynamicType.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 新增APP用户动态话题类型
     */
    @Override
    public Boolean insert(AppDynamicType add) {
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            add.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改APP用户动态话题类型
     */
    @Override
    public Boolean updateByBo(AppDynamicTypeBo bo) {
        AppDynamicType update = BeanUtil.toBean(bo, AppDynamicType.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 修改APP用户动态话题类型
     */
    @Override
    public Boolean update(AppDynamicType update) {
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AppDynamicType entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除APP用户动态话题类型
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
