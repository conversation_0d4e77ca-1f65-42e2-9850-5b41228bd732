package com.ruoyi.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ruoyi.app.domain.bo.AppFeedbackBo;
import com.ruoyi.app.domain.vo.AppFeedbackVo;
import com.ruoyi.app.domain.AppFeedback;
import com.ruoyi.app.mapper.AppFeedbackMapper;
import com.ruoyi.app.service.IAppFeedbackService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * APP用户意见反馈Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-02-09
 */
@RequiredArgsConstructor
@Service
public class AppFeedbackServiceImpl implements IAppFeedbackService {

    private final AppFeedbackMapper baseMapper;

    /**
     * 查询APP用户意见反馈
     */
    @Override
    public AppFeedbackVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询APP用户意见反馈列表
     */
    @Override
    public TableDataInfo<AppFeedbackVo> queryPageList(AppFeedbackBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AppFeedback> lqw = buildQueryWrapper(bo);
        Page<AppFeedbackVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询APP用户意见反馈列表
     */
    @Override
    public List<AppFeedbackVo> queryList(AppFeedbackBo bo) {
        LambdaQueryWrapper<AppFeedback> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AppFeedback> buildQueryWrapper(AppFeedbackBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AppFeedback> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, AppFeedback::getUserId, bo.getUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), AppFeedback::getType, bo.getType());
        lqw.eq(StringUtils.isNotBlank(bo.getPhone()), AppFeedback::getPhone, bo.getPhone());
        lqw.eq(StringUtils.isNotBlank(bo.getContext()), AppFeedback::getContext, bo.getContext());
        return lqw;
    }

    /**
     * 新增APP用户意见反馈
     */
    @Override
    public Boolean insertByBo(AppFeedbackBo bo) {
        AppFeedback add = BeanUtil.toBean(bo, AppFeedback.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改APP用户意见反馈
     */
    @Override
    public Boolean updateByBo(AppFeedbackBo bo) {
        AppFeedback update = BeanUtil.toBean(bo, AppFeedback.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AppFeedback entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除APP用户意见反馈
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
