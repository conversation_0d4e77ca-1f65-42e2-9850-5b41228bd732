package com.ruoyi.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ruoyi.app.domain.bo.AppThirdUserBo;
import com.ruoyi.app.domain.vo.AppThirdUserVo;
import com.ruoyi.app.domain.AppThirdUser;
import com.ruoyi.app.mapper.AppThirdUserMapper;
import com.ruoyi.app.service.IAppThirdUserService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * APP用户第三方登录Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-01-07
 */
@RequiredArgsConstructor
@Service
public class AppThirdUserServiceImpl implements IAppThirdUserService {

    private final AppThirdUserMapper baseMapper;

    /**
     * 查询APP用户第三方登录
     */
    @Override
    public AppThirdUserVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询APP用户第三方登录列表
     */
    @Override
    public TableDataInfo<AppThirdUserVo> queryPageList(AppThirdUserBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AppThirdUser> lqw = buildQueryWrapper(bo);
        Page<AppThirdUserVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询APP用户第三方登录列表
     */
    @Override
    public List<AppThirdUserVo> queryList(AppThirdUserBo bo) {
        LambdaQueryWrapper<AppThirdUser> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 查询APP用户第三方登录信息
     * @param type 第三方类型
     * @param token 第三方TOKEN
     * @return
     */
    @Override
    public AppThirdUserVo queryByTypeAndToken(String type, String token) {
        LambdaQueryWrapper<AppThirdUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AppThirdUser::getToken, token).eq(AppThirdUser::getType, type);
        return baseMapper.selectVoOne(wrapper);
    }

    /**
     * 查询APP用户第三方登录信息
     * @param type 第三方类型
     * @param userId 用户ID
     * @return
     */
    @Override
    public AppThirdUserVo queryByTypeAndUserId(String type, Long userId) {
        LambdaQueryWrapper<AppThirdUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AppThirdUser::getUserId, userId).eq(AppThirdUser::getType, type);
        return baseMapper.selectVoOne(wrapper);
    }

    private LambdaQueryWrapper<AppThirdUser> buildQueryWrapper(AppThirdUserBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AppThirdUser> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, AppThirdUser::getUserId, bo.getUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getToken()), AppThirdUser::getToken, bo.getToken());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), AppThirdUser::getType, bo.getType());
        lqw.eq(StringUtils.isNotBlank(bo.getImage()), AppThirdUser::getImage, bo.getImage());
        return lqw;
    }

    /**
     * 新增APP用户第三方登录
     */
    @Override
    public Boolean insertByBo(AppThirdUserBo bo) {
        AppThirdUser add = BeanUtil.toBean(bo, AppThirdUser.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改APP用户第三方登录
     */
    @Override
    public Boolean updateByBo(AppThirdUserBo bo) {
        AppThirdUser update = BeanUtil.toBean(bo, AppThirdUser.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AppThirdUser entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除APP用户第三方登录
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
