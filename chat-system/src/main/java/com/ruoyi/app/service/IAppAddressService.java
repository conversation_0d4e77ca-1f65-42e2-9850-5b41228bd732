package com.ruoyi.app.service;

import com.ruoyi.app.domain.bo.AppAddressBo;
import com.ruoyi.app.domain.vo.AppAddressVo;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * APP用户地址信息Service接口
 *
 * <AUTHOR>
 * @date 2022-12-21
 */
public interface IAppAddressService {

    /**
     * 查询APP用户地址信息
     */
    AppAddressVo queryById(Long id);

    /**
     * 查询APP用户地址信息列表
     */
    TableDataInfo<AppAddressVo> queryPageList(AppAddressBo bo, PageQuery pageQuery);

    /**
     * 查询APP用户地址信息列表
     */
    List<AppAddressVo> queryList(AppAddressBo bo);

    /**
     * 新增APP用户地址信息
     */
    Boolean insertByBo(AppAddressBo bo);

    /**
     * 修改APP用户地址信息
     */
    Boolean updateByBo(AppAddressBo bo);

    /**
     * 修改APP用户地址信息
     */
    Boolean updateByVo(AppAddressVo vo);

    /**
     * 校验并批量删除APP用户地址信息信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    Object saveOrUpdate(AppAddressBo bo);

    AppAddressVo queryUserId(Long userId,String status);
}
