package com.ruoyi.app.service;

import com.ruoyi.app.domain.bo.AppAssetBo;
import com.ruoyi.app.domain.vo.AppAssetVo;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

/**
 * APP用户资产信息Service接口
 *
 * <AUTHOR>
 * @date 2025-12-09
 */
public interface IAppAssetService {

    /**
     * 查询APP用户资产信息
     */
    AppAssetVo queryById(Long id);


    AppAssetVo queryByUid(Long uid);

    /**
     * 查询APP用户资产信息列表
     */
    TableDataInfo<AppAssetVo> queryPageList(AppAssetBo bo, PageQuery pageQuery);

    /**
     * 查询APP用户资产信息列表
     */
    List<AppAssetVo> queryList(AppAssetBo bo);

    /**
     * 新增APP用户资产信息
     */
    Boolean insertByBo(AppAssetBo bo);


    /**
     * 新增APP用户资产信息
     */
    Boolean insert(Long userId);

    /**
     * 修改APP用户资产信息
     */
    Boolean updateByBo(AppAssetBo bo);

    /**
     * 校验并批量删除APP用户资产信息信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);


    /**
     * 增加资产
     *
     * @param uid
     * @param amount
     */
    Boolean addBalance(Long uid, BigDecimal amount);

    /**
     * 增加积分
     *
     * @param uid
     * @param amount
     */
    Boolean addIntegral(Long uid, BigDecimal amount);

    /**
     * 减少资产
     *
     * @param uid
     * @param amount
     */
    Boolean subtractBalance(Long uid, BigDecimal amount);

    /**
     *
     * @param uid
     * @param amount
     * @return
     */
    Boolean subtractIntegral(Long uid, BigDecimal amount);
}
