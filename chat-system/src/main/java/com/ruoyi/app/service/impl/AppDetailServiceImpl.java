package com.ruoyi.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.constant.TaskConstants;
import com.ruoyi.common.enums.CoinType;
import com.ruoyi.common.enums.DetailType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ruoyi.app.domain.bo.AppDetailBo;
import com.ruoyi.app.domain.vo.AppDetailVo;
import com.ruoyi.app.domain.AppDetail;
import com.ruoyi.app.mapper.AppDetailMapper;
import com.ruoyi.app.service.IAppDetailService;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * APP用户账单明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@RequiredArgsConstructor
@Service
public class AppDetailServiceImpl implements IAppDetailService {

    private final AppDetailMapper baseMapper;

    /**
     * 查询APP用户账单明细
     */
    @Override
    public AppDetailVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询APP用户账单明细列表
     */
    @Override
    public TableDataInfo<AppDetailVo> queryPageList(AppDetailBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AppDetail> lqw = buildQueryWrapper(bo);
        Page<AppDetailVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询APP用户账单明细列表
     */
    @Override
    public List<AppDetailVo> queryList(AppDetailBo bo) {
        LambdaQueryWrapper<AppDetail> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AppDetail> buildQueryWrapper(AppDetailBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AppDetail> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, AppDetail::getUserId, bo.getUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), AppDetail::getType, bo.getType());
        lqw.eq(StringUtils.isNotBlank(bo.getFlow()), AppDetail::getFlow, bo.getFlow());
        lqw.eq(StringUtils.isNotBlank(bo.getCoinTyp()), AppDetail::getCoinTyp, bo.getCoinTyp());
        return lqw;
    }

    /**
     * 新增APP用户账单明细
     */
    @Override
    public Boolean insertByBo(AppDetailBo bo) {
        AppDetail add = BeanUtil.toBean(bo, AppDetail.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public Boolean insert(Long userId, BigDecimal balance, BigDecimal freeBalance, String flow, String coinType, String type, String remark, Long fromId) {
        AppDetailBo detailBo = new AppDetailBo();
        detailBo.setUserId(userId);
        detailBo.setBalance(balance);
        detailBo.setCoinTyp(coinType);
        detailBo.setType(type);
        detailBo.setFlow(flow);
        detailBo.setRemark(remark);
        detailBo.setFromId(fromId);
        return insertByBo(detailBo);
    }

    /**
     * 修改APP用户账单明细
     */
    @Override
    public Boolean updateByBo(AppDetailBo bo) {
        AppDetail update = BeanUtil.toBean(bo, AppDetail.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AppDetail entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除APP用户账单明细
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
