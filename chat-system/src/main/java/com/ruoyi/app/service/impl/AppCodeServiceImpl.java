package com.ruoyi.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ruoyi.app.domain.bo.AppCodeBo;
import com.ruoyi.app.domain.vo.AppCodeVo;
import com.ruoyi.app.domain.AppCode;
import com.ruoyi.app.mapper.AppCodeMapper;
import com.ruoyi.app.service.IAppCodeService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * APP瓶盖码Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-04-03
 */
@RequiredArgsConstructor
@Service
public class AppCodeServiceImpl implements IAppCodeService {

    private final AppCodeMapper baseMapper;

    /**
     * 查询APP瓶盖码
     */
    @Override
    public AppCodeVo queryById(String id) {
        return baseMapper.selectVoById(id);
    }


    /**
     * 查询APP瓶盖码
     */
    @Override
    public AppCodeVo queryByCode(String code) {
        LambdaQueryWrapper<AppCode> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AppCode::getCode, code);
        return baseMapper.selectVoOne(wrapper);
    }

    /**
     * 查询APP瓶盖码
     */
    @Override
    public AppCodeVo queryByDesCode(String desCode) {
        LambdaQueryWrapper<AppCode> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AppCode::getDesCode, desCode);
        return baseMapper.selectVoOne(wrapper);
    }

    /**
     * 查询APP瓶盖码列表
     */
    @Override
    public TableDataInfo<AppCodeVo> queryPageList(AppCodeBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AppCode> lqw = buildQueryWrapper(bo);
        Page<AppCodeVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询APP瓶盖码列表
     */
    @Override
    public List<AppCodeVo> queryList(AppCodeBo bo) {
        LambdaQueryWrapper<AppCode> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AppCode> buildQueryWrapper(AppCodeBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AppCode> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getCode()), AppCode::getCode, bo.getCode());
        lqw.eq(StringUtils.isNotBlank(bo.getDesCode()), AppCode::getDesCode, bo.getDesCode());
        lqw.eq(bo.getStatus() != null, AppCode::getStatus, bo.getStatus());
        lqw.eq(bo.getUserId() != null, AppCode::getUserId, bo.getUserId());
        return lqw;
    }

    /**
     * 新增APP瓶盖码
     */
    @Override
    public Boolean insertByBo(AppCodeBo bo) {
        AppCode add = BeanUtil.toBean(bo, AppCode.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }


    /**
     * 新增APP瓶盖码
     */
    @Override
    public Boolean inserts(List<AppCode> list) {
        return baseMapper.insertBatch(list);
    }

    /**
     * 修改APP瓶盖码
     */
    @Override
    public Boolean updateByBo(AppCodeBo bo) {
        AppCode update = BeanUtil.toBean(bo, AppCode.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }


    /**
     * 修改APP瓶盖码
     */
    @Override
    public Boolean updateByVo(AppCodeVo vo) {
        AppCode update = BeanUtil.toBean(vo, AppCode.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }


    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AppCode entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除APP瓶盖码
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
