package com.ruoyi.app.service;

import com.ruoyi.app.domain.AppUserVip;
import com.ruoyi.app.domain.vo.AppUserVipVo;
import com.ruoyi.app.domain.bo.AppUserVipBo;
import com.ruoyi.app.domain.vo.AppVipOrderVo;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * APP用户VIPService接口
 *
 * <AUTHOR>
 * @date 2025-03-16
 */
public interface IAppUserVipService {

    /**
     * 查询APP用户VIP
     */
    AppUserVipVo queryById(Long id);


    AppUserVipVo queryByUserId(Long userId);

    /**
     * 查询APP用户VIP列表
     */
    TableDataInfo<AppUserVipVo> queryPageList(AppUserVipBo bo, PageQuery pageQuery);

    /**
     * 查询APP用户VIP列表
     */
    List<AppUserVipVo> queryList(AppUserVipBo bo);

    /**
     * 新增APP用户VIP
     */
    Boolean insertByBo(AppUserVipBo bo);

    /**
     * 修改APP用户VIP
     */
    Boolean updateByBo(AppUserVipBo bo);
    Boolean updateByVo(AppUserVipVo vo);

    /**
     * 判断用户是否VIP
     */
    String userVip(Long userId);


    AppUserVipVo userVipId(Long userId);

    /**
     * 开通VIP，增加VIP的时间
     */
    void addVipTime(AppVipOrderVo vipOrder, String tradeNo, String outTradeNo, String payType);

    /**
     * 校验并批量删除APP用户VIP信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
    Boolean deleteWithValidById(Long id);
}
