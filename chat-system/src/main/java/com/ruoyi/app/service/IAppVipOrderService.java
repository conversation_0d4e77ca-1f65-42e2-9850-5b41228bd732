package com.ruoyi.app.service;

import com.ruoyi.app.domain.AppVipOrder;
import com.ruoyi.app.domain.vo.AppVipOrderVo;
import com.ruoyi.app.domain.bo.AppVipOrderBo;
import com.ruoyi.app.domain.vo.AppVipTypeVo;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * APPv会员VIP订单Service接口
 *
 * <AUTHOR>
 * @date 2025-03-17
 */
public interface IAppVipOrderService {

    /**
     * 查询APPv会员VIP订单
     */
    AppVipOrderVo queryById(Long id);

    /**
     * 查询APPv会员VIP订单
     */
    AppVipOrderVo queryOrderNo(String orderNo);

    /**
     * 查询APPv会员VIP订单列表
     */
    TableDataInfo<AppVipOrderVo> queryPageList(AppVipOrderBo bo, PageQuery pageQuery);

    /**
     * 查询APPv会员VIP订单列表
     */
    List<AppVipOrderVo> queryList(AppVipOrderBo bo);

    /**
     * 新增APPv会员VIP订单
     */
    Boolean insertByBo(AppVipOrderBo bo);

    /**
     * 修改APPv会员VIP订单
     */
    Boolean updateByBo(AppVipOrderBo bo);

    /**
     * 修改APPv会员VIP订单
     */
    Boolean updateByVo(AppVipOrderVo vo);

    /**
     * 校验并批量删除APPv会员VIP订单信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    AppVipOrder createOrder(AppVipTypeVo appVipTypeVo, Long userId);
}
