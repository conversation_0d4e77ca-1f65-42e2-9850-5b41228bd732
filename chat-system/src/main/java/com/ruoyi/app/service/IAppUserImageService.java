package com.ruoyi.app.service;

import com.ruoyi.app.domain.bo.AppUserImageBo;
import com.ruoyi.app.domain.vo.AppUserImageVo;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * APP用户照片Service接口
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface IAppUserImageService {

    /**
     * 查询APP用户照片
     */
    AppUserImageVo queryById(Long id);


    /**
     * 查询APP用户照片列表
     */
    TableDataInfo<AppUserImageVo> queryPageList(AppUserImageBo bo, PageQuery pageQuery);

    /**
     * 查询APP用户照片列表
     */
    List<AppUserImageVo> queryList(AppUserImageBo bo);


    /**
     * 查询APP用户照片列表
     */
    List<AppUserImageVo> queryList(Long uid);

    /**
     * 新增APP用户照片
     */
    Boolean insertByBo(AppUserImageBo bo);

    /**
     * 修改APP用户照片
     */
    Boolean updateByBo(AppUserImageBo bo);

    /**
     * 校验并批量删除APP用户照片信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据ID删除照片
     */
    Boolean deleteWithValidById(Long id,Long userId);
}
