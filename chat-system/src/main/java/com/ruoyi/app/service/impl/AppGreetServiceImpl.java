package com.ruoyi.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.app.mapper.AppGreetMapper;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ruoyi.app.domain.bo.AppGreetBo;
import com.ruoyi.app.domain.vo.AppGreetVo;
import com.ruoyi.app.domain.AppGreet;
import com.ruoyi.app.service.IAppGreetService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * APP打招呼记录
 * Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-03-23
 */
@RequiredArgsConstructor
@Service
public class AppGreetServiceImpl implements IAppGreetService {

    private final AppGreetMapper baseMapper;

    /**
     * 查询APP打招呼记录
     */
    @Override
    public AppGreetVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询APP打招呼记录
     * 列表
     */
    @Override
    public TableDataInfo<AppGreetVo> queryPageList(AppGreetBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AppGreet> lqw = buildQueryWrapper(bo);
        Page<AppGreetVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询APP打招呼记录列表
     */
    @Override
    public List<AppGreetVo> queryList(AppGreetBo bo) {
        LambdaQueryWrapper<AppGreet> lqw = buildQueryWrapper(bo);
        lqw.orderByAsc(AppGreet::getCreateTime);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 查询APP打招呼记录列表
     */
    @Override
    public List<AppGreet> queryListDesc(AppGreetBo bo) {
        LambdaQueryWrapper<AppGreet> lqw = buildQueryWrapper(bo);
        lqw.orderByDesc(AppGreet::getCreateTime);
        return baseMapper.selectList(lqw);
    }


    private LambdaQueryWrapper<AppGreet> buildQueryWrapper(AppGreetBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AppGreet> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, AppGreet::getUserId, bo.getUserId());
        lqw.eq(bo.getFromId() != null, AppGreet::getFromId, bo.getFromId());
        lqw.eq(bo.getNumber() != null, AppGreet::getNumber, bo.getNumber());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), AppGreet::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增APP打招呼记录
     */
    @Override
    public Boolean insertByBo(AppGreetBo bo) {
        AppGreet add = BeanUtil.toBean(bo, AppGreet.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改APP打招呼记录
     */
    @Override
    public Boolean updateByBo(AppGreetBo bo) {
        AppGreet update = BeanUtil.toBean(bo, AppGreet.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 批量修改APP打招呼记录
     */
    @Override
    public Boolean updates(List<AppGreet> appGreets) {
        return baseMapper.updateBatchById(appGreets);
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AppGreet entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除APP打招呼记录
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
