package com.ruoyi.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ruoyi.app.domain.bo.AppUserRemarkBo;
import com.ruoyi.app.domain.vo.AppUserRemarkVo;
import com.ruoyi.app.domain.AppUserRemark;
import com.ruoyi.app.mapper.AppUserRemarkMapper;
import com.ruoyi.app.service.IAppUserRemarkService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * APP用户备注Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
@RequiredArgsConstructor
@Service
public class AppUserRemarkServiceImpl implements IAppUserRemarkService {

    private final AppUserRemarkMapper baseMapper;

    /**
     * 查询APP用户备注
     */
    @Override
    public AppUserRemarkVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    @Override
    public AppUserRemarkVo queryUser(Long id, Long friendId) {
        LambdaQueryWrapper<AppUserRemark> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AppUserRemark::getUserId, id);
        wrapper.eq(AppUserRemark::getFriendId, friendId);
        return baseMapper.selectVoOne(wrapper);
    }

    /**
     * 查询APP用户备注列表
     */
    @Override
    public TableDataInfo<AppUserRemarkVo> queryPageList(AppUserRemarkBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AppUserRemark> lqw = buildQueryWrapper(bo);
        Page<AppUserRemarkVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询APP用户备注列表
     */
    @Override
    public List<AppUserRemarkVo> queryList(AppUserRemarkBo bo) {
        LambdaQueryWrapper<AppUserRemark> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AppUserRemark> buildQueryWrapper(AppUserRemarkBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AppUserRemark> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, AppUserRemark::getUserId, bo.getUserId());
        lqw.eq(bo.getFriendId() != null, AppUserRemark::getFriendId, bo.getFriendId());
        return lqw;
    }

    /**
     * 新增APP用户备注
     */
    @Override
    public Boolean insertByBo(AppUserRemarkBo bo) {
        AppUserRemark add = BeanUtil.toBean(bo, AppUserRemark.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改APP用户备注
     */
    @Override
    public Boolean updateByBo(AppUserRemarkBo bo) {
        AppUserRemark update = BeanUtil.toBean(bo, AppUserRemark.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 修改APP用户备注
     */
    @Override
    public Boolean updateByVo(AppUserRemarkVo vo) {
        AppUserRemark update = BeanUtil.toBean(vo, AppUserRemark.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AppUserRemark entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除APP用户备注
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
