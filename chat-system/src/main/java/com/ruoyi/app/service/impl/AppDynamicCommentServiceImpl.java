package com.ruoyi.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.app.domain.AppDynamicComment;
import com.ruoyi.app.domain.bo.AppDynamicCommentBo;
import com.ruoyi.app.domain.vo.AppDynamicCommentVo;
import com.ruoyi.app.mapper.AppDynamicCommentMapper;
import com.ruoyi.app.mapper.AppDynamicMapper;
import com.ruoyi.app.mapper.AppRecordMapper;
import com.ruoyi.app.service.IAppDynamicCommentService;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * APP用户动态评论Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-12-23
 */
@RequiredArgsConstructor
@Service
public class AppDynamicCommentServiceImpl implements IAppDynamicCommentService {

    private final AppDynamicCommentMapper baseMapper;
    private final AppRecordMapper appRecordMapper;
    private final AppDynamicMapper appDynamicMapper;

    /**
     * 查询APP用户动态评论
     */
    @Override
    public AppDynamicCommentVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询APP用户动态评论列表
     */
    @Override
    public TableDataInfo<AppDynamicCommentVo> queryPageList(AppDynamicCommentBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AppDynamicComment> lqw = buildQueryWrapper(bo);
        Page<AppDynamicCommentVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询APP用户动态评论列表
     */
    @Override
    public List<AppDynamicCommentVo> queryList(AppDynamicCommentBo bo) {
        LambdaQueryWrapper<AppDynamicComment> lqw = buildQueryWrapper(bo);
        lqw.orderByDesc(AppDynamicComment::getCreateTime);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AppDynamicComment> buildQueryWrapper(AppDynamicCommentBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AppDynamicComment> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getDynamicId() != null, AppDynamicComment::getDynamicId, bo.getDynamicId());
        lqw.eq(bo.getUserId() != null, AppDynamicComment::getUserId, bo.getUserId());
        lqw.eq(bo.getToUserId() != null, AppDynamicComment::getToUserId, bo.getToUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getContext()), AppDynamicComment::getContext, bo.getContext());
        lqw.orderByDesc(AppDynamicComment::getCreateTime);
        return lqw;
    }

    /**
     * 新增APP用户动态评论
     */
    @Override
    public Boolean insertByBo(AppDynamicCommentBo bo) {
        AppDynamicComment add = BeanUtil.toBean(bo, AppDynamicComment.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改APP用户动态评论
     */
    @Override
    public Boolean updateByBo(AppDynamicCommentBo bo) {
        AppDynamicComment update = BeanUtil.toBean(bo, AppDynamicComment.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AppDynamicComment entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除APP用户动态评论
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public Boolean delete(Long id) {
        return baseMapper.deleteById(id) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AppDynamicCommentBo addComment(Long id, Long userId, Long toUserId, String content) {
        AppDynamicCommentBo appDynamicCommentBo = new AppDynamicCommentBo();
        appDynamicCommentBo.setDynamicId(id);
        appDynamicCommentBo.setToUserId(toUserId);
        appDynamicCommentBo.setContext(content);
        appDynamicCommentBo.setUserId(userId);
        insertByBo(appDynamicCommentBo);
        return appDynamicCommentBo;
    }
}
