package com.ruoyi.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.app.domain.AppUserImage;
import com.ruoyi.app.domain.bo.AppUserImageBo;
import com.ruoyi.app.domain.vo.AppUserImageVo;
import com.ruoyi.app.mapper.AppUserImageMapper;
import com.ruoyi.app.service.IAppUserImageService;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * APP用户照片Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@RequiredArgsConstructor
@Service
public class AppUserImageServiceImpl implements IAppUserImageService {

    private final AppUserImageMapper baseMapper;

    /**
     * 查询APP用户照片
     */
    @Override
    public AppUserImageVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询APP用户照片列表
     */
    @Override
    public TableDataInfo<AppUserImageVo> queryPageList(AppUserImageBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AppUserImage> lqw = buildQueryWrapper(bo);
        Page<AppUserImageVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询APP用户照片列表
     */
    @Override
    public List<AppUserImageVo> queryList(AppUserImageBo bo) {
        LambdaQueryWrapper<AppUserImage> lqw = buildQueryWrapper(bo);
        lqw.orderByAsc(AppUserImage::getCreateTime);
        return baseMapper.selectVoList(lqw);
    }

    @Override
    public List<AppUserImageVo> queryList(Long uid) {
        AppUserImageBo imageBo = new AppUserImageBo();
        imageBo.setUserId(uid);
        return queryList(imageBo);
    }

    private LambdaQueryWrapper<AppUserImage> buildQueryWrapper(AppUserImageBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AppUserImage> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, AppUserImage::getUserId, bo.getUserId());
        return lqw;
    }

    /**
     * 新增APP用户照片
     */
    @Override
    public Boolean insertByBo(AppUserImageBo bo) {
        AppUserImage add = BeanUtil.toBean(bo, AppUserImage.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改APP用户照片
     */
    @Override
    public Boolean updateByBo(AppUserImageBo bo) {
        AppUserImage update = BeanUtil.toBean(bo, AppUserImage.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AppUserImage entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除APP用户照片
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public Boolean deleteWithValidById(Long id, Long userId) {
        LambdaQueryWrapper<AppUserImage> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AppUserImage::getId, id).eq(AppUserImage::getUserId, userId);
        return baseMapper.delete(wrapper) > 0;
    }
}
