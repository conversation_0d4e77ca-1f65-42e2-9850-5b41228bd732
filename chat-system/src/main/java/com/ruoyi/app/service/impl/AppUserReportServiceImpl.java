package com.ruoyi.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ruoyi.app.domain.bo.AppUserReportBo;
import com.ruoyi.app.domain.vo.AppUserReportVo;
import com.ruoyi.app.domain.AppUserReport;
import com.ruoyi.app.mapper.AppUserReportMapper;
import com.ruoyi.app.service.IAppUserReportService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * APP举报信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-03
 */
@RequiredArgsConstructor
@Service
public class AppUserReportServiceImpl implements IAppUserReportService {

    private final AppUserReportMapper baseMapper;

    /**
     * 查询APP举报信息
     */
    @Override
    public AppUserReportVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询APP举报信息列表
     */
    @Override
    public TableDataInfo<AppUserReportVo> queryPageList(AppUserReportBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AppUserReport> lqw = buildQueryWrapper(bo);
        Page<AppUserReportVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询APP举报信息列表
     */
    @Override
    public List<AppUserReportVo> queryList(AppUserReportBo bo) {
        LambdaQueryWrapper<AppUserReport> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AppUserReport> buildQueryWrapper(AppUserReportBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AppUserReport> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getReportId() != null, AppUserReport::getReportId, bo.getReportId());
        lqw.eq(StringUtils.isNotBlank(bo.getContext()), AppUserReport::getContext, bo.getContext());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), AppUserReport::getType, bo.getType());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), AppUserReport::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增APP举报信息
     */
    @Override
    public Boolean insertByBo(AppUserReportBo bo) {
        AppUserReport add = BeanUtil.toBean(bo, AppUserReport.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改APP举报信息
     */
    @Override
    public Boolean updateByBo(AppUserReportBo bo) {
        AppUserReport update = BeanUtil.toBean(bo, AppUserReport.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AppUserReport entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除APP举报信息
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
