package com.ruoyi.app.service;

import com.ruoyi.app.domain.bo.AppBannerBo;
import com.ruoyi.app.domain.vo.AppBannerVo;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * APP商城的轮播图Service接口
 *
 * <AUTHOR>
 * @date 2025-04-20
 */
public interface IAppBannerService {

    /**
     * 查询APP商城的轮播图
     */
    AppBannerVo queryById(Long id);

    /**
     * 查询APP商城的轮播图列表
     */
    TableDataInfo<AppBannerVo> queryPageList(AppBannerBo bo, PageQuery pageQuery);

    /**
     * 查询APP商城的轮播图列表
     */
    List<AppBannerVo> queryList(AppBannerBo bo);

    /**
     * 新增APP商城的轮播图
     */
    Boolean insertByBo(AppBannerBo bo);

    /**
     * 修改APP商城的轮播图
     */
    Boolean updateByBo(AppBannerBo bo);

    /**
     * 校验并批量删除APP商城的轮播图信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
