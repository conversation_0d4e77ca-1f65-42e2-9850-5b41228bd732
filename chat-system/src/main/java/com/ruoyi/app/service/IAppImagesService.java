package com.ruoyi.app.service;

import com.ruoyi.app.domain.AppImages;
import com.ruoyi.app.domain.vo.AppImagesVo;
import com.ruoyi.app.domain.bo.AppImagesBo;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * APP用户静态资源Service接口
 *
 * <AUTHOR>
 * @date 2023-04-04
 */
public interface IAppImagesService {

    /**
     * 查询APP用户静态资源
     */
    AppImagesVo queryById(Long id);

    /**
     * 查询APP用户静态资源列表
     */
    TableDataInfo<AppImagesVo> queryPageList(AppImagesBo bo, PageQuery pageQuery);

    /**
     * 查询APP用户静态资源列表
     */
    List<AppImagesVo> queryList(AppImagesBo bo);

    /**
     * 新增APP用户静态资源
     */
    Boolean insertByBo(AppImagesBo bo);

    /**
     * 修改APP用户静态资源
     */
    Boolean updateByBo(AppImagesBo bo);

    /**
     * 校验并批量删除APP用户静态资源信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
