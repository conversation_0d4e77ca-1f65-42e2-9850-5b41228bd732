package com.ruoyi.app.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.app.domain.AppUser;
import com.ruoyi.app.domain.bo.AppUserBo;
import com.ruoyi.app.domain.vo.*;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;

import java.math.BigDecimal;
import java.util.*;

/**
 * APP用户信息Service接口
 *
 * <AUTHOR>
 * @date 2025-11-30
 */
public interface IAppUserService {



    AppUserVo selectVoById(Long id);

    /**
     * 根据用户ID查询APP用户信息
     */
    AppUserVo queryById(Long id);


    /**
     * 根据用户ID查询APP用户信息
     */
    List<AppUserVo> queryByPid(Long id);

    /**
     * 查询用户详细信息
     *
     * @param id 用户ID
     * @return
     */
    UserVo queryByIdUserVo(Long id);


    UserResultVo filterateUserVo(Long userId);

    /**
     * 精简用户信息
     */
    UserFilterVo userFilterVo(Long userId);

    /**
     * 查询用户基本信息
     *
     * @param id 用户ID
     * @return
     */
    UserVo userVo(Long id);

    /**
     * 查询用户信息
     *
     * @param phone 电话
     * @return
     */
    AppUserVo queryByPhone(String phone);

    List<AppUserVo> queryByPhone(String phone,Long id);

    /**
     * 用户名查询
     * @param userName 用户名
     * @return
     */
    AppUserVo queryByUserName(String userName);

    /**
     * EMAIL查询
     * @param email email
     * @return
     */
    AppUserVo queryByEmail(String email);

    List<AppUserVo> queryByEmail(String email,Long id);


    /**
     * 邮箱/手机号
     * @param userName 查询信息
     * @return
     */
    AppUserVo queryByEmailOrPhone(String userName);

    /**
     * 查询用户名/邮箱/手机号
     * @param userName 查询信息
     * @return
     */
    AppUserVo queryByUserNameOrPhone(String userName);

    /**
     * 查询用户昵称
     *
     * @param name 昵称
     * @return
     */
    AppUserVo queryNickName(String name);

    /**
     * 根据邀请码查询用户
     *
     * @param code 邀请码
     * @return
     */
    AppUserVo queryByInviteCode(String code);

    /**
     * 查询用户信息列表
     *
     * @param bo 对象信息
     * @return
     */
    List<AppUserVo> selectList(AppUserBo bo);


    /**
     * 查询用户信息列表
     * @return
     */
    List<AppUserVo> selectList(LambdaQueryWrapper<AppUser> lambdaQueryWrapper);

    /**
     * 查询APP用户信息列表
     */
    TableDataInfo<AppUserVo> queryPageList(AppUserBo bo, PageQuery pageQuery);


    /**
     * 查询APP用户信息列表
     */
    TableDataInfo<AppUser> queryPage(AppUserBo bo, PageQuery pageQuery);


    /**
     * 查询APP用户信息列表
     */
    List<AppUserVo> queryList(AppUserBo bo);

    /**
     * 查询APP用户信息列表
     */
    List<AppUser> queryListUser(AppUserBo appUserBor);

    /**
     * 查询APP用户信息列表
     */
    List<AppUserVo> queryListOrder(AppUserBo bo);

    /**
     * 根据条件匹配用户
     *
     * @param settingVo 条件信息
     * @return
     */
    List<AppUserVo> queryList(AppUserSettingVo settingVo, Set<Long> noLikeIds);


    /**
     * 查找APP用户信息
     *
     * @param bo 查询条件
     * @return
     */
    List<AppUserVo> findList(AppUserBo bo);


    /**
     * 模糊查找APP用户信息
     *
     * @param bo 查询条件
     * @return
     */
    List<AppUserVo> findNameList(AppUserBo bo);


    /**
     * 新增APP用户信息
     */
    Boolean insertByBo(AppUserBo bo);

    /**
     * 新增APP用户
     *
     * @param user 用户对象
     * @return
     */
    Boolean insert(AppUserVo user);

    /**
     * 修改APP用户信息
     */
    Boolean updateByBo(AppUserBo bo);
    Boolean updateByVo(AppUserVo vo);

    /**
     * 批量修改APP用户在线状态
     */
    Boolean updates(List<AppUserVo> appUserVos);

    /**
     * 校验并批量删除APP用户信息信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 获取邀请码和邀请信息
     *
     * @param userId 当前用户ID
     * @return
     */
    HashMap<String, Object> inviteCode(Long userId);

    /**
     * 修改用户密码
     *
     * @param userId   用户ID
     * @param password 用户密码
     * @return
     */
    Boolean updatePassword(Long userId, String password);


    /**
     * 修改用户的环信UUID
     *
     * @param userId 用户ID
     * @param uuid   环信的UUID
     * @return
     */
    Boolean updateUuid(Long userId, String uuid);

    /**
     * 修改用户的状态
     *
     * @param userId 用户ID
     * @return
     */
    Boolean updateStatus(Long userId, String status);

    /**
     * 修改用户信息
     *
     * @param bo 用户信息对象
     * @return
     */
    Boolean updateInfo(AppUserBo bo);

    UserVo setUserInfo(Long id, Long userId);

    List<UserVo> randNumberUser(Long userId, Integer number);

    /**
     * 修改用户的在线状态
     *
     * @param id 用户ID
     * @return
     */
    Boolean updateOnline(Long id);

    /**
     * 批量更新用户的信息
     *
     * @param updates 用户的信息
     * @return
     */
    Boolean update(ArrayList<AppUser> updates);

    /**
     * 翻译文字
     * @param userId
     * @param number
     * @return
     */
    Boolean translate(Long userId, Integer number);


    R<?> subtractionEmoji(Long userId, BigDecimal emojiPrice);

    /**
     * 清除用户缓存今日使用字符
     */
    void resetDailyFreeQuota();

    /**
     * 获取语言列表
     * @return
     */
    List<AppLanguageVo> languages();
}
