package com.ruoyi.app.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.app.domain.AppDynamic;
import com.ruoyi.app.domain.bo.AppDynamicBo;
import com.ruoyi.app.domain.vo.AppDynamicVo;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * APP用户动态Service接口
 *
 * <AUTHOR>
 * @date 2025-12-22
 */
public interface IAppDynamicService {

    /**
     * 查询APP用户动态
     */
    AppDynamicVo queryById(Long id);

    AppDynamicVo selectLimitOne(LambdaQueryWrapper<AppDynamic> queryWrapper);

    /**
     * 查询APP用户动态列表
     */
    TableDataInfo<AppDynamicVo> queryPageList(AppDynamicBo bo, PageQuery pageQuery);

    /**
     * 查询APP用户动态列表
     */
    TableDataInfo<AppDynamicVo> queryPageList(Long id, String type, PageQuery pageQuery);
    /**
     * 查询APP用户动态列表
     */
    List<AppDynamicVo> queryList(AppDynamicBo bo);

    /**
     * 新增APP用户动态
     */
    Boolean insertByBo(AppDynamicBo bo);


    Boolean addDynamic(AppDynamicBo bo);

    /**
     * 修改APP用户动态
     */
    Boolean updateByBo(AppDynamicBo bo);

    /**
     * 校验并批量删除APP用户动态信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据ID删除动态
     * 软删除，所以不用情况点赞，评论，图片视频资源等表
     *
     * @param id 动态ID
     * @return
     */
    Boolean delete(Long id);

    /**
     * 处理查询后的数据
     *
     * @param page 数据
     * @return
     */
    TableDataInfo<AppDynamicVo> queryDate(TableDataInfo<AppDynamicVo> page, Long id);

    /**
     * 处理查询后的数据
     *
     * @param list 数据
     * @return
     */
    List<AppDynamicVo> queryDate(List<AppDynamicVo> list, Long id);


    /**
     * 精简查询动态数据
     *
     * @param list 数据
     * @return
     */
    List<AppDynamicVo> simplifyDate(List<AppDynamicVo> list, Long id);

    /**
     * 根据ID查询动态的详情
     *
     * @param id     动态ID
     * @param userId 用户ID
     * @return
     */
    AppDynamicVo findOne(Long id, Long userId);


    AppDynamicVo info(AppDynamicVo e, Long id);


    /**
     * 根据ID查询动态的详情(分享页面)
     *
     * @param id 动态ID
     * @return
     */
    AppDynamicVo findOne(Long id);
}
