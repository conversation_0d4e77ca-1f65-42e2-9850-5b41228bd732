package com.ruoyi.app.service;

import com.ruoyi.app.domain.AppFriend;
import com.ruoyi.app.domain.bo.AppFriendBo;
import com.ruoyi.app.domain.vo.AppFriendVo;
import com.ruoyi.app.domain.vo.UserVo;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * APP用户关注Service接口
 *
 * <AUTHOR>
 * @date 2023-01-04
 */
public interface IAppFriendService {

    /**
     * 查询APP用户关注
     */
    AppFriendVo queryById(Long id);

    /**
     * 查询APP用户关注列表
     */
    TableDataInfo<AppFriendVo> queryPageList(AppFriendBo bo, PageQuery pageQuery);

    /**
     * 查询用户的关注信息
     *
     * @param userId   用户ID
     * @param toUserId 关注的用户ID
     * @return
     */
    AppFriendVo queryUser(Long userId, Long toUserId);

    /**
     * 查询APP用户关注列表
     */
    List<AppFriendVo> queryList(AppFriendBo bo);

    /**
     * 查询APP用户关注列表
     */
    List<AppFriend> list(AppFriendBo bo);


    AppFriendVo findByUser(Long userId, Long friendId);


    String userRelation(Long userId, Long friendId);

    /**
     * 新增APP用户关注
     */
    Boolean insertByBo(AppFriendBo bo);

    /**
     * 新增APP用户关注
     */
    Boolean insert(AppFriend appFriend);

    /**
     * APP用户关注
     */
    void addFriend(AppFriendBo appFriend, String status);

    /**
     * 修改APP用户关注
     */
    Boolean updateByBo(AppFriendBo bo);

    Boolean updateByVo(AppFriendVo bo);
    /**
     * 校验并批量删除APP用户关注信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);


    /**
     * 删除记录，取消关注
     */
    Boolean delete(Long id);

    /**
     * 添加好友申请
     *
     * @param userId
     * @param id
     * @param msg
     * @return
     */
    Boolean addFridend(Long userId, Long id, String msg);

    /**
     * 通过好友申请
     *
     * @param userId
     * @param id
     * @param remark
     * @return
     */
    Boolean passFridend(Long userId, Long id, String remark);

    /**
     * 获取用户的信息
     *
     * @param userId 用户ID
     * @return
     */
    UserVo info(Long userId);

    /**
     * 取消关注，如果双方是好友，删除好友关系
     *
     * @param appFriendVo
     * @return
     */
    Boolean canCelLike(AppFriendVo appFriendVo);

    /**
     * 取消关注，如果双方是好友，删除好友关系
     *
     * @param friendId 朋友ID
     * @return
     */
    Boolean canCelLike(Long userId, Long friendId);


    void createFriend(Long userId, Long friend);
}
