package com.ruoyi.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ruoyi.app.domain.bo.AppOccupationBo;
import com.ruoyi.app.domain.vo.AppOccupationVo;
import com.ruoyi.app.domain.AppOccupation;
import com.ruoyi.app.mapper.AppOccupationMapper;
import com.ruoyi.app.service.IAppOccupationService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * APP职业信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-12-12
 */
@RequiredArgsConstructor
@Service
public class AppOccupationServiceImpl implements IAppOccupationService {

    private final AppOccupationMapper baseMapper;

    /**
     * 查询APP职业信息
     */
    @Override
    public AppOccupationVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询APP职业信息列表
     */
    @Override
    public TableDataInfo<AppOccupationVo> queryPageList(AppOccupationBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AppOccupation> lqw = buildQueryWrapper(bo);
        Page<AppOccupationVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询APP职业信息列表
     */
    @Override
    public List<AppOccupationVo> queryList(AppOccupationBo bo) {
        LambdaQueryWrapper<AppOccupation> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AppOccupation> buildQueryWrapper(AppOccupationBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AppOccupation> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getName()), AppOccupation::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), AppOccupation::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增APP职业信息
     */
    @Override
    public Boolean insertByBo(AppOccupationBo bo) {
        AppOccupation add = BeanUtil.toBean(bo, AppOccupation.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改APP职业信息
     */
    @Override
    public Boolean updateByBo(AppOccupationBo bo) {
        AppOccupation update = BeanUtil.toBean(bo, AppOccupation.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AppOccupation entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除APP职业信息
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
