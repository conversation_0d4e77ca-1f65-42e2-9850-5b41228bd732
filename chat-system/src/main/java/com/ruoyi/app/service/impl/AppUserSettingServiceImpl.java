package com.ruoyi.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.app.domain.AppUser;
import com.ruoyi.app.domain.vo.AppUserVo;
import com.ruoyi.app.mapper.AppUserMapper;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ruoyi.app.domain.bo.AppUserSettingBo;
import com.ruoyi.app.domain.vo.AppUserSettingVo;
import com.ruoyi.app.domain.AppUserSetting;
import com.ruoyi.app.mapper.AppUserSettingMapper;
import com.ruoyi.app.service.IAppUserSettingService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * APP用户信息设置Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-02-02
 */
@RequiredArgsConstructor
@Service
public class AppUserSettingServiceImpl implements IAppUserSettingService {

    private final AppUserSettingMapper baseMapper;
    private final AppUserMapper appUserMapper;
    /**
     * 查询APP用户信息设置
     */
    @Override
    public AppUserSettingVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    @Override
    public AppUserSettingVo queryByUserId(Long uid) {
        LambdaQueryWrapper<AppUserSetting> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AppUserSetting::getUserId, uid);
        return baseMapper.selectVoOne(wrapper);
    }

    /**
     * 查询APP用户信息设置列表
     */
    @Override
    public TableDataInfo<AppUserSettingVo> queryPageList(AppUserSettingBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AppUserSetting> lqw = buildQueryWrapper(bo);
        Page<AppUserSettingVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询APP用户信息设置列表
     */
    @Override
    public List<AppUserSettingVo> queryList(AppUserSettingBo bo) {
        LambdaQueryWrapper<AppUserSetting> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    @Override
    public List<AppUserVo> queryList(Map<String, Double> doubleMap) {
        LambdaQueryWrapper<AppUser> lqw = Wrappers.lambdaQuery();
        lqw.le(AppUser::getLatitude, doubleMap.get("maxLatitude"));
        lqw.ge(AppUser::getLatitude, doubleMap.get("minLatitude"));
        lqw.le(AppUser::getLongitude, doubleMap.get("maxLongitude"));
        lqw.ge(AppUser::getLongitude, doubleMap.get("minLongitude"));
        lqw.eq(AppUser::getStatus, Constants.SUCCESS);
        return appUserMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AppUserSetting> buildQueryWrapper(AppUserSettingBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AppUserSetting> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, AppUserSetting::getUserId, bo.getUserId());
        lqw.eq(bo.getDistance() != null, AppUserSetting::getDistance, bo.getDistance());
        return lqw;
    }

    /**
     * 新增APP用户信息设置
     */
    @Override
    public Boolean insertByBo(AppUserSettingBo bo) {
        AppUserSetting add = BeanUtil.toBean(bo, AppUserSetting.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public Boolean insert(Long id) {
        AppUserSettingBo appUserSettingBo = new AppUserSettingBo();
        appUserSettingBo.setCity(Constants.FAIL);
        appUserSettingBo.setUserId(id);
        appUserSettingBo.setAgeMax(UserConstants.AGE_MAX);
        appUserSettingBo.setAgeMin(UserConstants.AGE_MIN);
        appUserSettingBo.setDistance(100);
        appUserSettingBo.setSex("2");
        return insertByBo(appUserSettingBo);
    }

    /**
     * 修改APP用户信息设置
     */
    @Override
    public Boolean updateByBo(AppUserSettingBo bo) {
        AppUserSetting update = BeanUtil.toBean(bo, AppUserSetting.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 修改APP用户信息设置
     */
    @Override
    public Boolean updateByVo(AppUserSettingVo vo) {
        AppUserSetting update = BeanUtil.toBean(vo, AppUserSetting.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AppUserSetting entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除APP用户信息设置
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
