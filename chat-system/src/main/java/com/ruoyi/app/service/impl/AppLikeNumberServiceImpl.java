package com.ruoyi.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.app.domain.AppLikeNumber;
import com.ruoyi.app.domain.bo.AppLikeNumberBo;
import com.ruoyi.app.domain.vo.AppLikeNumberVo;
import com.ruoyi.app.mapper.AppLikeNumberMapper;
import com.ruoyi.app.service.IAppLikeNumberService;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * APP通知数量控制Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-03-04
 */
@RequiredArgsConstructor
@Service
public class AppLikeNumberServiceImpl implements IAppLikeNumberService {

    private final AppLikeNumberMapper baseMapper;

    /**
     * 查询APP通知数量控制
     */
    @Override
    public AppLikeNumberVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询APP通知数量控制列表
     */
    @Override
    public TableDataInfo<AppLikeNumberVo> queryPageList(AppLikeNumberBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AppLikeNumber> lqw = buildQueryWrapper(bo);
        Page<AppLikeNumberVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询APP通知数量控制列表
     */
    @Override
    public List<AppLikeNumberVo> queryList(AppLikeNumberBo bo) {
        LambdaQueryWrapper<AppLikeNumber> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AppLikeNumber> buildQueryWrapper(AppLikeNumberBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AppLikeNumber> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, AppLikeNumber::getUserId, bo.getUserId());
        lqw.eq(bo.getRecordId() != null, AppLikeNumber::getRecordId, bo.getRecordId());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), AppLikeNumber::getType, bo.getType());
        return lqw;
    }

    /**
     * 新增APP通知数量控制
     */
    @Override
    public Boolean insertByBo(AppLikeNumberBo bo) {
        AppLikeNumber add = BeanUtil.toBean(bo, AppLikeNumber.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改APP通知数量控制
     */
    @Override
    public Boolean updateByBo(AppLikeNumberBo bo) {
        AppLikeNumber update = BeanUtil.toBean(bo, AppLikeNumber.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    @Override
    public Boolean update(Long userId, String type) {
        AppLikeNumberBo appLikeNumber = new AppLikeNumberBo();
        appLikeNumber.setUserId(userId);
        appLikeNumber.setType(type);
        List<AppLikeNumberVo> appLikeNumberVos = queryList(appLikeNumber);
        Collection<Long> collect = appLikeNumberVos.stream().map(AppLikeNumberVo::getId).collect(Collectors.toList());
        if (collect.size() > 0) {
            return baseMapper.deleteBatchIds(collect) > 0;
        } else {
            return false;
        }
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AppLikeNumber entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除APP通知数量控制
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
