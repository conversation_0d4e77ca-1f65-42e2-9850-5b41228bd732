package com.ruoyi.app.service;

import com.ruoyi.app.domain.AppVersion;
import com.ruoyi.app.domain.vo.AppVersionVo;
import com.ruoyi.app.domain.bo.AppVersionBo;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * APP版本更新记录Service接口
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
public interface IAppVersionService {

    /**
     * 查询APP版本更新记录
     */
    AppVersionVo queryById(Long id);

    /**
     * 查询APP版本更新记录列表
     */
    TableDataInfo<AppVersionVo> queryPageList(AppVersionBo bo, PageQuery pageQuery);

    /**
     * 查询APP版本更新记录列表
     */
    List<AppVersionVo> queryList(AppVersionBo bo);


    /**
     * 查询APP版本更新记录列表
     */
    AppVersionVo lastOne(AppVersionBo bo);

    /**
     * 新增APP版本更新记录
     */
    Boolean insertByBo(AppVersionBo bo);

    /**
     * 修改APP版本更新记录
     */
    Boolean updateByBo(AppVersionBo bo);

    /**
     * 校验并批量删除APP版本更新记录信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
