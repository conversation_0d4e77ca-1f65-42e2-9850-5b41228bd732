package com.ruoyi.app.service;

import com.ruoyi.app.domain.AppGroup;
import com.ruoyi.app.domain.vo.AppGroupUserVo;
import com.ruoyi.app.domain.vo.AppGroupVo;
import com.ruoyi.app.domain.bo.AppGroupBo;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * APP群组Service接口
 *
 * <AUTHOR>
 * @date 2023-01-29
 */
public interface IAppGroupService {

    /**
     * 查询APP群组
     */
    AppGroupVo queryById(Long id);


    /**
     * 根据环信群组ID查询群组信息
     */
    AppGroupVo queryByGroupId(String groupId);

    /**
     * 查询APP群组列表
     */
    TableDataInfo<AppGroupVo> queryPageList(AppGroupBo bo, PageQuery pageQuery);

    /**
     * 查询APP群组列表
     */
    List<AppGroupVo> queryList(AppGroupBo bo);

    /**
     * 查询APP群组列表
     */
    List<AppGroupVo> queryNameList(AppGroupBo bo);


    /**
     * 新增APP群组
     */
    Boolean insertByBo(AppGroupBo bo);


    /**
     * 创建APP群组
     */
    Boolean createGroup(AppGroupBo bo, Long userId, List<String> ids);

    /**
     * 修改APP群组信息
     */
    Boolean updateByBo(AppGroupBo bo);

    /**
     * 修改APP群组信息
     */
    Boolean updateByVo(AppGroupVo vo);

    /**
     * 校验并批量删除APP群组信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 邀请多个用户加入群组
     *
     * @param userIds 用户IDS
     * @param groupId 群组ID
     */
    void joinGroup(List<String> userIds, Long groupId);

    /**
     * 单个用户加入群组
     *
     * @param userId  用户ID
     * @param groupId 群组ID
     */
    Boolean joinGroup(Long userId, Long groupId);

    /**
     * 删除群组
     *
     * @param id 群组ID
     * @return
     */
    int delete(Long id);
}
