package com.ruoyi.app.service;

import com.ruoyi.app.domain.AppDetail;
import com.ruoyi.app.domain.vo.AppDetailVo;
import com.ruoyi.app.domain.bo.AppDetailBo;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.enums.CoinType;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

/**
 * APP用户账单明细Service接口
 *
 * <AUTHOR>
 * @date 2023-08-26
 */
public interface IAppDetailService {

    /**
     * 查询APP用户账单明细
     */
    AppDetailVo queryById(Long id);

    /**
     * 查询APP用户账单明细列表
     */
    TableDataInfo<AppDetailVo> queryPageList(AppDetailBo bo, PageQuery pageQuery);

    /**
     * 查询APP用户账单明细列表
     */
    List<AppDetailVo> queryList(AppDetailBo bo);

    /**
     * 新增APP用户账单明细
     */
    Boolean insertByBo(AppDetailBo bo);

    /**
     * 新增APP用户账单明细
     */
    Boolean insert(Long userId, BigDecimal balance, BigDecimal freeBalance, String flow, String coinType, String type, String remark, Long fromId);

    /**
     * 修改APP用户账单明细
     */
    Boolean updateByBo(AppDetailBo bo);

    /**
     * 校验并批量删除APP用户账单明细信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
