package com.ruoyi.app.service;

import com.ruoyi.app.domain.bo.AppLabelLikeBo;
import com.ruoyi.app.domain.vo.AppLabelLikeVo;
import com.ruoyi.app.domain.vo.AppLabelVo;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * APP用户标签喜欢Service接口
 *
 * <AUTHOR>
 * @date 2023-01-13
 */
public interface IAppLabelLikeService {

    /**
     * 查询APP用户标签喜欢
     */
    AppLabelLikeVo queryById(Long id);

    /**
     * 查询APP用户标签喜欢列表
     */
    TableDataInfo<AppLabelLikeVo> queryPageList(AppLabelLikeBo bo, PageQuery pageQuery);

    /**
     * 查询APP用户标签喜欢列表
     */
    List<AppLabelLikeVo> queryList(AppLabelLikeBo bo);

    /**
     * 查询APP用户标签喜欢列表
     */
    List<AppLabelLikeVo> list(AppLabelLikeBo bo);


    /**
     * 查询APP用户是否喜欢了标签
     */
    AppLabelLikeVo queryLikeLabel(Long userId, Long labelId);

    /**
     * 新增APP用户标签喜欢
     */
    Boolean insertByBo(AppLabelLikeBo bo);

    /**
     * 修改APP用户标签喜欢
     */
    Boolean updateByBo(AppLabelLikeBo bo);

    /**
     * 校验并批量删除APP用户标签喜欢信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据用户ID删除选择的标签
     */
    Boolean deletes(Long userId);

    /**
     * 设置标签为超级喜欢
     *
     * @param labelLikeBo 对象信息
     * @return
     */
    Boolean setLabelLike(AppLabelLikeBo labelLikeBo);

    /**
     * 取消标签的超级喜欢
     *
     * @param id     标签ID
     * @param userId 用户ID
     * @return
     */
    Boolean cancelLabelLike(Long id, Long userId);

    /**
     * 获取标签的详情信息
     *
     * @param labelVo 标签信息
     * @return
     */
    AppLabelVo info(AppLabelVo labelVo, Long uid);

    int delete(Long id);
}
