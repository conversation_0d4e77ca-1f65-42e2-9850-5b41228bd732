package com.ruoyi.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ruoyi.app.domain.bo.AppFriendRelationBo;
import com.ruoyi.app.domain.vo.AppFriendRelationVo;
import com.ruoyi.app.domain.AppFriendRelation;
import com.ruoyi.app.mapper.AppFriendRelationMapper;
import com.ruoyi.app.service.IAppFriendRelationService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * APP用户关系Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@RequiredArgsConstructor
@Service
public class AppFriendRelationServiceImpl implements IAppFriendRelationService {

    private final AppFriendRelationMapper baseMapper;

    /**
     * 查询APP用户关系
     */
    @Override
    public AppFriendRelationVo queryById(Long relationId){
        return baseMapper.selectVoById(relationId);
    }

    @Override
    public List<AppFriendRelationVo> queryList(Long id, Long userId, String type) {
        LambdaQueryWrapper<AppFriendRelation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AppFriendRelation::getUserId, id);
        wrapper.eq(AppFriendRelation::getToUserId, userId);
        wrapper.eq(AppFriendRelation::getType, type);
        return baseMapper.selectVoList(wrapper);
    }

    @Override
    public AppFriendRelationVo queryByOne(LambdaQueryWrapper<AppFriendRelation> wrapper) {
        return baseMapper.selectVoOne(wrapper);
    }

    /**
     * 查询APP用户关系列表
     */
    @Override
    public TableDataInfo<AppFriendRelationVo> queryPageList(AppFriendRelationBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AppFriendRelation> lqw = buildQueryWrapper(bo);
        Page<AppFriendRelationVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询APP用户关系列表
     */
    @Override
    public List<AppFriendRelationVo> queryList(AppFriendRelationBo bo) {
        LambdaQueryWrapper<AppFriendRelation> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AppFriendRelation> buildQueryWrapper(AppFriendRelationBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AppFriendRelation> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, AppFriendRelation::getUserId, bo.getUserId());
        lqw.eq(bo.getToUserId() != null, AppFriendRelation::getToUserId, bo.getToUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), AppFriendRelation::getType, bo.getType());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), AppFriendRelation::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增APP用户关系
     */
    @Override
    public Boolean insertByBo(AppFriendRelationBo bo) {
        AppFriendRelation add = BeanUtil.toBean(bo, AppFriendRelation.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setRelationId(add.getRelationId());
        }
        return flag;
    }

    /**
     * 修改APP用户关系
     */
    @Override
    public Boolean updateByBo(AppFriendRelationBo bo) {
        AppFriendRelation update = BeanUtil.toBean(bo, AppFriendRelation.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    @Override
    public Boolean updateByVo(AppFriendRelationVo vo) {
        AppFriendRelation update = BeanUtil.toBean(vo,AppFriendRelation.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AppFriendRelation entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除APP用户关系
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }


    @Override
    public Boolean deleteWithValidById(Long id) {
        return baseMapper.deleteById(id) > 0;
    }
}
