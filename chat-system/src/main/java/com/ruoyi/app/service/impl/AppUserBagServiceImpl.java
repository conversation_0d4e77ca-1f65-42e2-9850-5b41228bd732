package com.ruoyi.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ruoyi.app.domain.bo.AppUserBagBo;
import com.ruoyi.app.domain.vo.AppUserBagVo;
import com.ruoyi.app.domain.AppUserBag;
import com.ruoyi.app.mapper.AppUserBagMapper;
import com.ruoyi.app.service.IAppUserBagService;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * App用户签到领取奖励Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-03-29
 */
@RequiredArgsConstructor
@Service
public class AppUserBagServiceImpl implements IAppUserBagService {

    private final AppUserBagMapper baseMapper;

    /**
     * 查询App用户签到领取奖励
     */
    @Override
    public AppUserBagVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }


    /**
     * 查询App用户签到领取奖励
     */
    @Override
    public AppUserBagVo queryByUserId(Long userId,String type) {
        LambdaQueryWrapper<AppUserBag> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AppUserBag::getUserId, userId).eq(AppUserBag::getType,type);
        return baseMapper.selectVoOne(wrapper);
    }


    /**
     * 查询App用户签到领取奖励列表
     */
    @Override
    public TableDataInfo<AppUserBagVo> queryPageList(AppUserBagBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AppUserBag> lqw = buildQueryWrapper(bo);
        Page<AppUserBagVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询App用户签到领取奖励列表
     */
    @Override
    public List<AppUserBagVo> queryList(AppUserBagBo bo) {
        LambdaQueryWrapper<AppUserBag> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AppUserBag> buildQueryWrapper(AppUserBagBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AppUserBag> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, AppUserBag::getUserId, bo.getUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), AppUserBag::getType, bo.getType());
        lqw.eq(bo.getNumber() != null, AppUserBag::getNumber, bo.getNumber());
        return lqw;
    }

    /**
     * 新增App用户签到领取奖励
     */
    @Override
    public Boolean insertByBo(AppUserBagBo bo) {
        AppUserBag add = BeanUtil.toBean(bo, AppUserBag.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改App用户签到领取奖励
     */
    @Override
    public Boolean updateByBo(AppUserBagBo bo) {
        AppUserBag update = BeanUtil.toBean(bo, AppUserBag.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }


    /**
     * 修改App用户签到领取奖励
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByVo(AppUserBagVo vo) {
        AppUserBag update = BeanUtil.toBean(vo, AppUserBag.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AppUserBag entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除App用户签到领取奖励
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
