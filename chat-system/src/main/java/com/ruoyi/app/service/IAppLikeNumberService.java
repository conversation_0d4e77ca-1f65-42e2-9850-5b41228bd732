package com.ruoyi.app.service;

import com.ruoyi.app.domain.bo.AppLikeNumberBo;
import com.ruoyi.app.domain.vo.AppLikeNumberVo;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * APP通知数量控制Service接口
 *
 * <AUTHOR>
 * @date 2023-03-04
 */
public interface IAppLikeNumberService {

    /**
     * 查询APP通知数量控制
     */
    AppLikeNumberVo queryById(Long id);

    /**
     * 查询APP通知数量控制列表
     */
    TableDataInfo<AppLikeNumberVo> queryPageList(AppLikeNumberBo bo, PageQuery pageQuery);

    /**
     * 查询APP通知数量控制列表
     */
    List<AppLikeNumberVo> queryList(AppLikeNumberBo bo);

    /**
     * 新增APP通知数量控制
     */
    Boolean insertByBo(AppLikeNumberBo bo);

    /**
     * 修改APP通知数量控制
     */
    Boolean updateByBo(AppLikeNumberBo bo);

    /**
     * 修改APP通知数量控制
     */
    Boolean update(Long userId,String type);

    /**
     * 校验并批量删除APP通知数量控制信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
