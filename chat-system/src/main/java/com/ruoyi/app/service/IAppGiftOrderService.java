package com.ruoyi.app.service;

import com.ruoyi.app.domain.AppGiftOrder;
import com.ruoyi.app.domain.vo.AppGiftOrderVo;
import com.ruoyi.app.domain.bo.AppGiftOrderBo;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * APP礼物记录Service接口
 *
 * <AUTHOR>
 * @date 2022-12-21
 */
public interface IAppGiftOrderService {

    /**
     * 查询APP礼物记录
     */
    AppGiftOrderVo queryById(Long id);

    /**
     * 查询APP礼物记录列表
     */
    TableDataInfo<AppGiftOrderVo> queryPageList(AppGiftOrderBo bo, PageQuery pageQuery);

    /**
     * 查询APP礼物记录列表
     */
    List<AppGiftOrderVo> queryList(AppGiftOrderBo bo);

    /**
     * 新增APP礼物记录
     */
    Boolean insertByBo(AppGiftOrderBo bo);

    /**
     * 修改APP礼物记录
     */
    Boolean updateByBo(AppGiftOrderBo bo);

    /**
     * 校验并批量删除APP礼物记录信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
