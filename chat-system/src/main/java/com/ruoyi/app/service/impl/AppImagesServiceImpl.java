package com.ruoyi.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ruoyi.app.domain.bo.AppImagesBo;
import com.ruoyi.app.domain.vo.AppImagesVo;
import com.ruoyi.app.domain.AppImages;
import com.ruoyi.app.mapper.AppImagesMapper;
import com.ruoyi.app.service.IAppImagesService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * APP用户静态资源Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-04-04
 */
@RequiredArgsConstructor
@Service
public class AppImagesServiceImpl implements IAppImagesService {

    private final AppImagesMapper baseMapper;

    /**
     * 查询APP用户静态资源
     */
    @Override
    public AppImagesVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询APP用户静态资源列表
     */
    @Override
    public TableDataInfo<AppImagesVo> queryPageList(AppImagesBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AppImages> lqw = buildQueryWrapper(bo);
        Page<AppImagesVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询APP用户静态资源列表
     */
    @Override
    public List<AppImagesVo> queryList(AppImagesBo bo) {
        LambdaQueryWrapper<AppImages> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AppImages> buildQueryWrapper(AppImagesBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AppImages> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getUrl()), AppImages::getUrl, bo.getUrl());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), AppImages::getType, bo.getType());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), AppImages::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增APP用户静态资源
     */
    @Override
    public Boolean insertByBo(AppImagesBo bo) {
        AppImages add = BeanUtil.toBean(bo, AppImages.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改APP用户静态资源
     */
    @Override
    public Boolean updateByBo(AppImagesBo bo) {
        AppImages update = BeanUtil.toBean(bo, AppImages.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AppImages entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除APP用户静态资源
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
