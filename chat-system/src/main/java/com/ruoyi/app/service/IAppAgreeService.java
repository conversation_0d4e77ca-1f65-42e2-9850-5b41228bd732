package com.ruoyi.app.service;

import com.ruoyi.app.domain.AppAgree;
import com.ruoyi.app.domain.vo.AppAgreeVo;
import com.ruoyi.app.domain.bo.AppAgreeBo;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * APP时光约会Service接口
 *
 * <AUTHOR>
 * @date 2024-03-25
 */
public interface IAppAgreeService {

    /**
     * 查询APP时光约会
     */
    AppAgreeVo queryById(Long id);

    /**
     * 查询APP时光约会列表
     */
    TableDataInfo<AppAgreeVo> queryPageList(AppAgreeBo bo, PageQuery pageQuery);

    /**
     * 查询APP时光约会列表
     */
    List<AppAgreeVo> queryList(AppAgreeBo bo);

    /**
     * 新增APP时光约会
     */
    Boolean insertByBo(AppAgreeBo bo);

    /**
     * 修改APP时光约会
     */
    Boolean updateByBo(AppAgreeBo bo);

    Boolean updateByVo(AppAgreeVo vo);

    /**
     * 校验并批量删除APP时光约会信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
