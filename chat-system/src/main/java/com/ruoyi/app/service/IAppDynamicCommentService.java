package com.ruoyi.app.service;

import com.ruoyi.app.domain.bo.AppDynamicCommentBo;
import com.ruoyi.app.domain.vo.AppDynamicCommentVo;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * APP用户动态评论Service接口
 *
 * <AUTHOR>
 * @date 2022-12-23
 */
public interface IAppDynamicCommentService {

    /**
     * 查询APP用户动态评论
     */
    AppDynamicCommentVo queryById(Long id);

    /**
     * 查询APP用户动态评论列表
     */
    TableDataInfo<AppDynamicCommentVo> queryPageList(AppDynamicCommentBo bo, PageQuery pageQuery);

    /**
     * 查询APP用户动态评论列表
     */
    List<AppDynamicCommentVo> queryList(AppDynamicCommentBo bo);

    /**
     * 新增APP用户动态评论
     */
    Boolean insertByBo(AppDynamicCommentBo bo);

    /**
     * 修改APP用户动态评论
     */
    Boolean updateByBo(AppDynamicCommentBo bo);

    /**
     * 校验并批量删除APP用户动态评论信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 删除用户评论
     * @param id 评论的ID
     * @return
     */
    Boolean delete(Long id);

    AppDynamicCommentBo addComment(Long id, Long userId,Long toUserId,String content);
}
