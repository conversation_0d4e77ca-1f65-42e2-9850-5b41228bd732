package com.ruoyi.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.app.mapper.AppRecordMapper;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ruoyi.app.domain.bo.AppRecordBo;
import com.ruoyi.app.domain.vo.AppRecordVo;
import com.ruoyi.app.domain.AppRecord;
import com.ruoyi.app.service.IAppRecordService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * APP用户通知记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@RequiredArgsConstructor
@Service
public class AppRecordServiceImpl implements IAppRecordService {

    private final AppRecordMapper baseMapper;

    /**
     * 查询APP用户通知记录
     */
    @Override
    public AppRecordVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询APP用户通知记录列表
     */
    @Override
    public TableDataInfo<AppRecordVo> queryPageList(AppRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AppRecord> lqw = buildQueryWrapper(bo);
        Page<AppRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询APP用户通知记录列表
     */
    @Override
    public List<AppRecordVo> queryList(AppRecordBo bo) {
        LambdaQueryWrapper<AppRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AppRecord> buildQueryWrapper(AppRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AppRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getRecordId() != null, AppRecord::getRecordId, bo.getRecordId());
        lqw.eq(bo.getUserId() != null, AppRecord::getUserId, bo.getUserId());
        lqw.eq(bo.getRecordUserId() != null, AppRecord::getRecordUserId, bo.getRecordUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), AppRecord::getType, bo.getType());
        lqw.eq(StringUtils.isNotBlank(bo.getReadStatus()), AppRecord::getReadStatus, bo.getReadStatus());
        return lqw;
    }

    /**
     * 新增APP用户通知记录
     */
    @Override
    public Boolean insertByBo(AppRecordBo bo) {
        AppRecord add = BeanUtil.toBean(bo, AppRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改APP用户通知记录
     */
    @Override
    public Boolean updateByBo(AppRecordBo bo) {
        AppRecord update = BeanUtil.toBean(bo, AppRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 修改APP用户通知记录
     */
    @Override
    public Boolean updateByBo(Long id, String status) {
        LambdaUpdateWrapper<AppRecord> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(AppRecord::getId, id).set(AppRecord::getReadStatus, status);
        return baseMapper.update(null, wrapper) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AppRecord entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除APP用户通知记录
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public Boolean delete(Long id, String type) {
        LambdaQueryWrapper<AppRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AppRecord::getRecordId, id);
        return baseMapper.delete(wrapper) > 0;
    }
}
