package com.ruoyi.app.service;

import com.ruoyi.app.domain.AppLevel;
import com.ruoyi.app.domain.vo.AppLevelVo;
import com.ruoyi.app.domain.bo.AppLevelBo;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * AP用户等级Service接口
 *
 * <AUTHOR>
 * @date 2024-09-26
 */
public interface IAppLevelService {

    /**
     * 查询AP用户等级
     */
    AppLevelVo queryById(Long id);


    /**
     * 根据积分查询对应等级
     */
    AppLevelVo queryLevel(Integer number);


    /**
     * 查询AP用户等级列表
     */
    TableDataInfo<AppLevelVo> queryPageList(AppLevelBo bo, PageQuery pageQuery);

    /**
     * 查询AP用户等级列表
     */
    List<AppLevelVo> queryList(AppLevelBo bo);

    /**
     * 新增AP用户等级
     */
    Boolean insertByBo(AppLevelBo bo);

    /**
     * 修改AP用户等级
     */
    Boolean updateByBo(AppLevelBo bo);

    /**
     * 校验并批量删除AP用户等级信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
