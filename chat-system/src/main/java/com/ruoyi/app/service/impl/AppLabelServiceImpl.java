package com.ruoyi.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.app.domain.AppLabel;
import com.ruoyi.app.domain.AppLabelLike;
import com.ruoyi.app.domain.bo.AppLabelBo;
import com.ruoyi.app.domain.bo.AppLabelLikeBo;
import com.ruoyi.app.domain.vo.AppLabelLikeVo;
import com.ruoyi.app.domain.vo.AppLabelVo;
import com.ruoyi.app.mapper.AppLabelLikeMapper;
import com.ruoyi.app.mapper.AppLabelMapper;
import com.ruoyi.app.service.IAppLabelLikeService;
import com.ruoyi.app.service.IAppLabelService;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.LikeStatus;
import com.ruoyi.common.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * APP标签Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
@RequiredArgsConstructor
@Service
public class AppLabelServiceImpl implements IAppLabelService {

    private final AppLabelMapper baseMapper;
    private final AppLabelLikeMapper appLabelLikeMapper;
    private final IAppLabelLikeService appLabelLikeService;


    /**
     * 查询APP标签
     */
    @Override
    public AppLabelVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 根据名字查询标签
     */
    @Override
    public AppLabelVo queryByName(String name) {
        return baseMapper.selectVoOne(new LambdaQueryWrapper<AppLabel>().eq(AppLabel::getName, name));
    }

    /**
     * 查询APP标签列表
     */
    @Override
    public TableDataInfo<AppLabelVo> queryPageList(AppLabelBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AppLabel> lqw = buildQueryWrapper(bo);
        Page<AppLabelVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询APP标签列表
     */
    @Override
    public List<AppLabelVo> queryList(AppLabelBo bo) {
        LambdaQueryWrapper<AppLabel> lqw = buildQueryWrapper(bo);
        lqw.orderByDesc(AppLabel::getCreateTime);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AppLabel> buildQueryWrapper(AppLabelBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AppLabel> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getName()), AppLabel::getName, bo.getName());
        lqw.eq(bo.getTypeId() != null, AppLabel::getTypeId, bo.getTypeId());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), AppLabel::getStatus, bo.getStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getHotStatus()), AppLabel::getHotStatus, bo.getHotStatus());
        return lqw;
    }

    /**
     * 新增APP标签
     */
    @Override
    public Boolean insertByBo(AppLabelBo bo) {
        AppLabel add = BeanUtil.toBean(bo, AppLabel.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createLabel(AppLabelBo bo) {
        //添加标签成功，用户加入标签
        if (insertByBo(bo)) {
            AppLabelLike like = new AppLabelLike();
            like.setUserId(bo.getUserId());
            like.setLabelId(bo.getId());
            like.setTypeId(bo.getTypeId());
            like.setLikeStatus(LikeStatus.LIKE.getCode());
            return appLabelLikeMapper.insert(like) > 0;
        }
        return false;
    }

    /**
     * 修改APP标签
     */
    @Override
    public Boolean updateByBo(AppLabelBo bo) {
        AppLabel update = BeanUtil.toBean(bo, AppLabel.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AppLabel entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除APP标签
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public List<AppLabelVo> queryListType(AppLabelBo bo, Long userId) {
        //如果是推荐获取热门的标签
        if (bo.getTypeId().equals(1L)) {
            bo.setHotStatus(Constants.SUCCESS);
            bo.setTypeId(null);
        }
        bo.setStatus(Constants.SUCCESS);
        List<AppLabelVo> list = queryList(bo);
        //获取用户喜欢的标签
        AppLabelLikeBo likeBo = new AppLabelLikeBo();
        likeBo.setUserId(userId);
        List<AppLabelLikeVo> appLabelLikeVos = appLabelLikeService.queryList(likeBo);
        ArrayList<Long> ids = new ArrayList<>();
        appLabelLikeVos.forEach(e -> ids.add(e.getLabelId()));
        list.forEach(e -> e.setCheck(ids.contains(e.getId())));
        return list;
    }

}
