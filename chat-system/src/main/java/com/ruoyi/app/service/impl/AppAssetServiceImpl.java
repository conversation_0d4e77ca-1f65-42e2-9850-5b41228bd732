package com.ruoyi.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.app.domain.AppAsset;
import com.ruoyi.app.domain.bo.AppAssetBo;
import com.ruoyi.app.domain.vo.AppAssetVo;
import com.ruoyi.app.domain.vo.AppUserVo;
import com.ruoyi.app.mapper.AppAssetMapper;
import com.ruoyi.app.service.IAppAssetService;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.MessageUtils;
import com.ruoyi.common.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.beans.Transient;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * APP用户资产信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-12-09
 */
@RequiredArgsConstructor
@Service
public class AppAssetServiceImpl implements IAppAssetService {

    private final AppAssetMapper baseMapper;

    /**
     * 查询APP用户资产信息
     */
    @Override
    public AppAssetVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询APP用户资产信息
     */
    @Override
    public AppAssetVo queryByUid(Long uid) {
        QueryWrapper<AppAsset> asset = new QueryWrapper<>();
        asset.lambda().eq(AppAsset::getUserId, uid);
        return baseMapper.selectVoOne(asset);
    }

    /**
     * 查询APP用户资产信息列表
     */
    @Override
    public TableDataInfo<AppAssetVo> queryPageList(AppAssetBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AppAsset> lqw = buildQueryWrapper(bo);
        Page<AppAssetVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询APP用户资产信息列表
     */
    @Override
    public List<AppAssetVo> queryList(AppAssetBo bo) {
        LambdaQueryWrapper<AppAsset> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AppAsset> buildQueryWrapper(AppAssetBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AppAsset> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, AppAsset::getUserId, bo.getUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), AppAsset::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增APP用户资产信息
     */
    @Override
    public Boolean insertByBo(AppAssetBo bo) {
        AppAsset add = BeanUtil.toBean(bo, AppAsset.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public Boolean insert(Long userId) {
        AppAssetBo assetBo = new AppAssetBo();
        assetBo.setUserId(userId);
        assetBo.setBalance(BigDecimal.ZERO);
        assetBo.setIntegral(BigDecimal.ZERO);
        assetBo.setStatus(Constants.SUCCESS);
        return insertByBo(assetBo);
    }

    /**
     * 修改APP用户资产信息
     */
    @Override
    public Boolean updateByBo(AppAssetBo bo) {
        AppAsset update = BeanUtil.toBean(bo, AppAsset.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AppAsset entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除APP用户资产信息
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addBalance(Long uid, BigDecimal amount) {
        AppAssetVo appAssetVo = queryByUid(uid);
        LambdaUpdateWrapper<AppAsset> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.set(AppAsset::getBalance, appAssetVo.getBalance().add(amount.abs())).eq(AppAsset::getUserId, uid);
        return baseMapper.update(null, lambdaUpdateWrapper) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addIntegral(Long uid, BigDecimal amount) {
        AppAssetVo appAssetVo = queryByUid(uid);
        LambdaUpdateWrapper<AppAsset> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.set(AppAsset::getIntegral, appAssetVo.getIntegral().add(amount.abs())).eq(AppAsset::getUserId, uid);
        return baseMapper.update(null, lambdaUpdateWrapper) > 0;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean subtractIntegral(Long uid, BigDecimal amount) {
        AppAssetVo appAssetVo = queryByUid(uid);
        BigDecimal integral = BigDecimal.ZERO;
        BigDecimal amountAbs = amount.abs();
        if (appAssetVo.getIntegral().compareTo(amountAbs) > 0) {
            integral = appAssetVo.getIntegral().subtract(amountAbs);
        }
        LambdaUpdateWrapper<AppAsset> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.set(AppAsset::getIntegral, integral).eq(AppAsset::getUserId, uid);
        return baseMapper.update(null, lambdaUpdateWrapper) > 0;
    }

    @Override
    public Boolean subtractBalance(Long uid, BigDecimal amount) {
        AppAssetVo appAssetVo = queryByUid(uid);
        if (appAssetVo.getBalance().compareTo(amount) < 0) {
            throw new ServiceException(MessageUtils.message("user.error.balance"));
        }
        LambdaUpdateWrapper<AppAsset> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.set(AppAsset::getBalance, appAssetVo.getBalance().subtract(amount.abs())).eq(AppAsset::getUserId, uid);
        return baseMapper.update(null, lambdaUpdateWrapper) > 0;
    }
}
