package com.ruoyi.app.service;

import com.ruoyi.app.domain.AppUserRemark;
import com.ruoyi.app.domain.vo.AppUserRemarkVo;
import com.ruoyi.app.domain.bo.AppUserRemarkBo;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * APP用户备注Service接口
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
public interface IAppUserRemarkService {

    /**
     * 查询APP用户备注
     */
    AppUserRemarkVo queryById(Long id);


    AppUserRemarkVo queryUser(Long id, Long friendId);

    /**
     * 查询APP用户备注列表
     */
    TableDataInfo<AppUserRemarkVo> queryPageList(AppUserRemarkBo bo, PageQuery pageQuery);

    /**
     * 查询APP用户备注列表
     */
    List<AppUserRemarkVo> queryList(AppUserRemarkBo bo);

    /**
     * 新增APP用户备注
     */
    Boolean insertByBo(AppUserRemarkBo bo);

    /**
     * 修改APP用户备注
     */
    Boolean updateByBo(AppUserRemarkBo bo);

    /**
     * 修改APP用户备注
     */
    Boolean updateByVo(AppUserRemarkVo vo);

    /**
     * 校验并批量删除APP用户备注信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
