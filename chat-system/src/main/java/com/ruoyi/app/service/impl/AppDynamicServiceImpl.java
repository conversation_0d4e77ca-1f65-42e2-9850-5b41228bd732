package com.ruoyi.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.app.domain.AppDynamic;
import com.ruoyi.app.domain.AppDynamicVideo;
import com.ruoyi.app.domain.AppFriend;
import com.ruoyi.app.domain.bo.*;
import com.ruoyi.app.domain.vo.*;
import com.ruoyi.app.mapper.AppDynamicMapper;
import com.ruoyi.app.mapper.AppDynamicTypeMapper;
import com.ruoyi.app.mapper.AppDynamicVideoMapper;
import com.ruoyi.app.mapper.AppFriendMapper;
import com.ruoyi.app.service.*;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.CommentType;
import com.ruoyi.common.enums.DynamicType;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Array;
import java.util.*;
import java.util.stream.Collectors;

/**
 * APP用户动态Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-12-22
 */
@RequiredArgsConstructor
@Service
public class AppDynamicServiceImpl implements IAppDynamicService {

    private final AppDynamicMapper baseMapper;
    private final IAppUserService appUserService;
    private final AppFriendMapper appFriendMapper;
    private final AppDynamicVideoMapper appDynamicVideoMapper;
    private final AppDynamicTypeMapper appDynamicTypeMapper;
    private final IAppDynamicLikeService appDynamicLikeService;
    private final IAppDynamicEmojiService appDynamicEmojiService;
    private final IAppDynamicVideoService appDynamicVideoService;
    private final IAppFriendRelationService appFriendRelationService;


    /**
     * 查询APP用户动态
     */
    @Override
    public AppDynamicVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }


    @Override
    public AppDynamicVo selectLimitOne(LambdaQueryWrapper<AppDynamic> queryWrapper) {
        return baseMapper.selectVoOne(queryWrapper);
    }

    /**
     * 查询APP用户动态列表
     */
    @Override
    public TableDataInfo<AppDynamicVo> queryPageList(AppDynamicBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AppDynamic> lqw = buildQueryWrapper(bo);
        lqw.orderByDesc(AppDynamic::getCreateTime);
        Page<AppDynamicVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询APP用户动态列表
     */
    @Override
    public TableDataInfo<AppDynamicVo> queryPageList(Long id, String type, PageQuery pageQuery) {
        LambdaQueryWrapper<AppDynamic> wrapper = new LambdaQueryWrapper<>();
        if (!StringUtils.equals(type, Constants.SUCCESS)) {
            wrapper.orderByDesc(AppDynamic::getCreateTime);
        }
        Page<AppDynamicVo> result = baseMapper.selectVoPage(pageQuery.build(), wrapper);
        return TableDataInfo.build(result);
    }

    /**
     * 查询APP用户动态列表
     */
    @Override
    public List<AppDynamicVo> queryList(AppDynamicBo bo) {
        LambdaQueryWrapper<AppDynamic> lqw = buildQueryWrapper(bo);
        lqw.orderByDesc(AppDynamic::getCreateTime);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AppDynamic> buildQueryWrapper(AppDynamicBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AppDynamic> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, AppDynamic::getUserId, bo.getUserId());
        lqw.like(StringUtils.isNotBlank(bo.getContext()), AppDynamic::getContext, bo.getContext());
        lqw.eq(StringUtils.isNotBlank(bo.getImages()), AppDynamic::getImages, bo.getImages());
        lqw.eq(StringUtils.isNotBlank(bo.getAddress()), AppDynamic::getAddress, bo.getAddress());
        lqw.eq(bo.getLongitude() != null, AppDynamic::getLongitude, bo.getLongitude());
        lqw.eq(bo.getLatitude() != null, AppDynamic::getLatitude, bo.getLatitude());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), AppDynamic::getStatus, bo.getStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getScope()), AppDynamic::getScope, bo.getScope());
        return lqw;
    }

    /**
     * 新增APP用户动态
     */
    @Override
    public Boolean insertByBo(AppDynamicBo bo) {
        AppDynamic add = BeanUtil.toBean(bo, AppDynamic.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addDynamic(AppDynamicBo bo) {
        if (insertByBo(bo)) {
            if (bo.getType().equals(DynamicType.VIDEO.getCode())) {
                AppDynamicVideo video = new AppDynamicVideo();
                video.setUrl(bo.getUrl());
                video.setImage(bo.getImages());
                video.setDynamicId(bo.getId());
                appDynamicVideoMapper.insert(video);
            } else {
                if (ObjectUtils.isNotEmpty(bo.getImagesList())) {
                    for (int i = 0; i < bo.getImagesList().size(); i++) {
                        String e = bo.getImagesList().get(i);
                        AppDynamicVideo video = new AppDynamicVideo();
                        video.setImage(e);
                        video.setDynamicId(bo.getId());
                        video.setSmallImage(e);
                        appDynamicVideoMapper.insert(video);
                    }
                }
            }
            return true;
        }
        return false;
    }

    /**
     * 修改APP用户动态
     */
    @Override
    public Boolean updateByBo(AppDynamicBo bo) {
        AppDynamic update = BeanUtil.toBean(bo, AppDynamic.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AppDynamic entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除APP用户动态
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public Boolean delete(Long id) {
        return baseMapper.deleteById(id) > 0;
    }

    @Override
    public TableDataInfo<AppDynamicVo> queryDate(TableDataInfo<AppDynamicVo> page, Long id) {
        for (AppDynamicVo e : page.getData()) {
            info(e, id);
        }
        return page;
    }

    @Override
    public List<AppDynamicVo> queryDate(List<AppDynamicVo> list, Long id) {
        for (AppDynamicVo e : list) {
            info(e, id);
        }
        return list;
    }

    @Override
    public List<AppDynamicVo> simplifyDate(List<AppDynamicVo> list, Long id) {
        for (AppDynamicVo e : list) {
            simplifyInfo(e, id);
        }
        return list;
    }


    @Override
    public AppDynamicVo findOne(Long id, Long userId) {
        AppDynamicVo dynamicVo = queryById(id);
        if (ObjectUtils.isEmpty(dynamicVo)) {
            return null;
        }
        return info(dynamicVo, userId);
    }

    /**
     * 处理动态信息
     *
     * @param e  动态信息
     * @param id 用户ID
     * @return
     */
    public AppDynamicVo info(AppDynamicVo e, Long id) {
        List<AppDynamicLikeVo> likeVos = appDynamicLikeService.queryByLike(e.getId(), CommentType.DYNAMIC.getCode());
        //点赞人列表点赞数量
        if (!likeVos.isEmpty()) {
            likeVos.forEach(i -> i.setUserVo(appUserService.userFilterVo(i.getUserId())));
        }
        e.setLikeVos(likeVos);
        e.setLikeVosNumber(likeVos.size());
        //当前用户点赞状态
        e.setUserLike(appDynamicLikeService.queryByUser(e.getId(), id, CommentType.DYNAMIC.getCode()));
        //当前用户收藏状态
        List<AppFriendRelationVo> userCollect = appFriendRelationService.queryList(id, e.getId(), Constants.SUCCESS);
        e.setCollectLike(!userCollect.isEmpty());
        //发布动态用户信息
        e.setUserVo(appUserService.filterateUserVo(e.getUserId()));
        //动态图片和视频信息
        AppDynamicVideoBo appDynamicVideoBo = new AppDynamicVideoBo();
        appDynamicVideoBo.setDynamicId(e.getId());
        List<AppDynamicVideoVo> videoVos = appDynamicVideoService.queryList(appDynamicVideoBo);
        e.setVideoVos(videoVos);
        String createTimeFormat = DateUtils.getDatePoorSingle(e.getCreateTime());
        if (createTimeFormat == null) {
            createTimeFormat = Constants.JUST;
        }
        e.setCreateTimeFormat(createTimeFormat);
        e.setEmojiVos(getEmojiVo(likeVos));
        return e;
    }

    /**
     * 处理动态信息
     *
     * @param e  动态信息
     * @param id 用户ID
     * @return
     */
    public AppDynamicVo simplifyInfo(AppDynamicVo e, Long id) {
        List<AppDynamicLikeVo> likeVos = appDynamicLikeService.queryByLike(e.getId(), CommentType.DYNAMIC.getCode());
        //点赞人列表点赞数量
        if (!likeVos.isEmpty()) {
            likeVos.forEach(i -> i.setUserVo(appUserService.userFilterVo(i.getUserId())));
        }
        e.setLikeVos(likeVos);
        e.setLikeVosNumber(likeVos.size());
        //当前用户点赞状态
        e.setUserLike(appDynamicLikeService.queryByUser(e.getId(), id, CommentType.DYNAMIC.getCode()));
        //当前用户收藏状态
        List<AppFriendRelationVo> userCollect = appFriendRelationService.queryList(id, e.getId(), Constants.SUCCESS);
        e.setCollectLike(!userCollect.isEmpty());
        //动态图片和视频信息
        AppDynamicVideoBo appDynamicVideoBo = new AppDynamicVideoBo();
        appDynamicVideoBo.setDynamicId(e.getId());
        List<AppDynamicVideoVo> videoVos = appDynamicVideoService.queryList(appDynamicVideoBo);
        e.setVideoVos(videoVos);
        String createTimeFormat = DateUtils.getDatePoorSingle(e.getCreateTime());
        if (createTimeFormat == null) {
            createTimeFormat = Constants.JUST;
        }
        e.setCreateTimeFormat(createTimeFormat);
        e.setEmojiVos(getEmojiVo(likeVos));
        return e;
    }


    @Override
    public AppDynamicVo findOne(Long id) {
        AppDynamicVo e = queryById(id);
        List<AppDynamicLikeVo> likeVos = appDynamicLikeService.queryByLike(e.getId(), CommentType.DYNAMIC.getCode());
        //点赞人列表点赞数量
        if (!likeVos.isEmpty()) {
            likeVos.forEach(i -> i.setUserVo(appUserService.userFilterVo(i.getUserId())));
        }
        e.setLikeVos(likeVos);
        e.setLikeVosNumber(likeVos.size());
        //发布动态用户信息
        e.setUserVo(appUserService.filterateUserVo(e.getUserId()));
        //动态图片和视频信息
        AppDynamicVideoBo appDynamicVideoBo = new AppDynamicVideoBo();
        appDynamicVideoBo.setDynamicId(e.getId());
        List<AppDynamicVideoVo> videoVos = appDynamicVideoService.queryList(appDynamicVideoBo);
        e.setVideoVos(videoVos);
        String createTimeFormat = DateUtils.getDatePoorSingle(e.getCreateTime());
        if (createTimeFormat == null) {
            createTimeFormat = Constants.JUST;
        }
        e.setCreateTimeFormat(createTimeFormat);
        e.setEmojiVos(getEmojiVo(likeVos));
        return e;
    }

    /**
     * 获取动态的表情信息
     *
     * @param likeVos 动态的点赞信息
     * @return
     */
    private List<AppDynamicEmojiVo> getEmojiVo(List<AppDynamicLikeVo> likeVos) {
        AppDynamicEmojiBo emojiBo = new AppDynamicEmojiBo();
        emojiBo.setStatus(Constants.SUCCESS);
        List<AppDynamicEmojiVo> emojiVos = appDynamicEmojiService.queryList(emojiBo);
        Map<Long, List<AppDynamicLikeVo>> collect = likeVos.stream().collect(Collectors.groupingBy(AppDynamicLikeVo::getEmojiId));
        emojiVos.forEach(e -> {
            List<AppDynamicLikeVo> dynamicLikeVos = collect.get(e.getId());
            if (ObjectUtils.isNotEmpty(dynamicLikeVos)) {
                e.setStarNumber(dynamicLikeVos.size());
                e.setUserVos(dynamicLikeVos.stream().map(AppDynamicLikeVo::getUserVo).collect(Collectors.toList()));
            } else {
                e.setStarNumber(0);
                e.setUserVos(new ArrayList<>());
            }
        });
        return emojiVos;
    }
}
