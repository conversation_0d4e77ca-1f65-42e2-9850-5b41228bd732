package com.ruoyi.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ruoyi.app.domain.bo.AppLabelTypeBo;
import com.ruoyi.app.domain.vo.AppLabelTypeVo;
import com.ruoyi.app.domain.AppLabelType;
import com.ruoyi.app.mapper.AppLabelTypeMapper;
import com.ruoyi.app.service.IAppLabelTypeService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * APP标签类型Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
@RequiredArgsConstructor
@Service
public class AppLabelTypeServiceImpl implements IAppLabelTypeService {

    private final AppLabelTypeMapper baseMapper;

    /**
     * 查询APP标签类型
     */
    @Override
    public AppLabelTypeVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询APP标签类型列表
     */
    @Override
    public TableDataInfo<AppLabelTypeVo> queryPageList(AppLabelTypeBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AppLabelType> lqw = buildQueryWrapper(bo);
        Page<AppLabelTypeVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询APP标签类型列表
     */
    @Override
    public List<AppLabelTypeVo> queryList(AppLabelTypeBo bo) {
        LambdaQueryWrapper<AppLabelType> lqw = buildQueryWrapper(bo);
        lqw.orderByAsc(AppLabelType::getId);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AppLabelType> buildQueryWrapper(AppLabelTypeBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AppLabelType> lqw = Wrappers.lambdaQuery();
        lqw.like(bo.getName() != null, AppLabelType::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), AppLabelType::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增APP标签类型
     */
    @Override
    public Boolean insertByBo(AppLabelTypeBo bo) {
        AppLabelType add = BeanUtil.toBean(bo, AppLabelType.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改APP标签类型
     */
    @Override
    public Boolean updateByBo(AppLabelTypeBo bo) {
        AppLabelType update = BeanUtil.toBean(bo, AppLabelType.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AppLabelType entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除APP标签类型
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
