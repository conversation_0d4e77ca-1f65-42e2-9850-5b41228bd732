package com.ruoyi.app.service;

import com.ruoyi.app.domain.AppFriend;
import com.ruoyi.app.domain.bo.AppAddressBookBo;
import com.ruoyi.app.domain.bo.AppFriendBo;
import com.ruoyi.app.domain.vo.AppAddressBookVo;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * APP用户的通讯录Service接口
 *
 * <AUTHOR>
 * @date 2022-12-26
 */
public interface IAppAddressBookService {

    /**
     * 查询APP用户的通讯录
     */
    AppAddressBookVo queryById(Long id);

    /**
     * 查询用户通讯录好友
     *
     * @param userId 用户ID
     * @param bookId 通讯录好友ID
     * @return
     */
    AppAddressBookVo queryBook(Long userId, Long bookId);

    /**
     * 查询APP用户的通讯录列表
     */
    TableDataInfo<AppAddressBookVo> queryPageList(AppAddressBookBo bo, PageQuery pageQuery);

    /**
     * 查询APP用户的通讯录列表
     */
    List<AppAddressBookVo> queryList(AppAddressBookBo bo);

    /**
     * 新增APP用户的通讯录
     */
    Boolean insertByBo(AppAddressBookBo bo);

    /**
     * 建立通讯录用户关系
     */
    Boolean createFriend(Long userId, Long friendId);


    /**
     * 解除通讯录用户关系
     */
    Boolean cancel(Long userId, Long friendId);

    /**
     * 修改APP用户的通讯录
     */
    Boolean updateByBo(AppAddressBookBo bo);

    /**
     * 修改通讯录的备注信息
     *
     * @param id     记录ID
     * @param userId 用户ID，防止修改到其他用户的
     * @param remark 备注名称
     * @return
     */
    Boolean updateByUserIdAndRemark(Long id, Long userId, String remark);

    /**
     * 校验并批量删除APP用户的通讯录信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
