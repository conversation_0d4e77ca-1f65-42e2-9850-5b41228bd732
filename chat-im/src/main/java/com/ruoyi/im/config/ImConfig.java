package com.ruoyi.im.config;


import com.ruoyi.im.config.properties.ImProperties;
import com.ruoyi.im.core.EasemobImTemplate;
import com.ruoyi.im.core.EasemobTemplate;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * IM配置类
 *
 * <AUTHOR>
 */

@Configuration
public class ImConfig {

    @Configuration
    @ConditionalOnClass(com.easemob.im.server.EMService.class)
    static class EasemobImConfig {
        @Bean
        public EasemobTemplate easemobImTemplate(ImProperties imProperties) {
            return new EasemobImTemplate(imProperties);
        }
    }
}
