package com.ruoyi.im.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * IM环信 配置属性
 *
 * <AUTHOR>
 * @version 4.2.0
 */
@Data
@Component
@ConfigurationProperties(prefix = "im")
public class ImProperties {


    /**
     * 环信 服务器地址
     */
    private String api;

    /**
     * 环信 APPKEY
     */
    private String appkey;

    /**
     * 环信 Client ID
     */
    private String clientId;

    /**
     * 环信 ClientSecret
     */
    private String clientSecret;


}
