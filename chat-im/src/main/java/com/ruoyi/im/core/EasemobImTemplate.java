package com.ruoyi.im.core;

import com.easemob.im.server.EMProperties;
import com.easemob.im.server.EMService;
import com.easemob.im.server.api.group.settings.UpdateGroupRequest;
import com.easemob.im.server.api.token.Token;
import com.easemob.im.server.model.*;
import com.easemob.im.shaded.reactor.core.publisher.Mono;
import com.ruoyi.im.config.properties.ImProperties;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 */
@Slf4j
public class EasemobImTemplate implements EasemobTemplate {

    private ImProperties imProperties;
    private EMService emService;

    public EasemobImTemplate(ImProperties imProperties) {
        this.imProperties = imProperties;
        EMProperties properties = EMProperties.builder()
            .setAppkey(imProperties.getAppkey())
            .setClientId(imProperties.getClientId())
            .setClientSecret(imProperties.getClientSecret())
            .build();
        this.emService = new EMService(properties);
    }


    @Override
    public EMUser create(String userName, String password) {
        return emService.user().create(userName, password).block();
    }

    @Override
    public Void setMetadataToUser(String username, Map<String, String> metadata) {
        return emService.metadata().setMetadataToUser(username, metadata).block();

    }

    @Override
    public EMMetadata getMetadataFromUser(String username) {
        return emService.metadata().getMetadataFromUser(username).block();
    }

    @Override
    public Boolean isUserOnline(String username) {
        return emService.user().isUserOnline(username).block();
    }

    @Override
    public List<EMUserStatus> isUsersOnline(List<String> username) {
        return emService.user().isUsersOnline(username).block();
    }

    @Override
    public String getUserToken(EMUser user, String password, Integer expire) {
        return emService.token().getUserToken(user, expire, null, password);
    }

    @Override
    public Void addFriend(String user, String contact) {
        return emService.contact().add(user, contact).block();
    }


    @Override
    public Void removeFriend(String user, String contact) {
        return emService.contact().remove(user, contact).block();
    }

    @Override
    public Token appToken() {
        return emService.token().getAppToken().block();
    }

    @Override
    public List<String> friendList(String userName) {
        return emService.contact().list(userName).collectList().block();
    }

    @Override
    public Void friendRemove(String userName, String contact) {
        return emService.contact().remove(userName, contact).block();
    }

    @Override
    public List<EMBlock> getUsersBlockedFromSendMsgToUser(String userName) {
        return emService.block().getUsersBlockedFromSendMsgToUser(userName).collectList().block();
    }

    @Override
    public Void blockUserSendMsgToUser(String fromUser, String toUser) {
        return emService.block().blockUserSendMsgToUser(fromUser, toUser).block();
    }

    @Override
    public Void unblockUserSendMsgToUser(String fromUser, String toUser) {
        return emService.block().unblockUserSendMsgToUser(fromUser, toUser).block();
    }

    @Override
    public String createPublicGroup(String user, String groupName, String description, List<String> members, Boolean needApproveToJoin) {
        return emService.group().createPublicGroup(user, groupName, description, members, 200, needApproveToJoin).block();
    }

    @Override
    public Void addGroupMembers(String groupId, List<String> members) {
        return emService.group().addGroupMembers(groupId, members).block();
    }

    @Override
    public List<String> istGroupsUserJoined(String username) {
        return emService.group().listGroupsUserJoined(username).collectList().block();
    }

    @Override
    public Void removeGroupMember(String groupId, String username) {
        return emService.group().removeGroupMember(groupId, username).block();
    }


    @Override
    public List<EMRemoveMember> removeGroupMembers(String groupId, List<String> usernames) {
        return emService.group().removeGroupMembers(groupId, usernames).block();
    }

    @Override
    public Void updateGroup(String groupId, Consumer<UpdateGroupRequest> customizer) {
        return emService.group().updateGroup(groupId, customizer).block();
    }

    @Override
    public Void addGroupAdmin(String groupId, String username) {
        return emService.group().addGroupAdmin(groupId, username).block();
    }

    @Override
    public Void removeGroupAdmin(String groupId, String username) {
        return emService.group().removeGroupAdmin(groupId, username).block();
    }

    @Override
    public Void updateGroupOwner(String groupId, String username) {
        return emService.group().updateGroupOwner(groupId, username).block();
    }

    @Override
    public Void blockUserSendMsgToGroup(String username, String groupId, Duration duration) {
        return emService.block().blockUserSendMsgToGroup(username, groupId, duration).block();
    }

    @Override
    public Void unblockUserSendMsgToGroup(String username, String groupId) {
        return emService.block().unblockUserSendMsgToGroup(username, groupId).block();
    }

    @Override
    public Void destroyGroup(String groupId) {
        return emService.group().destroyGroup("groupId").block();
    }

    @Override
    public EMSentMessageIds send(String from,
                                 String toType,
                                 Set<String> tos,
                                 EMMessage message,
                                 Set<EMKeyValue> extensions) {
        return emService.message().send(from, toType, tos, message, extensions).block();
    }

    @Override
    public Void updateUserNickname(String username, String nickname) {
        return emService.push().updateUserNickname(username, nickname).block();
    }

}
