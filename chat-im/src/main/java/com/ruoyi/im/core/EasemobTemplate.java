package com.ruoyi.im.core;

import com.easemob.im.server.api.group.settings.UpdateGroupRequest;
import com.easemob.im.server.api.token.Token;
import com.easemob.im.server.model.*;
import com.easemob.im.shaded.reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 */
public interface EasemobTemplate {


    /**
     * 环信注册用户
     *
     * @param userName 用户名
     * @param password 密码
     * @return
     */
    EMUser create(String userName, String password);

    /**
     * 设置用户属性
     *
     * @param username 用户属性
     * @param metadata 设置属性的MAP
     */
    Void setMetadataToUser(String username, Map<String, String> metadata);


    EMMetadata getMetadataFromUser(String username);

    /**
     * 获取用户在线状态
     *
     * @param username 用户名
     * @return
     */
    Boolean isUserOnline(String username);

    /**
     * 批量获取用户在线状态
     *
     * @param username 多个用户名
     * @return
     */
    List<EMUserStatus> isUsersOnline(List<String> username);

    /**
     * 获取用户的TOKEN
     *
     * @param user     用户信息
     * @param password 用户密码
     * @return
     */
    String getUserToken(EMUser user, String password, Integer expire);

    /**
     * 添加好友到通讯录
     *
     * @param user    用户名
     * @param contact 添加好友的用户名
     * @return
     */
    Void addFriend(String user, String contact);


    /**
     * 从通讯录删除好友
     *
     * @param user    用户名
     * @param contact 删除好友的用户名
     * @return
     */
    Void removeFriend(String user, String contact);

    /**
     * 获取环信用户的好友列表
     * * @param userName 用户名
     *
     * @return
     */
    List<String> friendList(String userName);

    /**
     * 删除好友联系人
     *
     * @param userName 所属用户名
     * @param contact  联系人的用户名
     * @return
     */
    Void friendRemove(String userName, String contact);

    /**
     * 获取用户的禁言列表
     *
     * @param userName 环信用户名
     * @return
     */
    List<EMBlock> getUsersBlockedFromSendMsgToUser(String userName);

    /**
     * 用户禁言，阻止向这个用户发消息。
     * 阻止 fromUser 给 toUser发送消息：
     *
     * @param fromUser 被阻止发送消息的用户名
     * @param toUser   接受消息的用户的用户名
     */
    Void blockUserSendMsgToUser(String fromUser, String toUser);

    /**
     * 解除用户禁言，恢复向这个用户发消息。
     * 阻止 fromUser 给 toUser发送消息：
     *
     * @param fromUser 被阻止发送消息的用户名
     * @param toUser   接受消息的用户的用户名
     */
    Void unblockUserSendMsgToUser(String fromUser, String toUser);

    /**
     * 获取环信app_token
     *
     * @return
     */
    Token appToken();

    /**
     * 创建公开群
     *
     * @param user              群主用户名
     * @param groupName         群名，最大长度为 128 字符
     * @param description       群介绍，最大长度为 512 字符
     * @param members           初始群成员的用户名列表
     * @param needApproveToJoin 新成员加入需要管理员审批
     * @return 群ID或者错误
     */
    String createPublicGroup(String user, String groupName, String description, List<String> members, Boolean needApproveToJoin);

    /**
     * 添加群成员(多个)。
     *
     * @param groupId 群组ID
     * @param members 要添加的用户的用户名列表
     */
    Void addGroupMembers(String groupId, List<String> members);

    /**
     * 获取用户加入的群组列表
     *
     * @param username 环信用户名
     * @return
     */
    List<String> istGroupsUserJoined(String username);

    /**
     * 移除群组成员
     *
     * @param groupId  群组ID
     * @param username 要移除的用户的用户名
     * @return
     */
    Void removeGroupMember(String groupId, String username);


    /**
     * 移除群组多个成员
     *
     * @param groupId   群组ID
     * @param usernames 要移除的用户的用户集合
     * @return
     */
    List<EMRemoveMember> removeGroupMembers(String groupId, List<String> usernames);

    /**
     * 修改群详情
     *
     * @param groupId    环信群组ID
     * @param customizer 请求定制器
     * @return
     */
    Void updateGroup(String groupId, Consumer<UpdateGroupRequest> customizer);

    /**
     * 升级群成员为管理员
     *
     * @param groupId  环信群组ID
     * @param username 被升级的群成员的用户名
     * @return
     */
    Void addGroupAdmin(String groupId, String username);

    /**
     * 降级群管理员为成员
     *
     * @param groupId  环信群组ID
     * @param username 降级群管理员用户名
     * @return
     */
    Void removeGroupAdmin(String groupId, String username);

    /**
     * 转让群主给群成员
     *
     * @param groupId  环信群组ID
     * @param username 群组成员用户名
     * @return
     */
    Void updateGroupOwner(String groupId, String username);

    /**
     * 群组禁言
     *
     * @param username 环信用户名
     * @param groupId  群组ID
     * @param duration 禁言时间，Null永久禁言
     * @return
     */
    Void blockUserSendMsgToGroup(String username, String groupId, Duration duration);

    /**
     * 解除群禁言。
     *
     * @param username 环信用户名
     * @param groupId  群组ID
     * @return
     */
    Void unblockUserSendMsgToGroup(String username, String groupId);

    /**
     * 注销群组
     *
     * @param groupId 群组ID
     * @return
     */
    Void destroyGroup(String groupId);

    /**
     * 发送消息
     *
     * @param from       发送者用户名
     * @param toType     目标类型，可以是 `users`, `chatgroups`, `chatrooms`
     * @param tos        目标id列表
     * @param message    要发送的消息，EMTextMessage文本消息，EMImageMessage图片消息，EMVoiceMessage语音消息， EMVideoMessage视频消息，EMFileMessage文件消息，EMCommandMessage透传消息，EMCustomMessage自定义类型消息， 各种类型消息需要自己构造
     * @param extensions 要发送的扩展，可以为空
     * @return
     */
    EMSentMessageIds send(String from, String toType, Set<String> tos, EMMessage message, Set<EMKeyValue> extensions);

    /**
     * 设置推送的昵称
     *
     * @param username 环信用户名
     * @param nickname 用户昵称
     * @return
     */
    Void updateUserNickname(String username, String nickname);
}
