package com.ruoyi.framework.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 支付回调 配置属性
 *
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "pay-url")
public class PayUrlProperties {

    /**
     * 支付宝VIP开通回调地址
     */
    private String alipayNotifyUrl;

    /**
     * 支付宝购买商品回调地址
     */
    private String alipayNotifyShopUrl;

    /**
     * 微信VIP开通回调地址
     */
    private String wechatNotifyUrl;

    /**
     * 微信购买商品回调地址
     */
    private String wechatNotifyShopUrl;

}
