package com.ruoyi.framework.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 声望音视频 配置属性
 *
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "agora")
public class AgoraProperties {

    /**
     * 声网控制台创建项目时生成的 App ID
     */
    private String appId;

    /**
     * 项目的 App 证书
     */
    private String appCertificate;

    /**
     * 频道名称，长度在 64 个字节以内
     */
    private String channelName;

    /**
     * 从生成到过期的时间长度，单位为秒
     */
    private Integer expirationTimeInSeconds;

}
